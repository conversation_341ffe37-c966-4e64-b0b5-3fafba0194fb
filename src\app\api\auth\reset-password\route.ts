import { NextRequest, NextResponse } from 'next/server';
import bcrypt from 'bcryptjs';
import { z } from 'zod';
import { User } from '@/models/User';
import connectDB from '@/lib/mongodb';
import { generateSecureToken } from '@/lib/encryption';
import { sendPasswordResetEmail } from '@/lib/email';

const resetRequestSchema = z.object({
  email: z.string().email('Invalid email address'),
});

const resetPasswordSchema = z.object({
  token: z.string().min(1, 'Token is required'),
  password: z.string().min(8, 'Password must be at least 8 characters'),
});

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { searchParams } = new URL(request.url);
    const action = searchParams.get('action');

    await connectDB();

    if (action === 'request') {
      // Request password reset
      const { email } = resetRequestSchema.parse(body);

      const user = await User.findOne({ email });
      if (!user) {
        // Don't reveal if user exists or not
        return NextResponse.json({
          message: 'If an account with that email exists, a password reset link has been sent.',
        });
      }

      // Generate reset token
      const passwordResetToken = generateSecureToken();
      const passwordResetExpires = new Date(Date.now() + 60 * 60 * 1000); // 1 hour

      user.passwordResetToken = passwordResetToken;
      user.passwordResetExpires = passwordResetExpires;
      await user.save();

      // Send reset email
      try {
        await sendPasswordResetEmail(email, passwordResetToken);
      } catch (emailError) {
        console.error('Failed to send password reset email:', emailError);
      }

      return NextResponse.json({
        message: 'If an account with that email exists, a password reset link has been sent.',
      });

    } else if (action === 'reset') {
      // Reset password with token
      const { token, password } = resetPasswordSchema.parse(body);

      const user = await User.findOne({
        passwordResetToken: token,
        passwordResetExpires: { $gt: new Date() },
      });

      if (!user) {
        return NextResponse.json(
          { error: 'Invalid or expired reset token' },
          { status: 400 }
        );
      }

      // Hash new password
      const passwordHash = await bcrypt.hash(password, 12);

      // Update user
      user.passwordHash = passwordHash;
      user.passwordResetToken = undefined;
      user.passwordResetExpires = undefined;
      await user.save();

      return NextResponse.json({
        message: 'Password reset successfully',
      });

    } else {
      return NextResponse.json(
        { error: 'Invalid action' },
        { status: 400 }
      );
    }

  } catch (error) {
    console.error('Password reset error:', error);
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Validation error', details: error.issues },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
