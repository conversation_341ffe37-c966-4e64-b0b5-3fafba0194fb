import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';
import { Share } from '@/models/Share';
import { File } from '@/models/File';
import { User } from '@/models/User';
import { S3Service } from '@/lib/s3';
import connectDB from '@/lib/mongodb';
import bcrypt from 'bcryptjs';

const accessShareSchema = z.object({
  shareId: z.string().min(1, 'Share ID is required'),
  password: z.string().optional(),
});

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { shareId, password } = accessShareSchema.parse(body);

    await connectDB();

    // Find active share
    const share = await Share.findActiveShare(shareId);

    if (!share) {
      return NextResponse.json(
        { error: 'Share not found or has expired' },
        { status: 404 }
      );
    }

    // Get the file details
    const file = await (File as any).findById(share.fileId);
    if (!file) {
      return NextResponse.json(
        { error: 'File not found' },
        { status: 404 }
      );
    }

    // Get the user details
    const user = await (User as any).findById(share.userId);
    if (!user) {
      return NextResponse.json(
        { error: 'User not found' },
        { status: 404 }
      );
    }

    // Check if share can be accessed
    const canAccess = share.isActive &&
      (!share.expiresAt || new Date() < share.expiresAt) &&
      (!share.maxAccessCount || share.accessCount < share.maxAccessCount);

    if (!canAccess) {
      return NextResponse.json(
        { error: 'Share has expired or reached maximum access limit' },
        { status: 403 }
      );
    }

    // Check password if required
    if (share.password) {
      if (!password) {
        return NextResponse.json(
          { error: 'Password required', requiresPassword: true },
          { status: 401 }
        );
      }

      const isPasswordValid = await bcrypt.compare(password, share.password);
      if (!isPasswordValid) {
        return NextResponse.json(
          { error: 'Invalid password' },
          { status: 401 }
        );
      }
    }

    // Increment access count
    share.accessCount += 1;
    await share.save();

    return NextResponse.json({
      share: {
        id: share._id,
        shareId: share.shareId,
        permission: share.permission,
        allowDownload: share.allowDownload,
        accessCount: share.accessCount,
        maxAccessCount: share.maxAccessCount,
        expiresAt: share.expiresAt,
      },
      file: {
        id: file._id,
        name: file.name,
        originalName: file.originalName,
        size: file.size,
        mimeType: file.mimeType,
        downloadCount: file.downloadCount,
      },
      owner: {
        id: user._id,
        name: user.name,
      },
    });

  } catch (error) {
    console.error('Share access error:', error);
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Validation error', details: error.issues },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: 'Failed to access share' },
      { status: 500 }
    );
  }
}

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const shareId = searchParams.get('shareId');

    if (!shareId) {
      return NextResponse.json(
        { error: 'Share ID is required' },
        { status: 400 }
      );
    }

    await connectDB();

    // Find active share (without incrementing access count)
    const share = await Share.findActiveShare(shareId);

    if (!share) {
      return NextResponse.json(
        { error: 'Share not found or has expired' },
        { status: 404 }
      );
    }

    // Get the file details
    const file = await (File as any).findById(share.fileId);
    if (!file) {
      return NextResponse.json(
        { error: 'File not found' },
        { status: 404 }
      );
    }

    // Get the user details
    const user = await (User as any).findById(share.userId);
    if (!user) {
      return NextResponse.json(
        { error: 'User not found' },
        { status: 404 }
      );
    }

    // Check if share can be accessed
    const canAccess = share.isActive &&
      (!share.expiresAt || new Date() < share.expiresAt) &&
      (!share.maxAccessCount || share.accessCount < share.maxAccessCount);

    if (!canAccess) {
      return NextResponse.json(
        { error: 'Share has expired or reached maximum access limit' },
        { status: 403 }
      );
    }

    // Return basic share info (for password check, etc.)
    return NextResponse.json({
      shareId: share.shareId,
      requiresPassword: !!share.password,
      expiresAt: share.expiresAt,
      accessCount: share.accessCount,
      maxAccessCount: share.maxAccessCount,
      file: {
        name: file.name,
        size: file.size,
        mimeType: file.mimeType,
      },
      owner: {
        name: user.name,
      },
    });

  } catch (error) {
    console.error('Share info fetch error:', error);
    return NextResponse.json(
      { error: 'Failed to fetch share info' },
      { status: 500 }
    );
  }
}
