'use client';

import React from 'react';
import Link from 'next/link';
import { useSession, signOut } from 'next-auth/react';
import { MagnifyingGlassIcon, BellIcon, UserCircleIcon, XMarkIcon } from '@heroicons/react/24/outline';
import { Input } from '@/components/ui/Input';
import { ThemeToggle } from '@/components/ui/ThemeToggle';
import { cn } from '@/lib/utils';
import { toast } from 'react-hot-toast';


interface TopbarProps {
  onSearch?: (query: string) => void;
  className?: string;
}

// Check if user is admin (client-side version)
function isAdminUser(email: string): boolean {
  // Admin emails are hardcoded for client-side check
  const adminEmails = ['<EMAIL>', '<EMAIL>'];
  return adminEmails.includes(email);
}

export const Topbar: React.FC<TopbarProps> = ({ onSearch, className }) => {
  const { data: session } = useSession();
  const [searchQuery, setSearchQuery] = React.useState('');
  const [showProfileMenu, setShowProfileMenu] = React.useState(false);
  const profileMenuRef = React.useRef<HTMLDivElement>(null);
  const searchTimeoutRef = React.useRef<NodeJS.Timeout | null>(null);

  // Check if current user is admin
  const isAdmin = session?.user?.email ? isAdminUser(session.user.email) : false;

  React.useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (profileMenuRef.current && !profileMenuRef.current.contains(event.target as Node)) {
        setShowProfileMenu(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  // Cleanup search timeout on unmount
  React.useEffect(() => {
    return () => {
      if (searchTimeoutRef.current) {
        clearTimeout(searchTimeoutRef.current);
      }
    };
  }, []);

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    onSearch?.(searchQuery);
  };

  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const query = e.target.value;
    setSearchQuery(query);

    // Clear previous timeout
    if (searchTimeoutRef.current) {
      clearTimeout(searchTimeoutRef.current);
    }

    // Debounce search to avoid too many API calls
    searchTimeoutRef.current = setTimeout(() => {
      onSearch?.(query);
    }, 300); // 300ms delay
  };

  const handleSignOut = async () => {
    await signOut({ callbackUrl: '/' });
  };

  const handleNotificationClick = () => {
    toast('No new notifications', {
      icon: '🔔',
      duration: 2000,
    });
  };

  const handleClearSearch = () => {
    setSearchQuery('');
    // Clear any pending search timeout
    if (searchTimeoutRef.current) {
      clearTimeout(searchTimeoutRef.current);
    }
    // Immediately trigger search with empty query
    onSearch?.('');
  };

  return (
    <header className={cn('bg-background border-b border-divider', className)}>
      <div className="flex items-center justify-between px-6 py-4">
        {/* Search */}
        <div className="flex-1 max-w-lg">
          <form onSubmit={handleSearch}>
            <Input
              type="text"
              placeholder="Search files and folders..."
              value={searchQuery}
              onChange={handleSearchChange}
              leftIcon={<MagnifyingGlassIcon className="h-4 w-4" />}
              rightIcon={
                searchQuery && (
                  <button
                    type="button"
                    onClick={handleClearSearch}
                    className="text-text-secondary hover:text-text-primary transition-colors"
                  >
                    <XMarkIcon className="h-4 w-4" />
                  </button>
                )
              }
              className="w-full"
            />
          </form>
        </div>

        {/* Right side */}
        <div className="flex items-center space-x-4">
          {/* Theme Toggle */}
          <ThemeToggle />

          {/* Notifications */}
          <button
            onClick={handleNotificationClick}
            className="p-2 h-auto relative text-text-primary hover:bg-secondary rounded-lg transition-colors"
          >
            <BellIcon className="h-5 w-5" />
            {/* Notification badge */}
            <span className="absolute -top-1 -right-1 h-3 w-3 bg-error rounded-full"></span>
          </button>

          {/* Profile Menu */}
          <div className="relative" ref={profileMenuRef}>
            <button
              onClick={() => setShowProfileMenu(!showProfileMenu)}
              className="flex items-center space-x-2 p-2 rounded-lg hover:bg-secondary transition-colors"
            >
              {session?.user?.image ? (
                <img
                  src={session.user.image}
                  alt={session.user.name || 'User'}
                  className="h-8 w-8 rounded-full"
                />
              ) : (
                <UserCircleIcon className="h-8 w-8 text-text-secondary" />
              )}
              <div className="hidden md:block text-left">
                <p className="text-sm font-medium text-text-primary">
                  {session?.user?.name || 'User'}
                </p>
                <p className="text-xs text-text-secondary">
                  {session?.user?.email}
                </p>
              </div>
            </button>

            {/* Profile Dropdown */}
            {showProfileMenu && (
              <div className="absolute right-0 top-12 w-56 bg-background border border-divider rounded-lg shadow-lg z-50">
                <div className="py-2">
                  <div className="px-4 py-2 border-b border-divider">
                    <p className="text-sm font-medium text-text-primary">
                      {session?.user?.name}
                    </p>
                    <p className="text-xs text-text-secondary">
                      {session?.user?.email}
                    </p>
                    {!session?.user?.verified && (
                      <span className="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-warning/10 text-warning mt-1">
                        Unverified
                      </span>
                    )}
                  </div>
                  
                  <div className="py-1">
                    <Link
                      href="/settings"
                      className="block px-4 py-2 text-sm text-text-primary hover:bg-secondary"
                    >
                      Settings
                    </Link>
                    <Link
                      href="/settings/storage"
                      className="block px-4 py-2 text-sm text-text-primary hover:bg-secondary"
                    >
                      Storage Settings
                    </Link>
                    <Link
                      href="/dashboard/analytics"
                      className="block px-4 py-2 text-sm text-text-primary hover:bg-secondary"
                    >
                      Usage & Analytics
                    </Link>
                    {isAdmin && (
                      <Link
                        href="/admin-dashboard"
                        className="block px-4 py-2 text-sm text-accent hover:bg-accent/10"
                      >
                        Admin Dashboard
                      </Link>
                    )}
                  </div>
                  
                  <div className="border-t border-divider py-1">
                    <button
                      onClick={handleSignOut}
                      className="block w-full text-left px-4 py-2 text-sm text-error hover:bg-error/10"
                    >
                      Sign Out
                    </button>
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </header>
  );
};
