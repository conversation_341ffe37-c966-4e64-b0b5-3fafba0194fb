/**
 * Standalone Authentication Feature Tests
 * 
 * This test suite tests the authentication feature in complete isolation,
 * without dependencies on other features or external services.
 * Can be run independently for focused development and debugging.
 */

import { describe, it, expect, beforeEach, afterEach, jest } from '@jest/globals';
import { 
  DevTestEnvironment, 
  MockFactory, 
  FeatureTestUtils, 
  TestAssertions,
  mockDevUser,
  mockTestUser,
  mockAdminUser
} from '../utils/dev-test-helpers';

// Mock all external dependencies
jest.mock('@/lib/mongodb');
jest.mock('@/models/User');
jest.mock('@/lib/email');
jest.mock('bcryptjs');

describe('Authentication Feature - Standalone Tests', () => {
  let mockUser: any;
  let mockEmail: any;
  let mockBcrypt: any;

  beforeEach(() => {
    // Setup isolated test environment
    DevTestEnvironment.setup({
      developmentMode: true,
      authBypass: false, // Test real auth logic
      detailedErrors: true,
      debugLogging: true,
    });

    // Create fresh mocks for each test
    mockUser = MockFactory.createDatabaseMocks();
    mockEmail = MockFactory.createEmailMocks();
    mockBcrypt = {
      hash: jest.fn(),
      compare: jest.fn(),
    };

    // Apply mocks
    require('@/models/User').User = mockUser;
    require('@/lib/email').sendVerificationEmail = mockEmail.sendVerificationEmail;
    require('bcryptjs').hash = mockBcrypt.hash;
    require('bcryptjs').compare = mockBcrypt.compare;
  });

  afterEach(() => {
    DevTestEnvironment.cleanup();
    jest.clearAllMocks();
  });

  describe('User Registration', () => {
    it('should register a new user successfully', async () => {
      await FeatureTestUtils.testAuthFeature(async () => {
        // Setup mocks
        mockUser.findOne.mockResolvedValue(null); // User doesn't exist
        mockBcrypt.hash.mockResolvedValue('hashed-password');
        mockUser.create.mockResolvedValue({
          _id: 'new-user-id',
          name: 'New User',
          email: '<EMAIL>',
          verified: false,
        });

        // Import and test signup route
        const { POST } = await import('@/app/api/auth/signup/route');
        
        const request = MockFactory.createRequest('http://localhost:3000/api/auth/signup', {
          method: 'POST',
          body: {
            name: 'New User',
            email: '<EMAIL>',
            password: 'SecurePassword123!',
          },
        });

        const response = await POST(request);
        const data = await response.json();

        TestAssertions.expectSuccessResponse(response);
        expect(response.status).toBe(201);
        expect(data.message).toContain('User created successfully');
        expect(mockUser.create).toHaveBeenCalledWith(
          expect.objectContaining({
            name: 'New User',
            email: '<EMAIL>',
            verified: false,
          })
        );
        expect(mockEmail.sendVerificationEmail).toHaveBeenCalled();
      });
    });

    it('should reject registration with existing email', async () => {
      await FeatureTestUtils.testAuthFeature(async () => {
        // Setup mocks - user already exists
        mockUser.findOne.mockResolvedValue({
          _id: 'existing-user-id',
          email: '<EMAIL>',
        });

        const { POST } = await import('@/app/api/auth/signup/route');
        
        const request = MockFactory.createRequest('http://localhost:3000/api/auth/signup', {
          method: 'POST',
          body: {
            name: 'New User',
            email: '<EMAIL>',
            password: 'SecurePassword123!',
          },
        });

        const response = await POST(request);
        
        expect(response.status).toBe(409);
        expect(mockUser.create).not.toHaveBeenCalled();
      });
    });

    it('should validate input data', async () => {
      await FeatureTestUtils.testAuthFeature(async () => {
        const { POST } = await import('@/app/api/auth/signup/route');
        
        const request = MockFactory.createRequest('http://localhost:3000/api/auth/signup', {
          method: 'POST',
          body: {
            name: 'A', // Too short
            email: 'invalid-email', // Invalid format
            password: '123', // Too short
          },
        });

        const response = await POST(request);
        
        await TestAssertions.expectErrorWithContext(response, 'VALIDATION_ERROR');
        expect(mockUser.create).not.toHaveBeenCalled();
      });
    });
  });

  describe('User Authentication', () => {
    it('should authenticate valid credentials', async () => {
      await FeatureTestUtils.testAuthFeature(async () => {
        // Setup mocks
        mockUser.findOne.mockResolvedValue({
          _id: 'user-id',
          email: '<EMAIL>',
          passwordHash: 'hashed-password',
          verified: true,
          allowPlatformS3: false,
        });
        mockBcrypt.compare.mockResolvedValue(true);

        // Test authentication function
        const { authenticateUser } = await import('@/lib/auth-server');
        
        const result = await authenticateUser('<EMAIL>', 'correct-password');
        
        expect(result).toEqual({
          id: 'user-id',
          email: '<EMAIL>',
          name: undefined,
          image: undefined,
          verified: true,
          allowPlatformS3: false,
        });
        expect(mockBcrypt.compare).toHaveBeenCalledWith('correct-password', 'hashed-password');
      });
    });

    it('should reject invalid credentials', async () => {
      await FeatureTestUtils.testAuthFeature(async () => {
        // Setup mocks
        mockUser.findOne.mockResolvedValue({
          _id: 'user-id',
          email: '<EMAIL>',
          passwordHash: 'hashed-password',
        });
        mockBcrypt.compare.mockResolvedValue(false);

        const { authenticateUser } = await import('@/lib/auth-server');
        
        const result = await authenticateUser('<EMAIL>', 'wrong-password');
        
        expect(result).toBeNull();
      });
    });

    it('should reject non-existent user', async () => {
      await FeatureTestUtils.testAuthFeature(async () => {
        mockUser.findOne.mockResolvedValue(null);

        const { authenticateUser } = await import('@/lib/auth-server');
        
        const result = await authenticateUser('<EMAIL>', 'password');
        
        expect(result).toBeNull();
      });
    });
  });

  describe('Email Verification', () => {
    it('should verify email with valid token', async () => {
      await FeatureTestUtils.testAuthFeature(async () => {
        // Setup mocks
        const mockUserDoc = {
          verified: false,
          emailVerificationToken: 'valid-token',
          emailVerificationExpires: new Date(Date.now() + 3600000),
          save: jest.fn().mockResolvedValue(undefined),
        };
        mockUser.findOne.mockResolvedValue(mockUserDoc);

        const { GET } = await import('@/app/api/auth/verify-email/route');
        
        const request = MockFactory.createRequest(
          'http://localhost:3000/api/auth/verify-email?token=valid-token'
        );

        const response = await GET(request);
        const data = await response.json();

        TestAssertions.expectSuccessResponse(response);
        expect(data.message).toContain('Email verified successfully');
        expect(mockUserDoc.verified).toBe(true);
        expect(mockUserDoc.save).toHaveBeenCalled();
      });
    });

    it('should reject invalid verification token', async () => {
      await FeatureTestUtils.testAuthFeature(async () => {
        mockUser.findOne.mockResolvedValue(null);

        const { GET } = await import('@/app/api/auth/verify-email/route');
        
        const request = MockFactory.createRequest(
          'http://localhost:3000/api/auth/verify-email?token=invalid-token'
        );

        const response = await GET(request);
        
        TestAssertions.expectValidationError(response);
      });
    });

    it('should reject expired verification token', async () => {
      await FeatureTestUtils.testAuthFeature(async () => {
        mockUser.findOne.mockResolvedValue(null); // Expired tokens won't be found

        const { GET } = await import('@/app/api/auth/verify-email/route');
        
        const request = MockFactory.createRequest(
          'http://localhost:3000/api/auth/verify-email?token=expired-token'
        );

        const response = await GET(request);
        
        TestAssertions.expectValidationError(response);
      });
    });
  });

  describe('Development Mode Authentication', () => {
    it('should bypass authentication in development mode', async () => {
      // Setup development mode with auth bypass
      DevTestEnvironment.setup({
        developmentMode: true,
        authBypass: true,
        detailedErrors: true,
      });

      const { getSessionWithDevSupport } = await import('@/lib/auth-dev');
      
      const request = MockFactory.createRequest('http://localhost:3000/test');
      const session = await getSessionWithDevSupport(request);

      expect(session).toBeTruthy();
      expect(session?.user.email).toBe(mockDevUser.email);
      expect(session?.user.verified).toBe(true);
    });

    it('should grant admin access to dev user', async () => {
      DevTestEnvironment.setup({
        developmentMode: true,
        authBypass: true,
      });

      const { isAdminUserWithDevSupport } = await import('@/lib/auth-dev');
      
      const isAdmin = isAdminUserWithDevSupport(mockDevUser.email);
      
      expect(isAdmin).toBe(true);
    });

    it('should fall back to normal auth when bypass disabled', async () => {
      DevTestEnvironment.setup({
        developmentMode: true,
        authBypass: false,
      });

      const { getSessionWithDevSupport } = await import('@/lib/auth-dev');
      
      const request = MockFactory.createRequest('http://localhost:3000/test');
      const session = await getSessionWithDevSupport(request);

      expect(session).toBeNull(); // No JWT token provided
    });
  });

  describe('Error Handling', () => {
    it('should provide detailed error context in development', async () => {
      await FeatureTestUtils.testAuthFeature(async () => {
        // Force a database error
        mockUser.findOne.mockRejectedValue(new Error('Database connection failed'));

        const { authenticateUser } = await import('@/lib/auth-server');
        
        const result = await authenticateUser('<EMAIL>', 'password');
        
        expect(result).toBeNull(); // Should handle error gracefully
      });
    });

    it('should handle missing environment variables', async () => {
      // Remove required environment variable
      delete process.env.NEXTAUTH_SECRET;

      const { getSessionWithDevSupport } = await import('@/lib/auth-dev');
      
      const request = MockFactory.createRequest('http://localhost:3000/test');
      const session = await getSessionWithDevSupport(request);

      expect(session).toBeNull(); // Should handle missing config gracefully
    });
  });
});
