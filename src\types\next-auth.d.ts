import NextAuth from 'next-auth';

declare module 'next-auth' {
  interface Session {
    user: {
      id: string;
      email: string;
      name: string;
      image?: string;
      verified: boolean;
      allowPlatformS3: boolean;
    };
  }

  interface User {
    id: string;
    email: string;
    name: string;
    image?: string;
    verified: boolean;
    allowPlatformS3: boolean;
  }
}

declare module 'next-auth/jwt' {
  interface JWT {
    verified: boolean;
    allowPlatformS3: boolean;
  }
}
