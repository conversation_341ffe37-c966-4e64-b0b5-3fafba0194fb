'use client';

import React from 'react';
import { Modal } from '@/components/ui/Modal';
import { Button } from '@/components/ui/Button';
import { Input } from '@/components/ui/Input';
import { PencilIcon } from '@heroicons/react/24/outline';

interface RenameModalProps {
  isOpen: boolean;
  onClose: () => void;
  onConfirm: (newName: string) => void;
  currentName: string;
  itemType: 'file' | 'folder';
}

export const RenameModal: React.FC<RenameModalProps> = ({
  isOpen,
  onClose,
  onConfirm,
  currentName,
  itemType,
}) => {
  const [newName, setNewName] = React.useState(currentName);
  const [loading, setLoading] = React.useState(false);
  const [error, setError] = React.useState('');

  React.useEffect(() => {
    if (isOpen) {
      setNewName(currentName);
      setError('');
    }
  }, [isOpen, currentName]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!newName.trim()) {
      setError('Name cannot be empty');
      return;
    }

    if (newName.trim() === currentName) {
      onClose();
      return;
    }

    setLoading(true);
    setError('');
    
    try {
      await onConfirm(newName.trim());
    } catch (error) {
      setError('Failed to rename. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Escape') {
      onClose();
    }
  };

  return (
    <Modal
      isOpen={isOpen}
      onClose={onClose}
      title={`Rename ${itemType === 'file' ? 'File' : 'Folder'}`}
      size="sm"
    >
      <form onSubmit={handleSubmit} className="space-y-4">
        {/* Icon */}
        <div className="flex items-center justify-center">
          <div className="p-3 bg-accent/10 rounded-full">
            <PencilIcon className="h-6 w-6 text-accent" />
          </div>
        </div>

        {/* Input */}
        <div className="space-y-2">
          <label className="block text-sm font-medium text-text-primary">
            {itemType === 'file' ? 'File' : 'Folder'} Name
          </label>
          <Input
            type="text"
            value={newName}
            onChange={(e) => setNewName(e.target.value)}
            onKeyDown={handleKeyDown}
            placeholder={`Enter ${itemType} name`}
            error={error}
            autoFocus
          />
        </div>

        {/* Actions */}
        <div className="flex space-x-3 pt-4">
          <Button
            type="button"
            variant="secondary"
            onClick={onClose}
            className="flex-1"
            disabled={loading}
          >
            Cancel
          </Button>
          <Button
            type="submit"
            className="flex-1"
            loading={loading}
            disabled={!newName.trim() || newName.trim() === currentName}
          >
            Rename
          </Button>
        </div>
      </form>
    </Modal>
  );
};
