/**
 * Development Configuration Utilities
 * 
 * This module provides utilities for development mode configuration,
 * including authentication bypass and development-specific features.
 * 
 * SECURITY NOTE: All development features are automatically disabled
 * in production environments.
 */

export interface DevUser {
  id: string;
  email: string;
  name: string;
  image?: string;
  verified: boolean;
  allowPlatformS3: boolean;
}

export interface DevConfig {
  isDevelopment: boolean;
  bypassAuth: boolean;
  defaultUser: DevUser;
  enableDebugLogging: boolean;
  enablePerformanceLogging: boolean;
  enableDetailedErrors: boolean;
}

/**
 * Get development configuration
 * All development features are disabled in production
 */
export function getDevConfig(): DevConfig {
  const isDevelopment = process.env.NODE_ENV === 'development';
  
  // Force disable all development features in production
  if (!isDevelopment) {
    return {
      isDevelopment: false,
      bypassAuth: false,
      defaultUser: createDefaultDevUser(),
      enableDebugLogging: false,
      enablePerformanceLogging: false,
      enableDetailedErrors: false,
    };
  }

  return {
    isDevelopment: true,
    bypassAuth: process.env.DEVELOPMENT_MODE_BYPASS_AUTH === 'true',
    defaultUser: createDefaultDevUser(),
    enableDebugLogging: process.env.DEVELOPMENT_DEBUG_LOGGING !== 'false',
    enablePerformanceLogging: process.env.DEVELOPMENT_PERFORMANCE_LOGGING !== 'false',
    enableDetailedErrors: process.env.DEVELOPMENT_DETAILED_ERRORS !== 'false',
  };
}

/**
 * Create default development user
 */
function createDefaultDevUser(): DevUser {
  return {
    id: 'dev-user-id',
    email: process.env.DEVELOPMENT_DEFAULT_USER_EMAIL || '<EMAIL>',
    name: process.env.DEVELOPMENT_DEFAULT_USER_NAME || 'Development User',
    image: process.env.DEVELOPMENT_DEFAULT_USER_IMAGE,
    verified: true,
    allowPlatformS3: true,
  };
}

/**
 * Check if development mode is enabled
 */
export function isDevelopmentMode(): boolean {
  return getDevConfig().isDevelopment;
}

/**
 * Check if authentication bypass is enabled
 */
export function isAuthBypassEnabled(): boolean {
  return getDevConfig().bypassAuth;
}

/**
 * Get development user for authentication bypass
 */
export function getDevUser(): DevUser {
  return getDevConfig().defaultUser;
}

/**
 * Check if debug logging is enabled
 */
export function isDebugLoggingEnabled(): boolean {
  return getDevConfig().enableDebugLogging;
}

/**
 * Check if performance logging is enabled
 */
export function isPerformanceLoggingEnabled(): boolean {
  return getDevConfig().enablePerformanceLogging;
}

/**
 * Check if detailed errors are enabled
 */
export function isDetailedErrorsEnabled(): boolean {
  return getDevConfig().enableDetailedErrors;
}

/**
 * Development logger utility
 */
export class DevLogger {
  private static shouldLog(level: 'debug' | 'info' | 'warn' | 'error'): boolean {
    if (!isDevelopmentMode()) return false;
    if (level === 'debug' && !isDebugLoggingEnabled()) return false;
    return true;
  }

  static debug(message: string, data?: any): void {
    if (this.shouldLog('debug')) {
      console.log(`[DEV-DEBUG] ${message}`, data || '');
    }
  }

  static info(message: string, data?: any): void {
    if (this.shouldLog('info')) {
      console.log(`[DEV-INFO] ${message}`, data || '');
    }
  }

  static warn(message: string, data?: any): void {
    if (this.shouldLog('warn')) {
      console.warn(`[DEV-WARN] ${message}`, data || '');
    }
  }

  static error(message: string, data?: any): void {
    if (this.shouldLog('error')) {
      console.error(`[DEV-ERROR] ${message}`, data || '');
    }
  }

  static performance(operation: string, duration: number, data?: any): void {
    if (isPerformanceLoggingEnabled()) {
      console.log(`[DEV-PERF] ${operation}: ${duration.toFixed(2)}ms`, data || '');
    }
  }
}

/**
 * Development utilities for testing and debugging
 */
export class DevUtils {
  /**
   * Create a mock session for development testing
   */
  static createMockSession(overrides?: Partial<DevUser>) {
    const devUser = getDevUser();
    return {
      user: { ...devUser, ...overrides },
      expires: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString(),
    };
  }

  /**
   * Simulate authentication delay for testing
   */
  static async simulateAuthDelay(ms: number = 100): Promise<void> {
    if (isDevelopmentMode()) {
      await new Promise(resolve => setTimeout(resolve, ms));
    }
  }

  /**
   * Generate test data for development
   */
  static generateTestData(type: 'user' | 'file' | 'folder', count: number = 1) {
    if (!isDevelopmentMode()) {
      throw new Error('Test data generation is only available in development mode');
    }

    const data = [];
    for (let i = 0; i < count; i++) {
      switch (type) {
        case 'user':
          data.push({
            id: `test-user-${i}`,
            email: `test${i}@example.com`,
            name: `Test User ${i}`,
            verified: true,
            allowPlatformS3: false,
          });
          break;
        case 'file':
          data.push({
            id: `test-file-${i}`,
            name: `test-file-${i}.txt`,
            originalName: `test-file-${i}.txt`,
            size: Math.floor(Math.random() * 1000000),
            mimeType: 'text/plain',
            s3Key: `test/test-file-${i}.txt`,
            isPublic: false,
          });
          break;
        case 'folder':
          data.push({
            id: `test-folder-${i}`,
            name: `Test Folder ${i}`,
            isPublic: false,
          });
          break;
      }
    }
    return count === 1 ? data[0] : data;
  }
}

/**
 * Development mode banner for console
 */
export function logDevModeBanner(): void {
  if (!isDevelopmentMode()) return;

  const config = getDevConfig();
  console.log('\n🚀 DEVELOPMENT MODE ACTIVE 🚀');
  console.log('================================');
  console.log(`Authentication Bypass: ${config.bypassAuth ? '✅ ENABLED' : '❌ DISABLED'}`);
  console.log(`Debug Logging: ${config.enableDebugLogging ? '✅ ENABLED' : '❌ DISABLED'}`);
  console.log(`Performance Logging: ${config.enablePerformanceLogging ? '✅ ENABLED' : '❌ DISABLED'}`);
  console.log(`Detailed Errors: ${config.enableDetailedErrors ? '✅ ENABLED' : '❌ DISABLED'}`);
  console.log(`Default User: ${config.defaultUser.email}`);
  console.log('================================\n');
}
