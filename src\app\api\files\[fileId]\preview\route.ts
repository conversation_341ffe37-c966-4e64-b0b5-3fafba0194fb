import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@/lib/auth';
import connectDB from '@/lib/mongodb';
import { File } from '@/models/File';
import { S3Service } from '@/lib/s3';

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ fileId: string }> }
) {
  try {
    const session = await auth();
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { fileId } = await params;

    await connectDB();

    // Find the file
    const file = await File.findById(fileId);
    if (!file) {
      return NextResponse.json({ error: 'File not found' }, { status: 404 });
    }

    // Check if user owns the file or if it's public
    if (file.userId.toString() !== session.user.id && !file.isPublic) {
      return NextResponse.json({ error: 'Access denied' }, { status: 403 });
    }

    // Check if file type is previewable
    const previewableMimeTypes = [
      'image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp', 'image/svg+xml',
      'video/mp4', 'video/webm', 'video/ogg',
      'audio/mp3', 'audio/wav', 'audio/ogg', 'audio/mpeg',
      'application/pdf',
      'text/plain', 'text/html', 'text/css', 'text/javascript', 'application/json'
    ];

    if (!previewableMimeTypes.includes(file.mimeType)) {
      return NextResponse.json({ error: 'File type not previewable' }, { status: 400 });
    }

    // Get S3 service instance using the appropriate credentials for this file
    let s3Service: S3Service;

    try {
      s3Service = S3Service.getS3ServiceForFile(request, file, session);
    } catch (error) {
      console.error('S3 service configuration error:', error);
      return NextResponse.json(
        { error: 'Unable to access file storage. Please check your S3 configuration.' },
        { status: 500 }
      );
    }

    try {
      const fileStream = await s3Service.getFileStream(file.s3Key);
      
      // Convert ReadableStream to Response
      const response = new NextResponse(fileStream, {
        status: 200,
        headers: {
          'Content-Type': file.mimeType,
          'Content-Length': file.size.toString(),
          'Cache-Control': 'public, max-age=3600', // Cache for 1 hour
          'Content-Disposition': 'inline', // Display inline for preview
        },
      });

      return response;
    } catch (s3Error) {
      console.error('S3 error:', s3Error);
      return NextResponse.json({ error: 'File not found in storage' }, { status: 404 });
    }
  } catch (error) {
    console.error('Preview error:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
