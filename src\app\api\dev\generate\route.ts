/**
 * Development Code Generation API
 * 
 * Provides endpoints for generating boilerplate code including:
 * - API routes with authentication
 * - Test files (unit and standalone)
 * - Database models
 * - Component templates
 * 
 * SECURITY: Automatically disabled in production
 */

import { NextRequest, NextResponse } from 'next/server';
import { isDevelopmentMode, DevLogger } from '@/lib/dev-config';
import { CodeGenerator } from '@/lib/dev-workflow';
import { handleApiError } from '@/lib/error-handler';
import { ErrorUtils } from '@/lib/error-utils';

export async function POST(request: NextRequest) {
  // Ensure this endpoint is only available in development
  if (!isDevelopmentMode()) {
    return NextResponse.json(
      { error: 'This endpoint is only available in development mode' },
      { status: 404 }
    );
  }

  try {
    const body = await request.json();
    const { type, options } = body;

    DevLogger.debug(`Code generation requested: ${type}`, options);

    switch (type) {
      case 'api-route':
        return generateApiRoute(options);
      
      case 'test-file':
        return generateTestFile(options);
      
      case 'model':
        return generateModel(options);
      
      case 'component':
        return generateComponent(options);
      
      default:
        return NextResponse.json({
          error: 'Invalid generation type',
          availableTypes: ['api-route', 'test-file', 'model', 'component']
        }, { status: 400 });
    }

  } catch (error) {
    return handleApiError(error, request);
  }
}

async function generateApiRoute(options: any) {
  const {
    name,
    methods = ['GET', 'POST'],
    requiresAuth = true,
    requiresAdmin = false,
    description = '',
  } = options;

  if (!name) {
    throw ErrorUtils.validation('API route name is required', 'generateApiRoute');
  }

  const validMethods = ['GET', 'POST', 'PUT', 'DELETE', 'PATCH'];
  const invalidMethods = methods.filter((m: string) => !validMethods.includes(m));
  
  if (invalidMethods.length > 0) {
    throw ErrorUtils.validation(
      `Invalid HTTP methods: ${invalidMethods.join(', ')}`,
      'generateApiRoute',
      { validMethods, invalidMethods }
    );
  }

  const code = CodeGenerator.generateApiRoute({
    name,
    methods,
    requiresAuth,
    requiresAdmin,
  });

  const fileName = `${name.toLowerCase().replace(/\s+/g, '-')}.ts`;
  const filePath = `src/app/api/${name.toLowerCase().replace(/\s+/g, '-')}/route.ts`;

  return NextResponse.json({
    type: 'api-route',
    result: {
      name,
      fileName,
      filePath,
      code,
      methods,
      requiresAuth,
      requiresAdmin,
      description: description || `API route for ${name}`,
      instructions: [
        `Save the generated code to ${filePath}`,
        'Update the TODO comments with actual implementation',
        'Add proper input validation using Zod schemas',
        'Test the endpoints using the development tools',
        requiresAuth ? 'Ensure authentication is properly configured' : '',
        requiresAdmin ? 'Verify admin access control is working' : '',
      ].filter(Boolean),
    },
    timestamp: new Date().toISOString(),
  });
}

async function generateTestFile(options: any) {
  const {
    name,
    type = 'api',
    standalone = true,
    includeAuth = true,
    includeDatabase = true,
    testCases = [],
  } = options;

  if (!name) {
    throw ErrorUtils.validation('Test file name is required', 'generateTestFile');
  }

  const validTypes = ['api', 'component', 'feature', 'unit'];
  if (!validTypes.includes(type)) {
    throw ErrorUtils.validation(
      `Invalid test type: ${type}`,
      'generateTestFile',
      { validTypes }
    );
  }

  const code = CodeGenerator.generateTestFile({
    name,
    type,
    standalone,
  });

  // Add custom test cases if provided
  let enhancedCode = code;
  if (testCases.length > 0) {
    const customTests = testCases.map((testCase: any) => `
  it('${testCase.description}', async () => {
    // TODO: Implement test case: ${testCase.description}
    ${testCase.code || 'expect(true).toBe(true);'}
  });`).join('\n');

    enhancedCode = code.replace(
      "expect(true).toBe(true);",
      `expect(true).toBe(true);${customTests}`
    );
  }

  const fileName = `${name.toLowerCase().replace(/\s+/g, '-')}.${standalone ? 'standalone.' : ''}test.ts`;
  const filePath = standalone 
    ? `src/__tests__/features/${fileName}`
    : `src/__tests__/${type}/${fileName}`;

  return NextResponse.json({
    type: 'test-file',
    result: {
      name,
      fileName,
      filePath,
      code: enhancedCode,
      testType: type,
      standalone,
      includeAuth,
      includeDatabase,
      testCases: testCases.length,
      instructions: [
        `Save the generated code to ${filePath}`,
        'Replace TODO comments with actual test implementations',
        'Add proper mock setup for external dependencies',
        standalone ? 'Use FeatureTestUtils for isolated testing' : 'Use standard test helpers',
        'Run tests using npm run test:feature or npm test',
        'Add additional test cases as needed',
      ],
    },
    timestamp: new Date().toISOString(),
  });
}

async function generateModel(options: any) {
  const {
    name,
    fields = [],
    includeTimestamps = true,
    includeValidation = true,
    includeIndexes = false,
  } = options;

  if (!name) {
    throw ErrorUtils.validation('Model name is required', 'generateModel');
  }

  if (!Array.isArray(fields) || fields.length === 0) {
    throw ErrorUtils.validation('Model fields are required', 'generateModel');
  }

  // Validate field structure
  for (const field of fields) {
    if (!field.name || !field.type) {
      throw ErrorUtils.validation(
        'Each field must have name and type',
        'generateModel',
        { invalidField: field }
      );
    }
  }

  const code = CodeGenerator.generateModel({
    name,
    fields,
  });

  // Add validation and indexes if requested
  let enhancedCode = code;
  
  if (includeValidation) {
    const validationComment = `
// Add custom validation methods here
${name}Schema.methods.validateCustom = function() {
  // TODO: Add custom validation logic
  return true;
};`;
    
    enhancedCode = enhancedCode.replace(
      'export const',
      `${validationComment}\n\nexport const`
    );
  }

  if (includeIndexes) {
    const indexComment = `
// Add indexes for better query performance
// ${name}Schema.index({ userId: 1 });
// ${name}Schema.index({ createdAt: -1 });`;
    
    enhancedCode = enhancedCode.replace(
      '}, {\n  timestamps: true,\n});',
      `}, {\n  timestamps: true,\n});${indexComment}`
    );
  }

  const fileName = `${name}.ts`;
  const filePath = `src/models/${fileName}`;

  return NextResponse.json({
    type: 'model',
    result: {
      name,
      fileName,
      filePath,
      code: enhancedCode,
      fields: fields.length,
      includeTimestamps,
      includeValidation,
      includeIndexes,
      instructions: [
        `Save the generated code to ${filePath}`,
        'Review and customize the field types and validation',
        'Add any additional methods or static functions',
        'Consider adding indexes for frequently queried fields',
        'Test the model with sample data',
        'Update TypeScript types if needed',
      ],
    },
    timestamp: new Date().toISOString(),
  });
}

async function generateComponent(options: any) {
  const {
    name,
    type = 'functional',
    includeProps = true,
    includeState = false,
    includeStyles = true,
    framework = 'react',
  } = options;

  if (!name) {
    throw ErrorUtils.validation('Component name is required', 'generateComponent');
  }

  const validTypes = ['functional', 'class'];
  if (!validTypes.includes(type)) {
    throw ErrorUtils.validation(
      `Invalid component type: ${type}`,
      'generateComponent',
      { validTypes }
    );
  }

  // Generate React functional component
  const propsInterface = includeProps ? `
interface ${name}Props {
  // TODO: Define component props
  className?: string;
  children?: React.ReactNode;
}` : '';

  const stateHook = includeState ? `
  const [state, setState] = useState({
    // TODO: Define component state
  });` : '';

  const stylesImport = includeStyles ? `
import styles from './${name}.module.css';` : '';

  const componentCode = `'use client';

import React${includeState ? ', { useState }' : ''} from 'react';${stylesImport}${propsInterface}

export default function ${name}(${includeProps ? `props: ${name}Props` : ''}) {${stateHook}

  return (
    <div${includeStyles ? ` className={styles.container}` : ''}${includeProps ? ' {...props}' : ''}>
      <h1>TODO: Implement ${name} component</h1>
      {/* TODO: Add component content */}
    </div>
  );
}`;

  const stylesCode = includeStyles ? `
.container {
  /* TODO: Add component styles */
  padding: 1rem;
}` : '';

  const fileName = `${name}.tsx`;
  const filePath = `src/components/${fileName}`;
  const stylesPath = `src/components/${name}.module.css`;

  const result: any = {
    name,
    fileName,
    filePath,
    code: componentCode,
    type,
    includeProps,
    includeState,
    includeStyles,
    framework,
    instructions: [
      `Save the component code to ${filePath}`,
      'Replace TODO comments with actual implementation',
      'Define proper TypeScript interfaces for props',
      includeStyles ? `Save the styles to ${stylesPath}` : '',
      'Add the component to your page or parent component',
      'Test the component in the browser',
    ].filter(Boolean),
  };

  if (includeStyles) {
    result.styles = {
      code: stylesCode,
      filePath: stylesPath,
    };
  }

  return NextResponse.json({
    type: 'component',
    result,
    timestamp: new Date().toISOString(),
  });
}

export async function GET(request: NextRequest) {
  // Ensure this endpoint is only available in development
  if (!isDevelopmentMode()) {
    return NextResponse.json(
      { error: 'This endpoint is only available in development mode' },
      { status: 404 }
    );
  }

  return NextResponse.json({
    message: 'Development Code Generator',
    availableTypes: [
      {
        type: 'api-route',
        description: 'Generate API route with authentication',
        requiredFields: ['name'],
        optionalFields: ['methods', 'requiresAuth', 'requiresAdmin', 'description'],
      },
      {
        type: 'test-file',
        description: 'Generate test file boilerplate',
        requiredFields: ['name'],
        optionalFields: ['type', 'standalone', 'includeAuth', 'includeDatabase', 'testCases'],
      },
      {
        type: 'model',
        description: 'Generate Mongoose model',
        requiredFields: ['name', 'fields'],
        optionalFields: ['includeTimestamps', 'includeValidation', 'includeIndexes'],
      },
      {
        type: 'component',
        description: 'Generate React component',
        requiredFields: ['name'],
        optionalFields: ['type', 'includeProps', 'includeState', 'includeStyles', 'framework'],
      },
    ],
    usage: {
      method: 'POST',
      body: {
        type: 'api-route | test-file | model | component',
        options: {
          name: 'string (required)',
          '...': 'other options based on type',
        },
      },
    },
    timestamp: new Date().toISOString(),
  });
}
