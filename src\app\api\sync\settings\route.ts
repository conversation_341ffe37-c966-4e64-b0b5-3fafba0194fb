import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@/lib/auth';
import { User } from '@/models/User';
import connectDB from '@/lib/mongodb';

export async function GET(request: NextRequest) {
  try {
    const session = await auth();
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    await connectDB();

    const user = await User.findById(session.user.id).select('syncEnabled lastSyncAt');
    
    if (!user) {
      return NextResponse.json(
        { error: 'User not found' },
        { status: 404 }
      );
    }

    return NextResponse.json({
      syncEnabled: user.syncEnabled || false,
      lastSyncAt: user.lastSyncAt
    });

  } catch (error) {
    console.error('Sync settings fetch error:', error);
    
    return NextResponse.json(
      { error: 'Failed to fetch sync settings' },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const session = await auth();
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const body = await request.json();
    const { syncEnabled } = body;

    if (typeof syncEnabled !== 'boolean') {
      return NextResponse.json(
        { error: 'syncEnabled must be a boolean' },
        { status: 400 }
      );
    }

    await connectDB();

    await User.findByIdAndUpdate(session.user.id, {
      syncEnabled: syncEnabled
    });

    return NextResponse.json({
      message: `Sync ${syncEnabled ? 'enabled' : 'disabled'} successfully`,
      syncEnabled
    });

  } catch (error) {
    console.error('Sync settings update error:', error);
    
    return NextResponse.json(
      { error: 'Failed to update sync settings' },
      { status: 500 }
    );
  }
}
