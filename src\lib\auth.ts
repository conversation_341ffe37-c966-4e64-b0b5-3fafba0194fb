import NextAuth from 'next-auth';
import Credentials<PERSON>rovider from 'next-auth/providers/credentials';
import GoogleProvider from 'next-auth/providers/google';
import { authenticateUser, handleGoogleSignIn } from './auth-server';
import { isAuthBypassEnabled, getDevUser, DevLogger } from './dev-config';

export const { handlers, signIn, signOut, auth } = NextAuth({
  providers: [
    CredentialsProvider({
      name: 'credentials',
      credentials: {
        email: { label: 'Email', type: 'email' },
        password: { label: 'Password', type: 'password' }
      },
      async authorize(credentials) {
        // Development mode bypass
        if (isAuthBypassEnabled()) {
          DevLogger.debug('Using development authentication bypass for credentials');
          const devUser = getDevUser();
          return {
            id: devUser.id,
            email: devUser.email,
            name: devUser.name,
            image: devUser.image,
            verified: devUser.verified,
            allowPlatformS3: devUser.allowPlatformS3,
          };
        }

        if (!credentials?.email || !credentials?.password) {
          return null;
        }

        const user = await authenticateUser(
          credentials.email as string,
          credentials.password as string
        );

        return user;
      }
    }),
    GoogleProvider({
      clientId: process.env.GOOGLE_CLIENT_ID!,
      clientSecret: process.env.GOOGLE_CLIENT_SECRET!,
    })
  ],
  callbacks: {
    async signIn({ user, account, profile }) {
      // Development mode bypass
      if (isAuthBypassEnabled()) {
        DevLogger.debug('Bypassing signIn callback in development mode');
        return true;
      }

      if (account?.provider === 'google') {
        const success = await handleGoogleSignIn(user);
        return success;
      }
      return true;
    },
    async jwt({ token, user }) {
      if (user) {
        token.verified = user.verified;
        token.allowPlatformS3 = user.allowPlatformS3;
      }
      return token;
    },
    async session({ session, token }) {
      if (session.user) {
        session.user.id = token.sub!;
        session.user.verified = token.verified as boolean;
        session.user.allowPlatformS3 = token.allowPlatformS3 as boolean;
      }
      return session;
    }
  },
  pages: {
    signIn: '/login',
  },
  session: {
    strategy: 'jwt',
  },
});
