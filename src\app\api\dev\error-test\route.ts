/**
 * Development Error Testing Endpoint
 * 
 * This endpoint is only available in development mode and provides
 * utilities for testing error handling, context generation, and debugging.
 * 
 * SECURITY: Automatically disabled in production
 */

import { NextRequest, NextResponse } from 'next/server';
import { isDevelopmentMode, DevLogger } from '@/lib/dev-config';
import { ErrorUtils, DatabaseErrorUtils, APIErrorUtils } from '@/lib/error-utils';
import { 
  AppError, 
  ValidationError, 
  AuthenticationError, 
  AuthorizationError, 
  NotFoundError,
  handleApiError
} from '@/lib/error-handler';

export async function GET(request: NextRequest) {
  // Ensure this endpoint is only available in development
  if (!isDevelopmentMode()) {
    return NextResponse.json(
      { error: 'This endpoint is only available in development mode' },
      { status: 404 }
    );
  }

  try {
    const { searchParams } = new URL(request.url);
    const errorType = searchParams.get('type') || 'basic';
    const includeContext = searchParams.get('context') === 'true';

    DevLogger.debug(`Testing error type: ${errorType}`);

    switch (errorType) {
      case 'basic':
        return testBasicError(includeContext);
      
      case 'validation':
        return testValidationError(includeContext);
      
      case 'authentication':
        return testAuthenticationError(includeContext);
      
      case 'authorization':
        return testAuthorizationError(includeContext);
      
      case 'notfound':
        return testNotFoundError(includeContext);
      
      case 'database':
        return testDatabaseError(includeContext);
      
      case 'api':
        return testAPIError(includeContext);
      
      case 'wrapped':
        return testWrappedError(includeContext);
      
      case 'chain':
        return testErrorChain(includeContext);
      
      default:
        return NextResponse.json({
          error: 'Invalid error type',
          availableTypes: [
            'basic', 'validation', 'authentication', 'authorization', 
            'notfound', 'database', 'api', 'wrapped', 'chain'
          ]
        }, { status: 400 });
    }

  } catch (error) {
    // This will demonstrate the error handling in action
    return handleApiError(error, request);
  }
}

function testBasicError(includeContext: boolean) {
  if (includeContext) {
    throw ErrorUtils.create(
      'This is a test error with context',
      'testBasicError',
      { 
        testData: 'sample data',
        userId: 'test-user-123',
        timestamp: new Date().toISOString()
      }
    );
  } else {
    throw new Error('This is a basic test error without context');
  }
}

function testValidationError(includeContext: boolean) {
  if (includeContext) {
    throw ErrorUtils.validation(
      'Validation failed for test data',
      'testValidationError',
      {
        field: 'email',
        value: 'invalid-email',
        constraint: 'must be valid email format'
      },
      {
        requestData: { email: 'invalid-email', name: 'Test User' },
        validationRules: ['email', 'required', 'max:255']
      }
    );
  } else {
    throw new ValidationError('Basic validation error');
  }
}

function testAuthenticationError(includeContext: boolean) {
  if (includeContext) {
    throw ErrorUtils.authentication(
      'Authentication failed - invalid token',
      'testAuthenticationError',
      {
        tokenType: 'JWT',
        tokenExpiry: new Date(Date.now() - 3600000).toISOString(),
        userAgent: 'Test Browser',
        ipAddress: '127.0.0.1'
      }
    );
  } else {
    throw new AuthenticationError('Basic authentication error');
  }
}

function testAuthorizationError(includeContext: boolean) {
  if (includeContext) {
    throw ErrorUtils.authorization(
      'User does not have permission to access admin features',
      'testAuthorizationError',
      {
        userId: 'test-user-123',
        userRole: 'user',
        requiredRole: 'admin',
        resource: 'admin-dashboard',
        action: 'read'
      }
    );
  } else {
    throw new AuthorizationError('Basic authorization error');
  }
}

function testNotFoundError(includeContext: boolean) {
  if (includeContext) {
    throw ErrorUtils.notFound(
      'File not found in user storage',
      'testNotFoundError',
      {
        fileId: 'test-file-456',
        userId: 'test-user-123',
        searchPath: '/user/files/test-file-456',
        lastAccessed: '2024-01-01T00:00:00Z'
      }
    );
  } else {
    throw new NotFoundError('Basic not found error');
  }
}

function testDatabaseError(includeContext: boolean) {
  if (includeContext) {
    throw DatabaseErrorUtils.queryError(
      'testDatabaseError',
      'SELECT * FROM users WHERE id = ?',
      new Error('Connection timeout')
    );
  } else {
    throw DatabaseErrorUtils.connectionError('testDatabaseError');
  }
}

function testAPIError(includeContext: boolean) {
  if (includeContext) {
    throw APIErrorUtils.invalidParameter(
      'testAPIError',
      'limit',
      'not-a-number',
      'positive integer'
    );
  } else {
    throw APIErrorUtils.missingParameter('testAPIError', 'required_field');
  }
}

async function testWrappedError(includeContext: boolean) {
  return await ErrorUtils.wrap(
    async () => {
      // Simulate an operation that throws
      throw new Error('Simulated async operation failure');
    },
    'testWrappedError',
    includeContext ? {
      operation: 'file-upload',
      retryCount: 3,
      lastAttempt: new Date().toISOString()
    } : undefined
  );
}

async function testErrorChain(includeContext: boolean) {
  try {
    await testWrappedError(includeContext);
  } catch (error) {
    // Chain errors to show how context accumulates
    throw ErrorUtils.create(
      'Error chain test - operation failed after multiple attempts',
      'testErrorChain',
      {
        originalError: error instanceof Error ? error.message : 'Unknown',
        chainLevel: 2,
        finalAttempt: true
      }
    );
  }
}

export async function POST(request: NextRequest) {
  // Ensure this endpoint is only available in development
  if (!isDevelopmentMode()) {
    return NextResponse.json(
      { error: 'This endpoint is only available in development mode' },
      { status: 404 }
    );
  }

  try {
    const body = await request.json();
    const { action, errorData } = body;

    DevLogger.debug(`Running error action: ${action}`);

    switch (action) {
      case 'create-custom':
        return createCustomError(errorData);
      
      case 'test-handler':
        return testErrorHandler(errorData);
      
      case 'simulate-production':
        return simulateProductionError(errorData);
      
      default:
        return NextResponse.json({
          error: 'Invalid action',
          availableActions: ['create-custom', 'test-handler', 'simulate-production']
        }, { status: 400 });
    }

  } catch (error) {
    return handleApiError(error, request);
  }
}

function createCustomError(errorData: any) {
  const { message, statusCode, variables, suggestions } = errorData;
  
  const error = ErrorUtils.create(
    message || 'Custom test error',
    'createCustomError',
    variables,
    statusCode || 500
  );

  if (suggestions && Array.isArray(suggestions)) {
    error.withSuggestions(suggestions);
  }

  throw error;
}

function testErrorHandler(errorData: any) {
  // Create an error and test how it's handled
  const error = new AppError(
    errorData.message || 'Testing error handler',
    errorData.statusCode || 500,
    true,
    'TEST_ERROR',
    {
      location: {
        file: 'error-test/route.ts',
        function: 'testErrorHandler',
      },
      variables: errorData.variables || { test: true },
      suggestions: ['This is a test error for handler validation']
    }
  );

  throw error;
}

function simulateProductionError(errorData: any) {
  // Temporarily disable detailed errors to simulate production
  const originalEnv = process.env.NODE_ENV;
  process.env.NODE_ENV = 'production';

  try {
    throw ErrorUtils.create(
      errorData.message || 'Production simulation error',
      'simulateProductionError',
      errorData.variables
    );
  } finally {
    process.env.NODE_ENV = originalEnv;
  }
}
