import { NextResponse } from 'next/server';
import type { NextRequest } from 'next/server';
import {
  getEdgeSession,
  isAdminUser,
  isProtectedRoute,
  isAdminRoute,
  isPublicRoute,
  isAdminApiRoute
} from '@/lib/auth-edge';
import {
  getSessionWithDevSupport,
  isAdminUserWithDevSupport,
  shouldBypassRouteProtection
} from '@/lib/auth-dev';
import { DevLogger, logDevModeBanner } from '@/lib/dev-config';

export async function middleware(request: NextRequest) {
  const { pathname } = request.nextUrl;

  // Log development mode banner on first request
  if (pathname === '/' && process.env.NODE_ENV === 'development') {
    logDevModeBanner();
  }

  // Get session using development-aware method
  const session = await getSessionWithDevSupport(request);

  DevLogger.debug(`Middleware processing: ${pathname}`, {
    hasSession: !!session,
    userEmail: session?.user?.email
  });

  // Check route types using Edge-compatible utilities
  const isProtected = isProtectedRoute(pathname);
  const isAdmin = isAdminRoute(pathname);
  const isAdminApi = isAdminApiRoute(pathname);
  const isPublic = isPublicRoute(pathname);

  // Check if we should bypass route protection in development
  const shouldBypass = shouldBypassRouteProtection(pathname);

  // Redirect authenticated users away from public routes
  if (isPublic && session) {
    DevLogger.debug(`Redirecting authenticated user from public route: ${pathname}`);
    return NextResponse.redirect(new URL('/dashboard', request.url));
  }

  // Redirect unauthenticated users from protected routes (unless bypassed in dev)
  if (isProtected && !session && !shouldBypass) {
    DevLogger.debug(`Redirecting unauthenticated user from protected route: ${pathname}`);
    const loginUrl = new URL('/login', request.url);
    loginUrl.searchParams.set('callbackUrl', pathname);
    return NextResponse.redirect(loginUrl);
  }

  // Redirect unverified users from protected routes (except verification routes)
  // Skip verification check if bypassing in development
  if (isProtected && session && !session.user.verified && !pathname.startsWith('/verify-email') && !shouldBypass) {
    DevLogger.debug(`Redirecting unverified user to verification: ${pathname}`);
    const verifyUrl = new URL('/verify-email', request.url);
    return NextResponse.redirect(verifyUrl);
  }

  // Check admin access for admin routes and API routes
  if ((isAdmin || isAdminApi) && session) {
    const hasAdminAccess = isAdminUserWithDevSupport(session.user.email);
    if (!hasAdminAccess) {
      DevLogger.debug(`Denying admin access to user: ${session.user.email}`);
      if (isAdminApi) {
        return NextResponse.json(
          { error: 'Access denied' },
          { status: 403 }
        );
      } else {
        // Return 404 for admin dashboard to hide its existence
        return NextResponse.rewrite(new URL('/404', request.url));
      }
    } else {
      DevLogger.debug(`Granting admin access to user: ${session.user.email}`);
    }
  }

  // Handle admin routes without session (unless bypassed in dev)
  if ((isAdmin || isAdminApi) && !session && !shouldBypass) {
    DevLogger.debug(`Redirecting unauthenticated user from admin route: ${pathname}`);
    if (isAdminApi) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    } else {
      const loginUrl = new URL('/login', request.url);
      loginUrl.searchParams.set('callbackUrl', pathname);
      return NextResponse.redirect(loginUrl);
    }
  }

  DevLogger.debug(`Allowing access to: ${pathname}`);
  return NextResponse.next();
}

export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     * - public folder
     * - api/health (health check endpoint)
     */
    '/((?!_next/static|_next/image|favicon.ico|public|api/health).*)',
  ],
};
