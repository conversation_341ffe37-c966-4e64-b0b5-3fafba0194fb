import { NextResponse } from 'next/server';
import type { NextRequest } from 'next/server';
import {
  getEdgeSession,
  isAdminUser,
  isProtectedRoute,
  isAdminRoute,
  isPublicRoute,
  isAdminApiRoute
} from '@/lib/auth-edge';

export async function middleware(request: NextRequest) {
  const { pathname } = request.nextUrl;

  // Get session using Edge-compatible method
  const session = await getEdgeSession(request);

  // Check route types using Edge-compatible utilities
  const isProtected = isProtectedRoute(pathname);
  const isAdmin = isAdminRoute(pathname);
  const isAdminApi = isAdminApiRoute(pathname);
  const isPublic = isPublicRoute(pathname);

  // Redirect authenticated users away from public routes
  if (isPublic && session) {
    return NextResponse.redirect(new URL('/dashboard', request.url));
  }

  // Redirect unauthenticated users from protected routes
  if (isProtected && !session) {
    const loginUrl = new URL('/login', request.url);
    loginUrl.searchParams.set('callbackUrl', pathname);
    return NextResponse.redirect(loginUrl);
  }

  // Redirect unverified users from protected routes (except verification routes)
  if (isProtected && session && !session.user.verified && !pathname.startsWith('/verify-email')) {
    const verifyUrl = new URL('/verify-email', request.url);
    return NextResponse.redirect(verifyUrl);
  }

  // Check admin access for admin routes and API routes
  if ((isAdmin || isAdminApi) && session) {
    if (!isAdminUser(session.user.email)) {
      if (isAdminApi) {
        return NextResponse.json(
          { error: 'Access denied' },
          { status: 403 }
        );
      } else {
        // Return 404 for admin dashboard to hide its existence
        return NextResponse.rewrite(new URL('/404', request.url));
      }
    }
  }

  // Handle admin routes without session
  if ((isAdmin || isAdminApi) && !session) {
    if (isAdminApi) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    } else {
      const loginUrl = new URL('/login', request.url);
      loginUrl.searchParams.set('callbackUrl', pathname);
      return NextResponse.redirect(loginUrl);
    }
  }
  

  return NextResponse.next();
}

export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     * - public folder
     * - api/health (health check endpoint)
     */
    '/((?!_next/static|_next/image|favicon.ico|public|api/health).*)',
  ],
};
