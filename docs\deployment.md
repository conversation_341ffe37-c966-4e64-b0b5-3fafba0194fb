# Deployment Guide

This guide covers various deployment options for Drivn, from simple cloud deployments to self-hosted solutions.

## Prerequisites

Before deploying, ensure you have:

- MongoDB database (MongoDB Atlas, self-hosted, or cloud provider)
- S3-compatible storage (AWS S3, Wasabi, Backblaze B2, etc.)
- SMTP email service (Gmail, SendGrid, etc.)
- Domain name (for production)
- SSL certificate (for HTTPS)

## Environment Variables

Create a `.env.local` file with the following variables:

```env
# Database
MONGODB_URI=mongodb+srv://username:<EMAIL>/drivn

# NextAuth
NEXTAUTH_URL=https://your-domain.com
NEXTAUTH_SECRET=your-secure-secret-key-here

# Google OAuth (optional)
GOOGLE_CLIENT_ID=your-google-client-id
GOOGLE_CLIENT_SECRET=your-google-client-secret

# Email Configuration
EMAIL_SERVER_HOST=smtp.gmail.com
EMAIL_SERVER_PORT=587
EMAIL_SERVER_USER=<EMAIL>
EMAIL_SERVER_PASSWORD=your-app-password
EMAIL_FROM=<EMAIL>

# Platform S3 (optional)
PLATFORM_S3_ENDPOINT=https://s3.amazonaws.com
PLATFORM_S3_REGION=us-east-1
PLATFORM_S3_ACCESS_KEY_ID=your-access-key
PLATFORM_S3_SECRET_ACCESS_KEY=your-secret-key
PLATFORM_S3_BUCKET=your-bucket-name

# Security
ENCRYPTION_KEY=your-32-character-encryption-key-here

# Admin Users
ADMIN_EMAILS=<EMAIL>,<EMAIL>
```

## Deployment Options

### 1. Vercel (Recommended)

Vercel provides the easiest deployment experience for Next.js applications.

#### Steps:

1. **Push to GitHub**
   ```bash
   git add .
   git commit -m "Initial commit"
   git push origin main
   ```

2. **Connect to Vercel**
   - Go to [vercel.com](https://vercel.com)
   - Sign in with GitHub
   - Import your repository
   - Configure environment variables

3. **Environment Variables**
   Add all environment variables in the Vercel dashboard under Settings > Environment Variables.

4. **Deploy**
   Vercel will automatically deploy your application and provide a URL.

#### Vercel Configuration

Create `vercel.json` in your project root:

```json
{
  "functions": {
    "src/app/api/**/*.ts": {
      "maxDuration": 30
    }
  },
  "regions": ["iad1"],
  "framework": "nextjs"
}
```

### 2. Netlify

#### Steps:

1. **Build Settings**
   - Build command: `pnpm build`
   - Publish directory: `.next`

2. **Environment Variables**
   Add all environment variables in Netlify dashboard.

3. **Netlify Configuration**
   Create `netlify.toml`:

```toml
[build]
  command = "pnpm build"
  publish = ".next"

[build.environment]
  NODE_VERSION = "18"

[[redirects]]
  from = "/api/*"
  to = "/.netlify/functions/:splat"
  status = 200

[[redirects]]
  from = "/*"
  to = "/index.html"
  status = 200
```

### 3. Railway

#### Steps:

1. **Connect Repository**
   - Go to [railway.app](https://railway.app)
   - Connect your GitHub repository

2. **Environment Variables**
   Add environment variables in Railway dashboard.

3. **Deploy**
   Railway will automatically build and deploy your application.

### 4. DigitalOcean App Platform

#### Steps:

1. **Create App**
   - Go to DigitalOcean App Platform
   - Connect your GitHub repository

2. **App Spec**
   Create `.do/app.yaml`:

```yaml
name: drivn
services:
- name: web
  source_dir: /
  github:
    repo: your-username/drivn
    branch: main
  run_command: pnpm start
  build_command: pnpm build
  environment_slug: node-js
  instance_count: 1
  instance_size_slug: basic-xxs
  envs:
  - key: NODE_ENV
    value: production
  # Add other environment variables here
```

### 5. AWS (Advanced)

Deploy using AWS services for maximum control and scalability.

#### Architecture:
- **Compute**: AWS Lambda + API Gateway or ECS Fargate
- **Database**: MongoDB Atlas or DocumentDB
- **Storage**: S3
- **CDN**: CloudFront
- **DNS**: Route 53

#### Using AWS Amplify:

1. **Install Amplify CLI**
   ```bash
   npm install -g @aws-amplify/cli
   amplify configure
   ```

2. **Initialize Amplify**
   ```bash
   amplify init
   amplify add hosting
   amplify publish
   ```

### 6. Docker Deployment

#### Dockerfile

Create `Dockerfile`:

```dockerfile
FROM node:18-alpine AS base

# Install dependencies only when needed
FROM base AS deps
RUN apk add --no-cache libc6-compat
WORKDIR /app

# Install dependencies based on the preferred package manager
COPY package.json pnpm-lock.yaml* ./
RUN npm install -g pnpm && pnpm install --frozen-lockfile

# Rebuild the source code only when needed
FROM base AS builder
WORKDIR /app
COPY --from=deps /app/node_modules ./node_modules
COPY . .

RUN npm install -g pnpm && pnpm build

# Production image, copy all the files and run next
FROM base AS runner
WORKDIR /app

ENV NODE_ENV production

RUN addgroup --system --gid 1001 nodejs
RUN adduser --system --uid 1001 nextjs

COPY --from=builder /app/public ./public

# Set the correct permission for prerender cache
RUN mkdir .next
RUN chown nextjs:nodejs .next

# Automatically leverage output traces to reduce image size
COPY --from=builder --chown=nextjs:nodejs /app/.next/standalone ./
COPY --from=builder --chown=nextjs:nodejs /app/.next/static ./.next/static

USER nextjs

EXPOSE 3000

ENV PORT 3000
ENV HOSTNAME "0.0.0.0"

CMD ["node", "server.js"]
```

#### Docker Compose

Create `docker-compose.yml`:

```yaml
version: '3.8'

services:
  app:
    build: .
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=production
      - MONGODB_URI=${MONGODB_URI}
      - NEXTAUTH_SECRET=${NEXTAUTH_SECRET}
      # Add other environment variables
    depends_on:
      - mongodb

  mongodb:
    image: mongo:6
    ports:
      - "27017:27017"
    volumes:
      - mongodb_data:/data/db
    environment:
      - MONGO_INITDB_ROOT_USERNAME=admin
      - MONGO_INITDB_ROOT_PASSWORD=password

volumes:
  mongodb_data:
```

#### Deploy with Docker:

```bash
# Build and run
docker-compose up -d

# Or build and push to registry
docker build -t your-registry/drivn:latest .
docker push your-registry/drivn:latest
```

### 7. Kubernetes

#### Deployment YAML

Create `k8s/deployment.yaml`:

```yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: drivn
spec:
  replicas: 3
  selector:
    matchLabels:
      app: drivn
  template:
    metadata:
      labels:
        app: drivn
    spec:
      containers:
      - name: drivn
        image: your-registry/drivn:latest
        ports:
        - containerPort: 3000
        env:
        - name: NODE_ENV
          value: "production"
        - name: MONGODB_URI
          valueFrom:
            secretKeyRef:
              name: drivn-secrets
              key: mongodb-uri
        # Add other environment variables
---
apiVersion: v1
kind: Service
metadata:
  name: drivn-service
spec:
  selector:
    app: drivn
  ports:
  - port: 80
    targetPort: 3000
  type: LoadBalancer
```

## Post-Deployment Checklist

### 1. Verify Environment Variables
- [ ] All required environment variables are set
- [ ] Database connection is working
- [ ] S3 credentials are valid
- [ ] Email service is configured

### 2. Test Core Functionality
- [ ] User registration works
- [ ] Email verification works
- [ ] File upload works
- [ ] File sharing works
- [ ] Admin dashboard accessible (for admin users)

### 3. Security Configuration
- [ ] HTTPS is enabled
- [ ] Security headers are set
- [ ] Rate limiting is active
- [ ] Admin emails are configured

### 4. Performance Optimization
- [ ] Enable compression
- [ ] Configure CDN (if applicable)
- [ ] Set up monitoring
- [ ] Configure logging

### 5. Monitoring and Logging
- [ ] Health check endpoint is accessible
- [ ] Error logging is working
- [ ] Performance metrics are collected
- [ ] Alerts are configured

## Troubleshooting

### Common Issues

1. **Database Connection Failed**
   - Check MongoDB URI format
   - Verify network connectivity
   - Check firewall settings

2. **S3 Upload Errors**
   - Verify S3 credentials
   - Check bucket permissions
   - Ensure CORS is configured

3. **Email Not Sending**
   - Check SMTP credentials
   - Verify email provider settings
   - Check spam folders

4. **Authentication Issues**
   - Verify NEXTAUTH_SECRET is set
   - Check NEXTAUTH_URL matches deployment URL
   - Ensure cookies are working

### Logs and Debugging

Check application logs for errors:

```bash
# Vercel
vercel logs

# Docker
docker logs container-name

# Kubernetes
kubectl logs deployment/drivn
```

## Scaling Considerations

### Horizontal Scaling
- Use load balancers
- Implement session storage (Redis)
- Use CDN for static assets

### Database Scaling
- MongoDB replica sets
- Read replicas
- Sharding for large datasets

### File Storage
- Multiple S3 buckets
- CDN for file delivery
- Compression and optimization

## Security Best Practices

1. **Use HTTPS everywhere**
2. **Keep dependencies updated**
3. **Regular security audits**
4. **Monitor for vulnerabilities**
5. **Implement proper logging**
6. **Use strong encryption keys**
7. **Regular backups**
