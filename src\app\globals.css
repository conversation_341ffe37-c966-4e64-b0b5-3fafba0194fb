@import "tailwindcss";

:root,
[data-theme="light"] {
  /* Light theme */
  --background: #ffffff;
  --secondary: #f4f4f6;
  --accent: #0b0b0f;
  --text-primary: #1f1f1f;
  --text-secondary: #6b7280;
  --divider: #dddee0;
  --success: #10b981;
  --error: #ef4444;
  --warning: #f59e0b;
}

[data-theme="dark"] {
  /* Dark theme */
  --background: #0b0b0f;
  --secondary: #1a1a1d;
  --accent: #2f80ed;
  --text-primary: #ffffff;
  --text-secondary: #b0b0b0;
  --divider: #2c2c2e;
  --success: #10b981;
  --error: #ef4444;
  --warning: #f59e0b;
}

/* Tailwind CSS theme configuration */
@theme {
  --color-background: var(--background);
  --color-secondary: var(--secondary);
  --color-accent: var(--accent);
  --color-text-primary: var(--text-primary);
  --color-text-secondary: var(--text-secondary);
  --color-divider: var(--divider);
  --color-success: var(--success);
  --color-error: var(--error);
  --color-warning: var(--warning);
  --font-sans: system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
  --font-mono: ui-monospace, SFMono-Regular, "SF Mono", Consolas, "Liberation Mono", Menlo, monospace;
}

@media (prefers-color-scheme: dark) {
  :root:not([data-theme]) {
    --background: #0b0b0f;
    --secondary: #1a1a1d;
    --accent: #2f80ed;
    --text-primary: #ffffff;
    --text-secondary: #b0b0b0;
    --divider: #2c2c2e;
  }
}

* {
  box-sizing: border-box;
}

html {
  scroll-behavior: smooth;
}

body {
  background: var(--background);
  color: var(--text-primary);
  font-family: var(--font-sans);
  line-height: 1.6;
  margin: 0;
  padding: 0;
  transition: background-color 0.2s ease, color 0.2s ease;
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: var(--secondary);
}

::-webkit-scrollbar-thumb {
  background: var(--divider);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--text-secondary);
}

/* Focus styles */
:focus-visible {
  outline: 2px solid var(--accent);
  outline-offset: 2px;
}
