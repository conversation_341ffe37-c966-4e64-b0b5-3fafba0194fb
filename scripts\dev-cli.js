#!/usr/bin/env node

/**
 * Drivn Development CLI
 * 
 * A comprehensive command-line interface for development workflow automation:
 * - Feature initialization and scaffolding
 * - Code generation (API routes, models, tests, components)
 * - Development server management
 * - Testing utilities
 * - Database management
 * 
 * Usage:
 *   pnpm run dev:cli <command> [options]
 *   node scripts/dev-cli.js <command> [options]
 */

const { spawn } = require('child_process');
const fs = require('fs').promises;
const path = require('path');
const readline = require('readline');

// Color codes for console output
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m',
};

function colorize(text, color) {
  return `${colors[color]}${text}${colors.reset}`;
}

function printBanner() {
  console.log(colorize('\n🚀 Drivn Development CLI', 'cyan'));
  console.log(colorize('========================', 'cyan'));
}

function printUsage() {
  printBanner();
  console.log('\nAvailable commands:');
  console.log(colorize('  init-feature <name>', 'green') + '     - Initialize a new feature with all files');
  console.log(colorize('  generate api <name>', 'green') + '     - Generate API route boilerplate');
  console.log(colorize('  generate model <name>', 'green') + '   - Generate database model');
  console.log(colorize('  generate test <name>', 'green') + '    - Generate test file');
  console.log(colorize('  generate component <name>', 'green') + ' - Generate React component');
  console.log(colorize('  test-feature <name>', 'green') + '    - Run feature tests');
  console.log(colorize('  dev-server', 'green') + '            - Start development server with enhancements');
  console.log(colorize('  db-reset', 'green') + '             - Reset development database');
  console.log(colorize('  health-check', 'green') + '         - Check system health');
  console.log(colorize('  dashboard', 'green') + '            - Open development dashboard');
  console.log('\nOptions:');
  console.log('  --help, -h    - Show help for specific command');
  console.log('  --verbose, -v - Show detailed output');
  console.log('  --dry-run     - Show what would be done without executing');
  console.log('\nExamples:');
  console.log(colorize('  pnpm run dev:cli init-feature sharing', 'yellow'));
  console.log(colorize('  pnpm run dev:cli generate api user-profile', 'yellow'));
  console.log(colorize('  pnpm run dev:cli test-feature auth --watch', 'yellow'));
  console.log('');
}

async function createReadlineInterface() {
  return readline.createInterface({
    input: process.stdin,
    output: process.stdout,
  });
}

async function askQuestion(rl, question) {
  return new Promise((resolve) => {
    rl.question(question, (answer) => {
      resolve(answer.trim());
    });
  });
}

async function initFeature(featureName, options = {}) {
  console.log(colorize(`\n🎯 Initializing feature: ${featureName}`, 'cyan'));
  
  if (!featureName) {
    console.error(colorize('❌ Feature name is required', 'red'));
    return;
  }

  const rl = await createReadlineInterface();
  
  try {
    // Ask for feature details
    console.log('\nFeature configuration:');
    const includeApi = await askQuestion(rl, 'Include API routes? (y/N): ') === 'y';
    const includeModel = await askQuestion(rl, 'Include database model? (y/N): ') === 'y';
    const includeTests = await askQuestion(rl, 'Include test files? (Y/n): ') !== 'n';
    const includeComponent = await askQuestion(rl, 'Include React component? (y/N): ') === 'y';
    
    if (options.dryRun) {
      console.log(colorize('\n📋 Dry run - would create:', 'yellow'));
      if (includeApi) console.log(`  - API route: src/app/api/${featureName.toLowerCase()}/route.ts`);
      if (includeModel) console.log(`  - Model: src/models/${featureName}.ts`);
      if (includeTests) console.log(`  - Test: src/__tests__/features/${featureName.toLowerCase()}.standalone.test.ts`);
      if (includeComponent) console.log(`  - Component: src/components/${featureName}.tsx`);
      return;
    }

    // Generate files
    const files = [];
    
    if (includeApi) {
      const apiCode = await generateApiRouteCode(featureName);
      const apiPath = path.join(process.cwd(), 'src', 'app', 'api', featureName.toLowerCase(), 'route.ts');
      files.push({ path: apiPath, content: apiCode, type: 'API route' });
    }
    
    if (includeModel) {
      const modelCode = await generateModelCode(featureName);
      const modelPath = path.join(process.cwd(), 'src', 'models', `${featureName}.ts`);
      files.push({ path: modelPath, content: modelCode, type: 'Model' });
    }
    
    if (includeTests) {
      const testCode = await generateTestCode(featureName);
      const testPath = path.join(process.cwd(), 'src', '__tests__', 'features', `${featureName.toLowerCase()}.standalone.test.ts`);
      files.push({ path: testPath, content: testCode, type: 'Test file' });
    }
    
    if (includeComponent) {
      const componentCode = await generateComponentCode(featureName);
      const componentPath = path.join(process.cwd(), 'src', 'components', `${featureName}.tsx`);
      files.push({ path: componentPath, content: componentCode, type: 'Component' });
    }

    // Create directories and files
    for (const file of files) {
      const dir = path.dirname(file.path);
      await fs.mkdir(dir, { recursive: true });
      await fs.writeFile(file.path, file.content);
      console.log(colorize(`✅ Created ${file.type}: ${path.relative(process.cwd(), file.path)}`, 'green'));
    }

    // Create checklist
    const checklist = createFeatureChecklist(featureName);
    const checklistPath = path.join(process.cwd(), 'docs', 'checklists', `${featureName.toLowerCase()}-checklist.md`);
    await fs.mkdir(path.dirname(checklistPath), { recursive: true });
    await fs.writeFile(checklistPath, checklist);
    console.log(colorize(`📋 Created checklist: ${path.relative(process.cwd(), checklistPath)}`, 'green'));

    console.log(colorize(`\n🎉 Feature '${featureName}' initialized successfully!`, 'green'));
    console.log(colorize('\nNext steps:', 'cyan'));
    console.log('1. Review the generated files and customize as needed');
    console.log('2. Update the TODO comments with actual implementation');
    console.log('3. Run tests to ensure everything works');
    console.log(`4. Check the development checklist at ${path.relative(process.cwd(), checklistPath)}`);

  } finally {
    rl.close();
  }
}

async function generateCode(type, name, options = {}) {
  console.log(colorize(`\n🔧 Generating ${type}: ${name}`, 'cyan'));
  
  if (!name) {
    console.error(colorize(`❌ ${type} name is required`, 'red'));
    return;
  }

  let code, filePath, fileType;
  
  switch (type) {
    case 'api':
      code = await generateApiRouteCode(name);
      filePath = path.join(process.cwd(), 'src', 'app', 'api', name.toLowerCase(), 'route.ts');
      fileType = 'API route';
      break;
    
    case 'model':
      code = await generateModelCode(name);
      filePath = path.join(process.cwd(), 'src', 'models', `${name}.ts`);
      fileType = 'Model';
      break;
    
    case 'test':
      code = await generateTestCode(name);
      filePath = path.join(process.cwd(), 'src', '__tests__', 'features', `${name.toLowerCase()}.standalone.test.ts`);
      fileType = 'Test file';
      break;
    
    case 'component':
      code = await generateComponentCode(name);
      filePath = path.join(process.cwd(), 'src', 'components', `${name}.tsx`);
      fileType = 'Component';
      break;
    
    default:
      console.error(colorize(`❌ Unknown generation type: ${type}`, 'red'));
      return;
  }

  if (options.dryRun) {
    console.log(colorize(`\n📋 Dry run - would create ${fileType}:`, 'yellow'));
    console.log(`  Path: ${path.relative(process.cwd(), filePath)}`);
    console.log(`  Size: ${code.length} characters`);
    return;
  }

  // Create directory and file
  const dir = path.dirname(filePath);
  await fs.mkdir(dir, { recursive: true });
  await fs.writeFile(filePath, code);
  
  console.log(colorize(`✅ Generated ${fileType}: ${path.relative(process.cwd(), filePath)}`, 'green'));
  
  if (options.verbose) {
    console.log(colorize('\nGenerated code preview:', 'blue'));
    console.log(code.substring(0, 500) + (code.length > 500 ? '...' : ''));
  }
}

async function runFeatureTest(featureName, options = {}) {
  console.log(colorize(`\n🧪 Running tests for feature: ${featureName}`, 'cyan'));
  
  const testCommand = 'node';
  const testArgs = ['scripts/test-features.js', featureName];
  
  if (options.watch) testArgs.push('--watch');
  if (options.coverage) testArgs.push('--coverage');
  if (options.verbose) testArgs.push('--verbose');
  
  const child = spawn(testCommand, testArgs, {
    stdio: 'inherit',
    shell: true,
  });
  
  child.on('close', (code) => {
    if (code === 0) {
      console.log(colorize(`\n✅ Tests completed successfully!`, 'green'));
    } else {
      console.log(colorize(`\n❌ Tests failed with exit code ${code}`, 'red'));
      process.exit(code);
    }
  });
}

async function startDevServer(options = {}) {
  console.log(colorize('\n🚀 Starting enhanced development server...', 'cyan'));
  
  // Set development environment variables
  process.env.NODE_ENV = 'development';
  process.env.DEVELOPMENT_MODE_BYPASS_AUTH = 'true';
  process.env.DEVELOPMENT_DETAILED_ERRORS = 'true';
  process.env.DEVELOPMENT_DEBUG_LOGGING = 'true';
  
  const serverCommand = 'pnpm';
  const serverArgs = ['run', 'dev'];
  
  console.log(colorize('Development mode features enabled:', 'green'));
  console.log('  ✅ Authentication bypass');
  console.log('  ✅ Detailed error messages');
  console.log('  ✅ Debug logging');
  console.log('  ✅ Development dashboard at /api/dev/dashboard');
  console.log('');
  
  const child = spawn(serverCommand, serverArgs, {
    stdio: 'inherit',
    shell: true,
    env: process.env,
  });
  
  child.on('close', (code) => {
    console.log(colorize(`\n👋 Development server stopped`, 'yellow'));
  });
}

async function healthCheck() {
  console.log(colorize('\n🏥 Running system health check...', 'cyan'));
  
  try {
    // Check if we're in a Next.js project
    const packageJsonPath = path.join(process.cwd(), 'package.json');
    const packageJson = JSON.parse(await fs.readFile(packageJsonPath, 'utf8'));
    
    if (!packageJson.dependencies?.next) {
      console.log(colorize('❌ Not in a Next.js project', 'red'));
      return;
    }
    
    console.log(colorize('✅ Next.js project detected', 'green'));
    
    // Check required directories
    const requiredDirs = ['src', 'src/app', 'src/lib', 'src/models'];
    for (const dir of requiredDirs) {
      try {
        await fs.access(path.join(process.cwd(), dir));
        console.log(colorize(`✅ Directory exists: ${dir}`, 'green'));
      } catch {
        console.log(colorize(`❌ Missing directory: ${dir}`, 'red'));
      }
    }
    
    // Check environment file
    try {
      await fs.access(path.join(process.cwd(), '.env.local'));
      console.log(colorize('✅ Environment file exists', 'green'));
    } catch {
      console.log(colorize('⚠️  No .env.local file found', 'yellow'));
    }
    
    console.log(colorize('\n🎉 Health check completed!', 'green'));
    
  } catch (error) {
    console.error(colorize(`❌ Health check failed: ${error.message}`, 'red'));
  }
}

// Code generation functions (simplified versions)
async function generateApiRouteCode(name) {
  return `import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@/lib/auth';
import { handleApiError } from '@/lib/error-handler';
import { ErrorUtils } from '@/lib/error-utils';

export async function GET(request: NextRequest) {
  try {
    const session = await auth();
    if (!session) {
      throw ErrorUtils.authentication('Authentication required', 'GET');
    }

    // TODO: Implement GET logic for ${name}
    
    return NextResponse.json({
      message: 'GET ${name} - Not implemented yet',
      timestamp: new Date().toISOString(),
    });

  } catch (error) {
    return handleApiError(error, request);
  }
}

export async function POST(request: NextRequest) {
  try {
    const session = await auth();
    if (!session) {
      throw ErrorUtils.authentication('Authentication required', 'POST');
    }

    // TODO: Implement POST logic for ${name}
    
    return NextResponse.json({
      message: 'POST ${name} - Not implemented yet',
      timestamp: new Date().toISOString(),
    });

  } catch (error) {
    return handleApiError(error, request);
  }
}`;
}

async function generateModelCode(name) {
  return `import mongoose from 'mongoose';

const ${name}Schema = new mongoose.Schema({
  name: { type: String, required: true },
  userId: { type: String, required: true },
  isActive: { type: Boolean, required: true, default: true },
  // TODO: Add more fields as needed
}, {
  timestamps: true,
});

export const ${name} = mongoose.models.${name} || mongoose.model('${name}', ${name}Schema);
export type ${name}Type = mongoose.InferSchemaType<typeof ${name}Schema>;`;
}

async function generateTestCode(name) {
  return `import { describe, it, expect, beforeEach, afterEach, jest } from '@jest/globals';
import { 
  DevTestEnvironment, 
  MockFactory, 
  FeatureTestUtils, 
  TestAssertions 
} from '../utils/dev-test-helpers';

describe('${name} Feature - Standalone Tests', () => {
  beforeEach(() => {
    DevTestEnvironment.setup({
      developmentMode: true,
      authBypass: false,
      detailedErrors: true,
    });
  });

  afterEach(() => {
    DevTestEnvironment.cleanup();
    jest.clearAllMocks();
  });

  it('should implement ${name} functionality', async () => {
    // TODO: Implement test for ${name}
    expect(true).toBe(true);
  });
});`;
}

async function generateComponentCode(name) {
  return `'use client';

import React from 'react';

interface ${name}Props {
  className?: string;
  children?: React.ReactNode;
}

export default function ${name}(props: ${name}Props) {
  return (
    <div {...props}>
      <h1>TODO: Implement ${name} component</h1>
      {/* TODO: Add component content */}
    </div>
  );
}`;
}

function createFeatureChecklist(featureName) {
  return `# ${featureName} Feature Development Checklist

## Planning
- [ ] Define ${featureName} requirements and specifications
- [ ] Create user stories and acceptance criteria
- [ ] Design database schema and relationships
- [ ] Plan API endpoints and data flow

## Implementation
- [ ] Create database model for ${featureName}
- [ ] Implement API routes with proper authentication
- [ ] Add input validation and error handling
- [ ] Create frontend components
- [ ] Implement business logic

## Testing
- [ ] Write unit tests for models and utilities
- [ ] Write API integration tests
- [ ] Write standalone feature tests
- [ ] Test authentication and authorization
- [ ] Test error scenarios and edge cases

## Quality Assurance
- [ ] Code review and refactoring
- [ ] Performance testing and optimization
- [ ] Security review
- [ ] Accessibility testing
- [ ] Cross-browser testing

## Documentation
- [ ] Update API documentation
- [ ] Write user documentation
- [ ] Update development documentation
- [ ] Create deployment notes

## Deployment
- [ ] Test in development environment
- [ ] Deploy to staging environment
- [ ] User acceptance testing
- [ ] Production deployment
- [ ] Monitor and verify functionality

Generated on: ${new Date().toISOString()}
`;
}

// Main CLI logic
async function main() {
  const args = process.argv.slice(2);
  
  if (args.length === 0) {
    printUsage();
    return;
  }
  
  const command = args[0];
  const subCommand = args[1];
  const name = args[2] || args[1];
  
  const options = {
    verbose: args.includes('--verbose') || args.includes('-v'),
    dryRun: args.includes('--dry-run'),
    watch: args.includes('--watch'),
    coverage: args.includes('--coverage'),
  };
  
  try {
    switch (command) {
      case 'init-feature':
        await initFeature(subCommand, options);
        break;
      
      case 'generate':
        await generateCode(subCommand, name, options);
        break;
      
      case 'test-feature':
        await runFeatureTest(subCommand, options);
        break;
      
      case 'dev-server':
        await startDevServer(options);
        break;
      
      case 'health-check':
        await healthCheck();
        break;
      
      case 'dashboard':
        console.log(colorize('\n🎛️  Development Dashboard', 'cyan'));
        console.log('Open your browser and navigate to:');
        console.log(colorize('  http://localhost:3000/api/dev/dashboard', 'blue'));
        break;
      
      default:
        console.error(colorize(`❌ Unknown command: ${command}`, 'red'));
        printUsage();
        process.exit(1);
    }
  } catch (error) {
    console.error(colorize(`❌ Error: ${error.message}`, 'red'));
    if (options.verbose) {
      console.error(error.stack);
    }
    process.exit(1);
  }
}

if (require.main === module) {
  main();
}

module.exports = {
  initFeature,
  generateCode,
  runFeatureTest,
  startDevServer,
  healthCheck,
};
