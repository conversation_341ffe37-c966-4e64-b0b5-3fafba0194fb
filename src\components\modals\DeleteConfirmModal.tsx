'use client';

import React from 'react';
import { Modal } from '@/components/ui/Modal';
import { Button } from '@/components/ui/Button';
import { ExclamationTriangleIcon } from '@heroicons/react/24/outline';

interface DeleteConfirmModalProps {
  isOpen: boolean;
  onClose: () => void;
  onConfirm: () => void;
  itemName: string;
  itemType: 'file' | 'folder';
}

export const DeleteConfirmModal: React.FC<DeleteConfirmModalProps> = ({
  isOpen,
  onClose,
  onConfirm,
  itemName,
  itemType,
}) => {
  const [loading, setLoading] = React.useState(false);

  const handleConfirm = async () => {
    setLoading(true);
    try {
      await onConfirm();
    } finally {
      setLoading(false);
    }
  };

  return (
    <Modal
      isOpen={isOpen}
      onClose={onClose}
      title={`Delete ${itemType === 'file' ? 'File' : 'Folder'}`}
      size="sm"
    >
      <div className="space-y-4">
        {/* Warning Icon */}
        <div className="flex items-center justify-center">
          <div className="p-3 bg-error/10 rounded-full">
            <ExclamationTriangleIcon className="h-8 w-8 text-error" />
          </div>
        </div>

        {/* Message */}
        <div className="text-center space-y-2">
          <p className="text-text-primary font-medium">
            Are you sure you want to delete this {itemType}?
          </p>
          <p className="text-text-secondary text-sm">
            <span className="font-medium">"{itemName}"</span>
          </p>
          <p className="text-text-secondary text-sm">
            {itemType === 'folder' 
              ? 'This will permanently delete the folder and all its contents. This action cannot be undone.'
              : 'This action cannot be undone.'
            }
          </p>
        </div>

        {/* Actions */}
        <div className="flex space-x-3 pt-4">
          <Button
            variant="secondary"
            onClick={onClose}
            className="flex-1"
            disabled={loading}
          >
            Cancel
          </Button>
          <Button
            variant="danger"
            onClick={handleConfirm}
            className="flex-1"
            loading={loading}
          >
            Delete {itemType === 'file' ? 'File' : 'Folder'}
          </Button>
        </div>
      </div>
    </Modal>
  );
};
