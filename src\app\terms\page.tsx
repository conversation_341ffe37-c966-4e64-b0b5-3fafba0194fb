import Link from 'next/link';
import { But<PERSON> } from '@/components/ui/Button';
import { ThemeToggle } from '@/components/ui/ThemeToggle';

export default function TermsPage() {
  return (
    <div className="min-h-screen bg-background flex flex-col">
      {/* Header */}
      <header className="border-b border-divider">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-6">
            <Link href="/" className="flex items-center space-x-2">
              <div className="w-8 h-8 bg-accent rounded-lg flex items-center justify-center">
                <span className="text-white font-bold text-lg">D</span>
              </div>
              <span className="text-2xl font-bold text-text-primary">Drivn</span>
            </Link>
            <div className="flex items-center space-x-4">
              <ThemeToggle />
              <Link href="/signup">
                <Button variant="ghost">Back to Signup</Button>
              </Link>
            </div>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <div className="flex-1 px-4 sm:px-6 lg:px-8 py-12">
        <div className="max-w-4xl mx-auto">
          <div className="text-center mb-12">
            <h1 className="text-4xl font-bold text-text-primary mb-4">
              Terms of Service
            </h1>
            <p className="text-lg text-text-secondary">
              Last updated: {new Date().toLocaleDateString()}
            </p>
          </div>

          <div className="prose prose-lg max-w-none">
            <div className="space-y-8 text-text-primary">
              <section>
                <h2 className="text-2xl font-semibold mb-4">1. Acceptance of Terms</h2>
                <p className="text-text-secondary leading-relaxed">
                  By accessing and using Drivn ("the Service"), you accept and agree to be bound by the terms and provision of this agreement. If you do not agree to abide by the above, please do not use this service.
                </p>
              </section>

              <section>
                <h2 className="text-2xl font-semibold mb-4">2. Description of Service</h2>
                <p className="text-text-secondary leading-relaxed">
                  Drivn is a cloud storage platform that allows users to store, manage, and share files securely. The service includes file upload, download, sharing, and management capabilities.
                </p>
              </section>

              <section>
                <h2 className="text-2xl font-semibold mb-4">3. User Accounts</h2>
                <p className="text-text-secondary leading-relaxed">
                  To use certain features of the Service, you must register for an account. You are responsible for maintaining the confidentiality of your account credentials and for all activities that occur under your account.
                </p>
              </section>

              <section>
                <h2 className="text-2xl font-semibold mb-4">4. Acceptable Use</h2>
                <p className="text-text-secondary leading-relaxed">
                  You agree not to use the Service to upload, store, or share content that is illegal, harmful, threatening, abusive, defamatory, or otherwise objectionable. You are solely responsible for the content you upload to the Service.
                </p>
              </section>

              <section>
                <h2 className="text-2xl font-semibold mb-4">5. Privacy and Data Protection</h2>
                <p className="text-text-secondary leading-relaxed">
                  Your privacy is important to us. Please review our Privacy Policy, which also governs your use of the Service, to understand our practices.
                </p>
              </section>

              <section>
                <h2 className="text-2xl font-semibold mb-4">6. Storage Limits and File Types</h2>
                <p className="text-text-secondary leading-relaxed">
                  The Service may impose limits on file sizes, storage capacity, and supported file types. These limits may vary based on your account type and are subject to change.
                </p>
              </section>

              <section>
                <h2 className="text-2xl font-semibold mb-4">7. Intellectual Property</h2>
                <p className="text-text-secondary leading-relaxed">
                  You retain ownership of the content you upload to the Service. By uploading content, you grant us a limited license to store, process, and deliver your content as necessary to provide the Service.
                </p>
              </section>

              <section>
                <h2 className="text-2xl font-semibold mb-4">8. Service Availability</h2>
                <p className="text-text-secondary leading-relaxed">
                  We strive to maintain high availability of the Service, but we do not guarantee uninterrupted access. The Service may be temporarily unavailable for maintenance, updates, or due to technical issues.
                </p>
              </section>

              <section>
                <h2 className="text-2xl font-semibold mb-4">9. Termination</h2>
                <p className="text-text-secondary leading-relaxed">
                  We may terminate or suspend your account and access to the Service at our sole discretion, without prior notice, for conduct that we believe violates these Terms or is harmful to other users or the Service.
                </p>
              </section>

              <section>
                <h2 className="text-2xl font-semibold mb-4">10. Limitation of Liability</h2>
                <p className="text-text-secondary leading-relaxed">
                  The Service is provided "as is" without warranties of any kind. We shall not be liable for any indirect, incidental, special, consequential, or punitive damages resulting from your use of the Service.
                </p>
              </section>

              <section>
                <h2 className="text-2xl font-semibold mb-4">11. Changes to Terms</h2>
                <p className="text-text-secondary leading-relaxed">
                  We reserve the right to modify these Terms at any time. We will notify users of significant changes via email or through the Service. Continued use of the Service after changes constitutes acceptance of the new Terms.
                </p>
              </section>

              <section>
                <h2 className="text-2xl font-semibold mb-4">12. Contact Information</h2>
                <p className="text-text-secondary leading-relaxed">
                  If you have any questions about these Terms, please contact us through our support channels available in the Service.
                </p>
              </section>
            </div>
          </div>

          <div className="mt-12 text-center">
            <Link href="/signup">
              <Button>Back to Signup</Button>
            </Link>
          </div>
        </div>
      </div>
    </div>
  );
}
