/**
 * Development Authentication Utilities
 * 
 * This module provides development-specific authentication utilities
 * that bypass normal authentication when in development mode.
 * 
 * SECURITY: All functions automatically disable in production
 */

import { NextRequest } from 'next/server';
import { getToken } from 'next-auth/jwt';
import { isAuthBypassEnabled, getDev<PERSON><PERSON>, DevLogger, isDevelopmentMode } from './dev-config';
import { EdgeSession } from './auth-edge';

/**
 * Get session with development mode support
 * In development mode with bypass enabled, returns a mock session
 * Otherwise, falls back to normal JWT token validation
 */
export async function getSessionWithDevSupport(request: NextRequest): Promise<EdgeSession | null> {
  // Check if we should use development bypass
  if (isAuthBypassEnabled()) {
    DevLogger.debug('Using development authentication bypass');
    const devUser = getDevUser();
    return {
      user: devUser,
      expires: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString(),
    };
  }

  // Fall back to normal authentication
  try {
    const token = await getToken({ 
      req: request,
      secret: process.env.NEXTAUTH_SECRET 
    });

    if (!token) {
      DevLogger.debug('No JWT token found');
      return null;
    }

    DevLogger.debug('Valid JWT token found', { email: token.email });
    return {
      user: {
        id: token.sub!,
        email: token.email!,
        name: token.name!,
        image: token.picture,
        verified: token.verified as boolean || false,
        allowPlatformS3: token.allowPlatformS3 as boolean || false,
      },
      expires: new Date(token.exp! * 1000).toISOString(),
    };
  } catch (error) {
    DevLogger.error('Session validation error', error);
    return null;
  }
}

/**
 * Check if user has admin access with development support
 */
export function isAdminUserWithDevSupport(email: string): boolean {
  // In development mode, allow dev user to be admin
  if (isAuthBypassEnabled()) {
    const devUser = getDevUser();
    if (email === devUser.email) {
      DevLogger.debug('Granting admin access to development user');
      return true;
    }
  }

  // Check normal admin emails
  const adminEmails = process.env.ADMIN_EMAILS?.split(',') || [];
  return adminEmails.includes(email);
}

/**
 * Validate admin access with development support
 */
export async function validateAdminAccessWithDevSupport(request: NextRequest): Promise<{
  isValid: boolean;
  isAdmin: boolean;
  session: EdgeSession | null;
}> {
  const session = await getSessionWithDevSupport(request);
  
  if (!session) {
    return {
      isValid: false,
      isAdmin: false,
      session: null,
    };
  }

  const isAdmin = isAdminUserWithDevSupport(session.user.email);

  return {
    isValid: true,
    isAdmin,
    session,
  };
}

/**
 * Development-aware route protection check
 */
export function shouldBypassRouteProtection(pathname: string): boolean {
  if (!isAuthBypassEnabled()) {
    return false;
  }

  // List of routes that should still require authentication even in dev mode
  const alwaysProtectedRoutes = [
    // Add any routes that should never be bypassed
  ];

  const isAlwaysProtected = alwaysProtectedRoutes.some(route => 
    pathname.startsWith(route)
  );

  if (isAlwaysProtected) {
    DevLogger.debug(`Route ${pathname} is always protected, not bypassing`);
    return false;
  }

  DevLogger.debug(`Bypassing route protection for ${pathname}`);
  return true;
}

/**
 * Create development session for API testing
 */
export function createDevSession(overrides?: Partial<EdgeSession['user']>): EdgeSession {
  if (!isDevelopmentMode()) {
    throw new Error('Development session creation is only available in development mode');
  }

  const devUser = getDevUser();
  return {
    user: { ...devUser, ...overrides },
    expires: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString(),
  };
}

/**
 * Development authentication middleware wrapper
 */
export function withDevAuth<T extends any[]>(
  handler: (session: EdgeSession, ...args: T) => Promise<any>
) {
  return async (...args: T): Promise<any> => {
    if (isAuthBypassEnabled()) {
      const devSession = createDevSession();
      DevLogger.debug('Using development session for handler');
      return handler(devSession, ...args);
    }

    // In production or when bypass is disabled, this wrapper does nothing
    // The handler should implement its own authentication
    throw new Error('Authentication required - development bypass is disabled');
  };
}

/**
 * Development API route wrapper that provides automatic authentication
 */
export function withDevApiAuth(
  handler: (request: NextRequest, session: EdgeSession) => Promise<Response>
) {
  return async (request: NextRequest): Promise<Response> => {
    const session = await getSessionWithDevSupport(request);
    
    if (!session) {
      return new Response(
        JSON.stringify({ error: 'Authentication required' }),
        { 
          status: 401,
          headers: { 'Content-Type': 'application/json' }
        }
      );
    }

    DevLogger.debug('API request authenticated', { 
      email: session.user.email,
      method: request.method,
      url: request.url 
    });

    return handler(request, session);
  };
}

/**
 * Development testing utilities
 */
export class DevAuthUtils {
  /**
   * Create a mock request with development authentication
   */
  static createAuthenticatedRequest(
    url: string, 
    options: RequestInit = {},
    userOverrides?: Partial<EdgeSession['user']>
  ): NextRequest {
    if (!isDevelopmentMode()) {
      throw new Error('Mock request creation is only available in development mode');
    }

    const request = new NextRequest(url, options);
    
    // In a real implementation, you might set cookies or headers here
    // For now, we rely on the getSessionWithDevSupport function
    // to return the development session
    
    return request;
  }

  /**
   * Test authentication flow
   */
  static async testAuthFlow(request: NextRequest): Promise<{
    hasSession: boolean;
    isAdmin: boolean;
    user?: EdgeSession['user'];
    bypassUsed: boolean;
  }> {
    if (!isDevelopmentMode()) {
      throw new Error('Auth flow testing is only available in development mode');
    }

    const session = await getSessionWithDevSupport(request);
    const bypassUsed = isAuthBypassEnabled();
    
    return {
      hasSession: !!session,
      isAdmin: session ? isAdminUserWithDevSupport(session.user.email) : false,
      user: session?.user,
      bypassUsed,
    };
  }
}
