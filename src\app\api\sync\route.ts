import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@/lib/auth';
import { GlobalSyncManager } from '@/lib/sync';
import { S3Service } from '@/lib/s3';
import { decryptS3Credentials } from '@/lib/encryption';

export async function POST(request: NextRequest) {
  try {
    const session = await auth();
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Get S3 credentials
    const s3Cookie = request.cookies.get('s3-credentials');
    
    if (!s3Cookie) {
      return NextResponse.json(
        { error: 'No S3 credentials configured. Please configure your storage settings first.' },
        { status: 400 }
      );
    }

    let s3Credentials;
    try {
      s3Credentials = decryptS3Credentials(s3Cookie.value);
    } catch (error) {
      return NextResponse.json(
        { error: 'Invalid S3 credentials. Please reconfigure your storage settings.' },
        { status: 400 }
      );
    }

    // Check if sync is already in progress
    const syncManager = GlobalSyncManager.getInstance();
    if (syncManager.isSyncInProgress(session.user.id)) {
      return NextResponse.json(
        { error: 'Sync already in progress. Please wait for the current sync to complete.' },
        { status: 409 }
      );
    }

    // Validate S3 credentials before starting sync
    const s3Service = new S3Service(s3Credentials);
    const isValid = await s3Service.validateCredentials();
    
    if (!isValid) {
      return NextResponse.json(
        { error: 'Invalid S3 credentials. Please check your storage configuration.' },
        { status: 400 }
      );
    }

    // Start sync
    const result = await syncManager.syncUser(session.user.id, s3Credentials);

    return NextResponse.json({
      message: 'Sync completed successfully',
      result: {
        added: result.added,
        updated: result.updated,
        deleted: result.deleted,
        errors: result.errors
      }
    });

  } catch (error) {
    console.error('Sync error:', error);
    
    return NextResponse.json(
      { 
        error: 'Sync failed', 
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}

export async function GET(request: NextRequest) {
  try {
    const session = await auth();
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const syncManager = GlobalSyncManager.getInstance();
    const isInProgress = syncManager.isSyncInProgress(session.user.id);

    return NextResponse.json({
      syncInProgress: isInProgress
    });

  } catch (error) {
    console.error('Sync status check error:', error);
    
    return NextResponse.json(
      { error: 'Failed to check sync status' },
      { status: 500 }
    );
  }
}
