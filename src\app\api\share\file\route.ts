import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';
import { auth } from '@/lib/auth';
import { File } from '@/models/File';
import { Share } from '@/models/Share';
import { sendShareNotificationEmail } from '@/lib/email';
import connectDB from '@/lib/mongodb';
import bcrypt from 'bcryptjs';

const shareFileSchema = z.object({
  fileId: z.string().min(1, 'File ID is required'),
  permission: z.enum(['view', 'edit']).default('view'),
  expiresAt: z.string().datetime().optional(),
  password: z.string().optional(),
  allowDownload: z.boolean().default(true),
  maxAccessCount: z.number().min(1).optional(),
  sharedWithEmails: z.array(z.string().email()).optional(),
});

export async function POST(request: NextRequest) {
  try {
    const session = await auth();
    if (!session) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const body = await request.json();
    const {
      fileId,
      permission,
      expiresAt,
      password,
      allowDownload,
      maxAccessCount,
      sharedWithEmails,
    } = shareFileSchema.parse(body);

    await connectDB();

    // Verify file ownership
    const file = await File.findOne({
      _id: fileId,
      userId: session.user.id,
    });

    if (!file) {
      return NextResponse.json(
        { error: 'File not found or access denied' },
        { status: 404 }
      );
    }

    // Check if share already exists for this file
    const existingShare = await Share.findOne({
      fileId,
      userId: session.user.id,
      isActive: true,
    });

    if (existingShare) {
      return NextResponse.json(
        { error: 'File is already shared. Please revoke the existing share first.' },
        { status: 400 }
      );
    }

    // Generate share ID
    const shareId = Share.generateShareId();

    // Hash password if provided
    let hashedPassword;
    if (password) {
      hashedPassword = await bcrypt.hash(password, 12);
    }

    // Create share
    const share = await Share.create({
      fileId,
      userId: session.user.id,
      shareId,
      permission,
      expiresAt: expiresAt ? new Date(expiresAt) : undefined,
      password: hashedPassword,
      allowDownload,
      maxAccessCount,
      sharedWithEmails: sharedWithEmails || [],
      isActive: true,
    });

    // Send email notifications if emails are provided
    if (sharedWithEmails && sharedWithEmails.length > 0) {
      const shareUrl = share.getShareUrl();
      
      for (const email of sharedWithEmails) {
        try {
          await sendShareNotificationEmail(
            email,
            file.name,
            shareUrl,
            session.user.name || 'Someone'
          );
        } catch (emailError) {
          console.error(`Failed to send share notification to ${email}:`, emailError);
        }
      }
    }

    return NextResponse.json({
      message: 'File shared successfully',
      share: {
        id: share._id,
        shareId: share.shareId,
        shareUrl: share.getShareUrl(),
        permission: share.permission,
        expiresAt: share.expiresAt,
        allowDownload: share.allowDownload,
        maxAccessCount: share.maxAccessCount,
        accessCount: share.accessCount,
        sharedWithEmails: share.sharedWithEmails,
        createdAt: share.createdAt,
      },
    }, { status: 201 });

  } catch (error) {
    console.error('File sharing error:', error);
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Validation error', details: error.issues },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: 'Failed to share file' },
      { status: 500 }
    );
  }
}

export async function GET(request: NextRequest) {
  try {
    const session = await auth();
    if (!session) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const { searchParams } = new URL(request.url);
    const fileId = searchParams.get('fileId');

    await connectDB();

    const query: any = { userId: session.user.id, isActive: true };
    if (fileId) {
      query.fileId = fileId;
    }

    const shares = await Share.find(query).sort({ createdAt: -1 });

    // Get file details for each share
    const sharesWithFiles = await Promise.all(
      shares.map(async (share) => {
        const file = await (File as any).findById(share.fileId);
        return {
          id: share._id,
          shareId: share.shareId,
          shareUrl: share.getShareUrl(),
          permission: share.permission,
          expiresAt: share.expiresAt,
          allowDownload: share.allowDownload,
          maxAccessCount: share.maxAccessCount,
          accessCount: share.accessCount,
          sharedWithEmails: share.sharedWithEmails,
          createdAt: share.createdAt,
          lastAccessedAt: share.lastAccessedAt,
          file: file ? {
            id: file._id,
            name: file.name,
            originalName: file.originalName,
            size: file.size,
            mimeType: file.mimeType,
          } : null,
        };
      })
    );

    return NextResponse.json({
      shares: sharesWithFiles,
    });

  } catch (error) {
    console.error('Shares fetch error:', error);
    return NextResponse.json(
      { error: 'Failed to fetch shares' },
      { status: 500 }
    );
  }
}
