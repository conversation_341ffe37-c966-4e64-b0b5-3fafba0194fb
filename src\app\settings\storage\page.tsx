'use client';

import React from 'react';
import { useSession } from 'next-auth/react';
import { Button } from '@/components/ui/Button';
import { Input } from '@/components/ui/Input';
import { toast } from '@/components/ui/Toast';
import { CloudIcon, CheckCircleIcon, XCircleIcon } from '@heroicons/react/24/outline';
import SyncManager from '@/components/sync/SyncManager';

export default function StorageSettingsPage() {
  const { data: session } = useSession();
  const [loading, setLoading] = React.useState(false);
  const [hasCredentials, setHasCredentials] = React.useState(false);
  const [formData, setFormData] = React.useState({
    endpoint: '',
    region: '',
    accessKeyId: '',
    secretAccessKey: '',
    bucket: '',
  });

  React.useEffect(() => {
    checkS3Status();
  }, []);

  const checkS3Status = async () => {
    try {
      const response = await fetch('/api/s3/configure');
      if (response.ok) {
        const data = await response.json();
        setHasCredentials(data.hasCredentials);
      }
    } catch (error) {
      console.error('Failed to check S3 status:', error);
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);

    try {
      const response = await fetch('/api/s3/configure', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(formData),
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || 'Failed to configure S3 credentials');
      }

      toast.success('S3 credentials configured successfully!');
      setHasCredentials(true);
      setFormData({
        endpoint: '',
        region: '',
        accessKeyId: '',
        secretAccessKey: '',
        bucket: '',
      });
    } catch (error) {
      console.error('S3 configuration error:', error);
      toast.error(error instanceof Error ? error.message : 'Failed to configure S3 credentials');
    } finally {
      setLoading(false);
    }
  };

  const handleReset = async () => {
    if (!confirm('Are you sure you want to reset your S3 credentials? This will remove your current storage configuration.')) {
      return;
    }

    setLoading(true);

    try {
      const response = await fetch('/api/s3/reset', {
        method: 'POST',
      });

      if (!response.ok) {
        throw new Error('Failed to reset S3 credentials');
      }

      toast.success('S3 credentials reset successfully');
      setHasCredentials(false);
    } catch (error) {
      console.error('S3 reset error:', error);
      toast.error('Failed to reset S3 credentials');
    } finally {
      setLoading(false);
    }
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setFormData(prev => ({
      ...prev,
      [e.target.name]: e.target.value,
    }));
  };

  return (
    <div className="max-w-4xl mx-auto p-6 space-y-8">
      {/* Header */}
      <div>
        <h1 className="text-2xl font-bold text-text-primary">Storage Settings</h1>
        <p className="text-text-secondary">
          Configure your S3-compatible storage provider
        </p>
      </div>

      {/* Current Status */}
      <div className="bg-background border border-divider rounded-lg">
        <div className="p-6 border-b border-divider">
          <div className="flex items-center space-x-3">
            <div className="p-2 bg-accent/10 rounded-lg">
              <CloudIcon className="h-5 w-5 text-accent" />
            </div>
            <div>
              <h2 className="text-lg font-semibold text-text-primary">Storage Status</h2>
              <p className="text-sm text-text-secondary">
                Current storage configuration status
              </p>
            </div>
          </div>
        </div>

        <div className="p-6 space-y-4">
          <div className="flex items-center justify-between">
            <div>
              <p className="font-medium text-text-primary">S3 Credentials</p>
              <p className="text-sm text-text-secondary">
                Your personal S3-compatible storage credentials
              </p>
            </div>
            <span
              className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium ${
                hasCredentials
                  ? 'bg-success/10 text-success'
                  : 'bg-warning/10 text-warning'
              }`}
            >
              {hasCredentials ? (
                <>
                  <CheckCircleIcon className="h-4 w-4 mr-1" />
                  Configured
                </>
              ) : (
                <>
                  <XCircleIcon className="h-4 w-4 mr-1" />
                  Not Configured
                </>
              )}
            </span>
          </div>

          <div className="flex items-center justify-between">
            <div>
              <p className="font-medium text-text-primary">Platform Storage Fallback</p>
              <p className="text-sm text-text-secondary">
                Permission to use platform storage when personal credentials are not available
              </p>
            </div>
            <span
              className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium ${
                session?.user?.allowPlatformS3
                  ? 'bg-success/10 text-success'
                  : 'bg-error/10 text-error'
              }`}
            >
              {session?.user?.allowPlatformS3 ? 'Enabled' : 'Disabled'}
            </span>
          </div>

          {hasCredentials && (
            <div className="mt-4 flex justify-end">
              <Button variant="danger" onClick={handleReset} loading={loading}>
                Reset S3 Credentials
              </Button>
            </div>
          )}
        </div>
      </div>

      {/* S3 Configuration Form */}
      <div className="bg-background border border-divider rounded-lg">
        <div className="p-6 border-b border-divider">
          <h2 className="text-lg font-semibold text-text-primary">
            {hasCredentials ? 'Update' : 'Configure'} S3 Credentials
          </h2>
          <p className="text-sm text-text-secondary mt-1">
            Enter your S3-compatible storage provider credentials. Supports AWS S3, Wasabi, Backblaze B2, MinIO, and other S3-compatible services.
          </p>
        </div>

        <form onSubmit={handleSubmit} className="p-6 space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <Input
              name="endpoint"
              label="Endpoint URL"
              type="url"
              value={formData.endpoint}
              onChange={handleInputChange}
              required
              placeholder="https://s3.amazonaws.com"
              helperText="The S3 endpoint URL for your storage provider"
            />
            <Input
              name="region"
              label="Region"
              type="text"
              value={formData.region}
              onChange={handleInputChange}
              required
              placeholder="us-east-1"
              helperText="The region where your bucket is located"
            />
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <Input
              name="accessKeyId"
              label="Access Key ID"
              type="text"
              value={formData.accessKeyId}
              onChange={handleInputChange}
              required
              placeholder="Your access key ID"
              helperText="Your S3 access key ID"
            />
            <Input
              name="secretAccessKey"
              label="Secret Access Key"
              type="password"
              value={formData.secretAccessKey}
              onChange={handleInputChange}
              required
              placeholder="Your secret access key"
              helperText="Your S3 secret access key"
            />
          </div>

          <div>
            <Input
              name="bucket"
              label="Bucket Name (Optional)"
              type="text"
              value={formData.bucket}
              onChange={handleInputChange}
              placeholder="my-drivn-bucket"
              helperText="Leave empty to use default bucket name"
            />
          </div>

          <div className="bg-accent/5 border border-accent/20 rounded-lg p-4">
            <h3 className="font-medium text-text-primary mb-2">Security Notice</h3>
            <ul className="text-sm text-text-secondary space-y-1">
              <li>• Your credentials are encrypted and stored securely</li>
              <li>• Credentials are never exposed to the frontend</li>
              <li>• Only you can access files stored with your credentials</li>
              <li>• Credentials expire after 7 days and need to be refreshed</li>
            </ul>
          </div>

          <div className="flex justify-end space-x-3">
            <Button
              type="button"
              variant="secondary"
              onClick={() => setFormData({
                endpoint: '',
                region: '',
                accessKeyId: '',
                secretAccessKey: '',
                bucket: '',
              })}
            >
              Clear Form
            </Button>
            <Button type="submit" loading={loading}>
              {hasCredentials ? 'Update' : 'Configure'} Credentials
            </Button>
          </div>
        </form>
      </div>

      {/* Popular Providers */}
      <div className="bg-background border border-divider rounded-lg">
        <div className="p-6 border-b border-divider">
          <h2 className="text-lg font-semibold text-text-primary">Popular S3-Compatible Providers</h2>
          <p className="text-sm text-text-secondary mt-1">
            Examples of supported storage providers and their typical endpoints
          </p>
        </div>

        <div className="p-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="p-4 border border-divider rounded-lg">
              <h3 className="font-medium text-text-primary">AWS S3</h3>
              <p className="text-sm text-text-secondary mt-1">
                Endpoint: https://s3.amazonaws.com<br />
                Region: us-east-1, eu-west-1, etc.
              </p>
            </div>
            <div className="p-4 border border-divider rounded-lg">
              <h3 className="font-medium text-text-primary">Wasabi</h3>
              <p className="text-sm text-text-secondary mt-1">
                Endpoint: https://s3.wasabisys.com<br />
                Region: us-east-1, eu-central-1, etc.
              </p>
            </div>
            <div className="p-4 border border-divider rounded-lg">
              <h3 className="font-medium text-text-primary">Backblaze B2</h3>
              <p className="text-sm text-text-secondary mt-1">
                Endpoint: https://s3.us-west-002.backblazeb2.com<br />
                Region: us-west-002, eu-central-003, etc.
              </p>
            </div>
            <div className="p-4 border border-divider rounded-lg">
              <h3 className="font-medium text-text-primary">MinIO</h3>
              <p className="text-sm text-text-secondary mt-1">
                Endpoint: https://your-minio-server.com<br />
                Region: us-east-1 (default)
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* CORS Configuration */}
      <div className="bg-background border border-divider rounded-lg">
        <div className="p-6 border-b border-divider">
          <h2 className="text-lg font-semibold text-text-primary">CORS Configuration</h2>
          <p className="text-sm text-text-secondary mt-1">
            Required CORS configuration for your S3 bucket to work with Drivn
          </p>
        </div>

        <div className="p-6">
          <div className="space-y-4">
            <p className="text-sm text-text-secondary">
              Add the following CORS configuration to your S3 bucket to enable file uploads and downloads from Drivn:
            </p>

            <div className="bg-secondary rounded-lg p-4">
              <div className="flex items-center justify-between mb-2">
                <span className="text-sm font-medium text-text-primary">CORS Configuration XML</span>
                <button
                  onClick={() => {
                    const corsConfig = `<?xml version="1.0" encoding="UTF-8"?>
<CORSConfiguration xmlns="http://s3.amazonaws.com/doc/2006-03-01/">
  <CORSRule>
    <AllowedOrigin>*</AllowedOrigin>
    <AllowedMethod>GET</AllowedMethod>
    <AllowedMethod>PUT</AllowedMethod>
    <AllowedMethod>POST</AllowedMethod>
    <AllowedMethod>DELETE</AllowedMethod>
    <AllowedMethod>HEAD</AllowedMethod>
    <AllowedHeader>*</AllowedHeader>
    <ExposeHeader>ETag</ExposeHeader>
    <ExposeHeader>x-amz-meta-custom-header</ExposeHeader>
  </CORSRule>
</CORSConfiguration>`;
                    navigator.clipboard.writeText(corsConfig);
                    toast.success('CORS configuration copied to clipboard!');
                  }}
                  className="px-3 py-1 text-xs bg-accent text-white rounded hover:bg-accent/90 transition-colors"
                >
                  Copy
                </button>
              </div>
              <pre className="text-xs text-text-primary overflow-x-auto whitespace-pre-wrap">
{`<?xml version="1.0" encoding="UTF-8"?>
<CORSConfiguration xmlns="http://s3.amazonaws.com/doc/2006-03-01/">
  <CORSRule>
    <AllowedOrigin>*</AllowedOrigin>
    <AllowedMethod>GET</AllowedMethod>
    <AllowedMethod>PUT</AllowedMethod>
    <AllowedMethod>POST</AllowedMethod>
    <AllowedMethod>DELETE</AllowedMethod>
    <AllowedMethod>HEAD</AllowedMethod>
    <AllowedHeader>*</AllowedHeader>
    <ExposeHeader>ETag</ExposeHeader>
    <ExposeHeader>x-amz-meta-custom-header</ExposeHeader>
  </CORSRule>
</CORSConfiguration>`}
              </pre>
            </div>

            <div className="bg-warning/10 border border-warning/20 rounded-lg p-4">
              <div className="flex items-start space-x-3">
                <div className="flex-shrink-0">
                  <svg className="h-5 w-5 text-warning" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                  </svg>
                </div>
                <div>
                  <h3 className="text-sm font-medium text-warning">Security Note</h3>
                  <p className="text-sm text-text-secondary mt-1">
                    The configuration above allows all origins (*) for simplicity. For production use, consider restricting AllowedOrigin to your specific domain(s) for better security.
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* S3 Synchronization */}
      {hasCredentials && <SyncManager />}
    </div>
  );
}
