/**
 * Development Test Helpers
 * 
 * Enhanced test utilities that support development mode features,
 * isolated testing, and comprehensive mocking capabilities.
 */

import { NextRequest } from 'next/server';
import { Session } from 'next-auth';
import { jest } from '@jest/globals';
import { DevUser } from '@/lib/dev-config';
import { EdgeSession } from '@/lib/auth-edge';

// Enhanced mock data with development support
export const mockDevUser: DevUser = {
  id: 'dev-user-id',
  email: '<EMAIL>',
  name: 'Development User',
  verified: true,
  allowPlatformS3: true,
};

export const mockTestUser = {
  id: '507f1f77bcf86cd799439011',
  email: '<EMAIL>',
  name: 'Test User',
  verified: true,
  allowPlatformS3: false,
};

export const mockAdminUser = {
  id: '507f1f77bcf86cd799439012',
  email: '<EMAIL>',
  name: 'Admin User',
  verified: true,
  allowPlatformS3: true,
};

// Enhanced session mocks
export const mockDevSession: EdgeSession = {
  user: mockDevUser,
  expires: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString(),
};

export const mockTestSession: Session = {
  user: mockTestUser,
  expires: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString(),
};

export const mockAdminSession: Session = {
  user: mockAdminUser,
  expires: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString(),
};

/**
 * Development-aware test environment setup
 */
export class DevTestEnvironment {
  private static originalEnv: Record<string, string | undefined> = {};

  /**
   * Setup test environment with development mode support
   */
  static setup(options: {
    developmentMode?: boolean;
    authBypass?: boolean;
    detailedErrors?: boolean;
    debugLogging?: boolean;
  } = {}) {
    // Store original environment
    this.originalEnv = { ...process.env };

    // Set test environment
    process.env.NODE_ENV = options.developmentMode ? 'development' : 'test';
    process.env.NEXTAUTH_SECRET = 'test-secret';
    process.env.MONGODB_URI = 'mongodb://localhost:27017/drivn-test';
    process.env.ENCRYPTION_KEY = 'test-encryption-key-32-characters';
    process.env.ADMIN_EMAILS = '<EMAIL>';

    // Development mode specific settings
    if (options.developmentMode) {
      process.env.DEVELOPMENT_MODE_BYPASS_AUTH = options.authBypass ? 'true' : 'false';
      process.env.DEVELOPMENT_DETAILED_ERRORS = options.detailedErrors ? 'true' : 'false';
      process.env.DEVELOPMENT_DEBUG_LOGGING = options.debugLogging ? 'true' : 'false';
      process.env.DEVELOPMENT_DEFAULT_USER_EMAIL = mockDevUser.email;
      process.env.DEVELOPMENT_DEFAULT_USER_NAME = mockDevUser.name;
    }
  }

  /**
   * Cleanup test environment
   */
  static cleanup() {
    // Restore original environment
    Object.keys(process.env).forEach(key => {
      if (key in this.originalEnv) {
        process.env[key] = this.originalEnv[key];
      } else {
        delete process.env[key];
      }
    });
    this.originalEnv = {};
  }

  /**
   * Create isolated test environment for a specific feature
   */
  static createIsolated(feature: string, options: {
    withAuth?: boolean;
    withDatabase?: boolean;
    withS3?: boolean;
    withEmail?: boolean;
    developmentMode?: boolean;
  } = {}) {
    const testId = `${feature}-${Date.now()}`;
    
    this.setup({
      developmentMode: options.developmentMode,
      authBypass: options.withAuth === false,
      detailedErrors: true,
      debugLogging: true,
    });

    // Feature-specific database isolation
    if (options.withDatabase) {
      process.env.MONGODB_URI = `mongodb://localhost:27017/drivn-test-${testId}`;
    }

    return {
      testId,
      cleanup: () => this.cleanup(),
    };
  }
}

/**
 * Enhanced mock factories
 */
export class MockFactory {
  /**
   * Create mock request with development support
   */
  static createRequest(
    url: string,
    options: {
      method?: string;
      body?: any;
      headers?: Record<string, string>;
      session?: EdgeSession | null;
      developmentMode?: boolean;
    } = {}
  ): NextRequest {
    const headers = new Headers(options.headers || {});
    
    if (options.body) {
      headers.set('content-type', 'application/json');
    }

    const request = new NextRequest(url, {
      method: options.method || 'GET',
      headers,
      body: options.body ? JSON.stringify(options.body) : undefined,
    });

    // Mock session attachment for development mode
    if (options.session && options.developmentMode) {
      // In a real implementation, this would set appropriate cookies/headers
      (request as any).__mockSession = options.session;
    }

    return request;
  }

  /**
   * Create comprehensive database mocks
   */
  static createDatabaseMocks() {
    const mockModel = {
      find: jest.fn(),
      findOne: jest.fn(),
      findById: jest.fn(),
      findByIdAndUpdate: jest.fn(),
      findByIdAndDelete: jest.fn(),
      create: jest.fn(),
      updateOne: jest.fn(),
      deleteOne: jest.fn(),
      countDocuments: jest.fn(),
      aggregate: jest.fn(),
      sort: jest.fn(),
      skip: jest.fn(),
      limit: jest.fn(),
      populate: jest.fn(),
      select: jest.fn(),
      save: jest.fn(),
      exec: jest.fn(),
    };

    // Chain methods for query building
    const chainableMethods = ['find', 'findOne', 'aggregate', 'sort', 'skip', 'limit', 'populate', 'select'];
    chainableMethods.forEach(method => {
      mockModel[method].mockReturnValue(mockModel);
    });

    return mockModel;
  }

  /**
   * Create S3 service mocks
   */
  static createS3Mocks() {
    return {
      validateCredentials: jest.fn().mockResolvedValue(true),
      generateFileKey: jest.fn().mockReturnValue('test-key'),
      getPresignedUploadUrl: jest.fn().mockResolvedValue('https://example.com/upload'),
      getPresignedDownloadUrl: jest.fn().mockResolvedValue('https://example.com/download'),
      deleteFile: jest.fn().mockResolvedValue(undefined),
      listFiles: jest.fn().mockResolvedValue([]),
      getFileMetadata: jest.fn().mockResolvedValue({}),
    };
  }

  /**
   * Create email service mocks
   */
  static createEmailMocks() {
    return {
      sendVerificationEmail: jest.fn().mockResolvedValue(undefined),
      sendPasswordResetEmail: jest.fn().mockResolvedValue(undefined),
      sendShareNotificationEmail: jest.fn().mockResolvedValue(undefined),
      sendWelcomeEmail: jest.fn().mockResolvedValue(undefined),
    };
  }

  /**
   * Create authentication mocks with development support
   */
  static createAuthMocks(options: {
    defaultSession?: EdgeSession | null;
    developmentMode?: boolean;
    authBypass?: boolean;
  } = {}) {
    const mocks = {
      auth: jest.fn(),
      getEdgeSession: jest.fn(),
      getSessionWithDevSupport: jest.fn(),
      isAdminUser: jest.fn(),
      isAdminUserWithDevSupport: jest.fn(),
      validateAdminAccess: jest.fn(),
      validateAdminAccessWithDevSupport: jest.fn(),
    };

    // Setup default behaviors
    const session = options.defaultSession || mockTestSession;
    mocks.auth.mockResolvedValue(session);
    mocks.getEdgeSession.mockResolvedValue(session);
    mocks.getSessionWithDevSupport.mockResolvedValue(session);

    // Admin checks
    const isAdmin = session?.user?.email === mockAdminUser.email;
    mocks.isAdminUser.mockReturnValue(isAdmin);
    mocks.isAdminUserWithDevSupport.mockReturnValue(isAdmin);
    mocks.validateAdminAccess.mockResolvedValue({
      isValid: !!session,
      isAdmin,
      session,
    });
    mocks.validateAdminAccessWithDevSupport.mockResolvedValue({
      isValid: !!session,
      isAdmin,
      session,
    });

    return mocks;
  }
}

/**
 * Feature-specific test utilities
 */
export class FeatureTestUtils {
  /**
   * Test authentication feature in isolation
   */
  static async testAuthFeature(testFn: () => Promise<void>) {
    const env = DevTestEnvironment.createIsolated('auth', {
      withAuth: true,
      withDatabase: true,
      developmentMode: true,
    });

    try {
      await testFn();
    } finally {
      env.cleanup();
    }
  }

  /**
   * Test file management feature in isolation
   */
  static async testFileFeature(testFn: () => Promise<void>) {
    const env = DevTestEnvironment.createIsolated('files', {
      withAuth: true,
      withDatabase: true,
      withS3: true,
      developmentMode: true,
    });

    try {
      await testFn();
    } finally {
      env.cleanup();
    }
  }

  /**
   * Test admin feature in isolation
   */
  static async testAdminFeature(testFn: () => Promise<void>) {
    const env = DevTestEnvironment.createIsolated('admin', {
      withAuth: true,
      withDatabase: true,
      developmentMode: true,
    });

    try {
      await testFn();
    } finally {
      env.cleanup();
    }
  }

  /**
   * Test sharing feature in isolation
   */
  static async testSharingFeature(testFn: () => Promise<void>) {
    const env = DevTestEnvironment.createIsolated('sharing', {
      withAuth: true,
      withDatabase: true,
      withEmail: true,
      developmentMode: true,
    });

    try {
      await testFn();
    } finally {
      env.cleanup();
    }
  }
}

/**
 * Enhanced assertion helpers
 */
export class TestAssertions {
  static expectValidationError(response: Response, field?: string) {
    expect(response.status).toBe(400);
    if (field) {
      // Additional validation checks can be added
    }
  }

  static expectAuthError(response: Response) {
    expect(response.status).toBe(401);
  }

  static expectForbiddenError(response: Response) {
    expect(response.status).toBe(403);
  }

  static expectNotFoundError(response: Response) {
    expect(response.status).toBe(404);
  }

  static expectRateLimitError(response: Response) {
    expect(response.status).toBe(429);
  }

  static async expectErrorWithContext(response: Response, expectedCode?: string) {
    expect(response.status).toBeGreaterThanOrEqual(400);
    const data = await response.json();
    expect(data).toHaveProperty('error');
    
    if (expectedCode) {
      expect(data.code).toBe(expectedCode);
    }

    // In development mode, expect enhanced context
    if (process.env.NODE_ENV === 'development') {
      expect(data).toHaveProperty('context');
      expect(data).toHaveProperty('suggestions');
    }
  }

  static expectSuccessResponse(response: Response) {
    expect(response.status).toBeGreaterThanOrEqual(200);
    expect(response.status).toBeLessThan(300);
  }
}

/**
 * Test data generators
 */
export class TestDataGenerator {
  static generateUser(overrides: Partial<typeof mockTestUser> = {}) {
    return {
      ...mockTestUser,
      ...overrides,
      _id: overrides.id || mockTestUser.id,
    };
  }

  static generateFile(overrides: any = {}) {
    return {
      _id: '507f1f77bcf86cd799439013',
      name: 'test-file.txt',
      originalName: 'test-file.txt',
      size: 1024,
      mimeType: 'text/plain',
      s3Key: 'users/507f1f77bcf86cd799439011/test-file.txt',
      userId: '507f1f77bcf86cd799439011',
      isPublic: false,
      downloadCount: 0,
      createdAt: new Date(),
      updatedAt: new Date(),
      ...overrides,
    };
  }

  static generateFolder(overrides: any = {}) {
    return {
      _id: '507f1f77bcf86cd799439014',
      name: 'Test Folder',
      userId: '507f1f77bcf86cd799439011',
      path: '/Test Folder',
      createdAt: new Date(),
      updatedAt: new Date(),
      ...overrides,
    };
  }
}
