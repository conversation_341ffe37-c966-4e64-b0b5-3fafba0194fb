'use client';

import React from 'react';
import { Modal } from '@/components/ui/Modal';
import { Input } from '@/components/ui/Input';
import { Button } from '@/components/ui/Button';
import { toast } from '@/components/ui/Toast';
import { ShareIcon, ClipboardIcon, EnvelopeIcon } from '@heroicons/react/24/outline';
import { cn } from '@/lib/utils';

interface ShareModalProps {
  isOpen: boolean;
  onClose: () => void;
  fileId: string;
  fileName: string;
}

interface ShareSettings {
  permission: 'view' | 'edit';
  expiresAt?: string;
  password?: string;
  allowDownload: boolean;
  maxAccessCount?: number;
  sharedWithEmails: string[];
}

export const ShareModal: React.FC<ShareModalProps> = ({
  isOpen,
  onClose,
  fileId,
  fileName,
}) => {
  const [loading, setLoading] = React.useState(false);
  const [shareUrl, setShareUrl] = React.useState<string>('');
  const [settings, setSettings] = React.useState<ShareSettings>({
    permission: 'view',
    allowDownload: true,
    sharedWithEmails: [],
  });
  const [emailInput, setEmailInput] = React.useState('');

  const handleShare = async () => {
    setLoading(true);
    try {
      const response = await fetch('/api/share/file', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          fileId,
          ...settings,
        }),
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || 'Failed to share file');
      }

      const result = await response.json();
      setShareUrl(result.share.shareUrl);
      toast.success('File shared successfully!');
    } catch (error) {
      console.error('Share error:', error);
      toast.error(error instanceof Error ? error.message : 'Failed to share file');
    } finally {
      setLoading(false);
    }
  };

  const handleCopyUrl = async () => {
    try {
      await navigator.clipboard.writeText(shareUrl);
      toast.success('Share URL copied to clipboard!');
    } catch (error) {
      toast.error('Failed to copy URL');
    }
  };

  const addEmail = () => {
    if (emailInput && !settings.sharedWithEmails.includes(emailInput)) {
      setSettings(prev => ({
        ...prev,
        sharedWithEmails: [...prev.sharedWithEmails, emailInput],
      }));
      setEmailInput('');
    }
  };

  const removeEmail = (email: string) => {
    setSettings(prev => ({
      ...prev,
      sharedWithEmails: prev.sharedWithEmails.filter(e => e !== email),
    }));
  };

  const handleClose = () => {
    setShareUrl('');
    setSettings({
      permission: 'view',
      allowDownload: true,
      sharedWithEmails: [],
    });
    setEmailInput('');
    onClose();
  };

  return (
    <Modal
      isOpen={isOpen}
      onClose={handleClose}
      title={`Share "${fileName}"`}
      size="lg"
    >
      <div className="space-y-6">
        {!shareUrl ? (
          <>
            {/* Permission Settings */}
            <div>
              <label className="block text-sm font-medium text-text-primary mb-3">
                Permission Level
              </label>
              <div className="grid grid-cols-2 gap-3">
                <button
                  onClick={() => setSettings(prev => ({ ...prev, permission: 'view' }))}
                  className={cn(
                    'p-3 rounded-lg border text-left transition-colors',
                    settings.permission === 'view'
                      ? 'border-accent bg-accent/10 text-accent'
                      : 'border-divider hover:border-accent/50'
                  )}
                >
                  <div className="font-medium">View Only</div>
                  <div className="text-sm text-text-secondary">Can view and download</div>
                </button>
                <button
                  onClick={() => setSettings(prev => ({ ...prev, permission: 'edit' }))}
                  className={cn(
                    'p-3 rounded-lg border text-left transition-colors',
                    settings.permission === 'edit'
                      ? 'border-accent bg-accent/10 text-accent'
                      : 'border-divider hover:border-accent/50'
                  )}
                >
                  <div className="font-medium">Can Edit</div>
                  <div className="text-sm text-text-secondary">Can view, download, and edit</div>
                </button>
              </div>
            </div>

            {/* Access Settings */}
            <div className="space-y-4">
              <h3 className="text-sm font-medium text-text-primary">Access Settings</h3>
              
              {/* Allow Download */}
              <label className="flex items-center space-x-3">
                <input
                  type="checkbox"
                  checked={settings.allowDownload}
                  onChange={(e) => setSettings(prev => ({ ...prev, allowDownload: e.target.checked }))}
                  className="rounded border-divider text-accent focus:ring-accent"
                />
                <span className="text-sm text-text-primary">Allow downloads</span>
              </label>

              {/* Expiration Date */}
              <div>
                {mounted && (
                  <Input
                    type="datetime-local"
                    label="Expiration Date (Optional)"
                    value={settings.expiresAt || ''}
                    onChange={(e) => setSettings(prev => ({ ...prev, expiresAt: e.target.value }))}
                    helperText="Leave empty for no expiration"
                  />
                )}
              </div>

              {/* Password Protection */}
              <div>
                <Input
                  type="password"
                  label="Password Protection (Optional)"
                  value={settings.password || ''}
                  onChange={(e) => setSettings(prev => ({ ...prev, password: e.target.value }))}
                  placeholder="Enter password to protect this share"
                  helperText="Leave empty for no password protection"
                />
              </div>

              {/* Max Access Count */}
              <div>
                <Input
                  type="number"
                  label="Maximum Access Count (Optional)"
                  value={settings.maxAccessCount || ''}
                  onChange={(e) => setSettings(prev => ({ 
                    ...prev, 
                    maxAccessCount: e.target.value ? parseInt(e.target.value) : undefined 
                  }))}
                  placeholder="Unlimited"
                  helperText="Maximum number of times this file can be accessed"
                  min="1"
                />
              </div>
            </div>

            {/* Email Sharing */}
            <div>
              <label className="block text-sm font-medium text-text-primary mb-3">
                Share with Specific People (Optional)
              </label>
              <div className="flex space-x-2 mb-3">
                <Input
                  type="email"
                  placeholder="Enter email address"
                  value={emailInput}
                  onChange={(e) => setEmailInput(e.target.value)}
                  onKeyPress={(e) => e.key === 'Enter' && addEmail()}
                  className="flex-1"
                />
                <Button onClick={addEmail} disabled={!emailInput}>
                  Add
                </Button>
              </div>
              
              {settings.sharedWithEmails.length > 0 && (
                <div className="space-y-2">
                  {settings.sharedWithEmails.map((email) => (
                    <div key={email} className="flex items-center justify-between p-2 bg-secondary rounded">
                      <span className="text-sm text-text-primary">{email}</span>
                      <button
                        onClick={() => removeEmail(email)}
                        className="text-error hover:text-error/80 text-sm"
                      >
                        Remove
                      </button>
                    </div>
                  ))}
                </div>
              )}
            </div>

            {/* Actions */}
            <div className="flex justify-end space-x-3">
              <Button
                variant="secondary"
                onClick={handleClose}
              >
                Cancel
              </Button>
              <Button
                onClick={handleShare}
                loading={loading}
              >
                <ShareIcon className="h-4 w-4 mr-2" />
                {loading ? 'Creating...' : 'Create Share Link'}
              </Button>
            </div>
          </>
        ) : (
          <>
            {/* Share URL Display */}
            <div>
              <label className="block text-sm font-medium text-text-primary mb-2">
                Share URL
              </label>
              <div className="flex space-x-2">
                <Input
                  value={shareUrl}
                  readOnly
                  className="flex-1 font-mono text-sm"
                />
                <Button
                  variant="secondary"
                  onClick={handleCopyUrl}
                  size="sm"
                >
                  <ClipboardIcon className="h-4 w-4" />
                </Button>
              </div>
            </div>

            {/* Success Message */}
            <div className="p-4 bg-success/10 border border-success/20 rounded-lg">
              <div className="flex items-center">
                <ShareIcon className="h-5 w-5 text-success mr-2" />
                <span className="text-sm font-medium text-success">File shared successfully!</span>
              </div>
              <p className="text-sm text-text-secondary mt-1">
                Anyone with this link can access the file according to the permissions you set.
                {settings.sharedWithEmails.length > 0 && (
                  <> Email notifications have been sent to {settings.sharedWithEmails.length} recipient(s).</>
                )}
              </p>
            </div>

            {/* Actions */}
            <div className="flex justify-end">
              <Button onClick={handleClose}>
                Done
              </Button>
            </div>
          </>
        )}
      </div>
    </Modal>
  );
};
