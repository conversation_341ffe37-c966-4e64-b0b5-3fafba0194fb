/**
 * Standalone File Management Feature Tests
 * 
 * This test suite tests the file management feature in complete isolation,
 * including upload, download, sharing, and organization functionality.
 * Can be run independently for focused development and debugging.
 */

import { describe, it, expect, beforeEach, afterEach, jest } from '@jest/globals';
import { 
  DevTestEnvironment, 
  MockFactory, 
  FeatureTestUtils, 
  TestAssertions,
  TestDataGenerator,
  mockTestUser,
  mockTestSession
} from '../utils/dev-test-helpers';

// Mock all external dependencies
jest.mock('@/lib/mongodb');
jest.mock('@/models/File');
jest.mock('@/models/Folder');
jest.mock('@/lib/s3-service');
jest.mock('@/lib/auth');

describe('File Management Feature - Standalone Tests', () => {
  let mockFile: any;
  let mockFolder: any;
  let mockS3: any;
  let mockAuth: any;

  beforeEach(() => {
    // Setup isolated test environment
    DevTestEnvironment.setup({
      developmentMode: true,
      authBypass: false,
      detailedErrors: true,
    });

    // Create fresh mocks for each test
    mockFile = MockFactory.createDatabaseMocks();
    mockFolder = MockFactory.createDatabaseMocks();
    mockS3 = MockFactory.createS3Mocks();
    mockAuth = MockFactory.createAuthMocks({
      defaultSession: mockTestSession,
      developmentMode: true,
    });

    // Apply mocks
    require('@/models/File').File = mockFile;
    require('@/models/Folder').Folder = mockFolder;
    require('@/lib/s3-service').S3Service = mockS3;
    require('@/lib/auth').auth = mockAuth.auth;
  });

  afterEach(() => {
    DevTestEnvironment.cleanup();
    jest.clearAllMocks();
  });

  describe('File Upload', () => {
    it('should generate presigned upload URL successfully', async () => {
      await FeatureTestUtils.testFileFeature(async () => {
        // Setup mocks
        mockAuth.auth.mockResolvedValue(mockTestSession);
        mockS3.getPresignedUploadUrl.mockResolvedValue('https://example.com/upload-url');

        const { POST } = await import('@/app/api/files/upload/route');
        
        const request = MockFactory.createRequest('http://localhost:3000/api/files/upload', {
          method: 'POST',
          body: {
            fileName: 'test-document.pdf',
            fileSize: 1024000,
            mimeType: 'application/pdf',
            folderId: null,
          },
          session: mockTestSession,
        });

        const response = await POST(request);
        const data = await response.json();

        TestAssertions.expectSuccessResponse(response);
        expect(data.uploadUrl).toBe('https://example.com/upload-url');
        expect(data.fileId).toBeDefined();
        expect(mockS3.getPresignedUploadUrl).toHaveBeenCalled();
        expect(mockFile.create).toHaveBeenCalledWith(
          expect.objectContaining({
            name: 'test-document.pdf',
            size: 1024000,
            mimeType: 'application/pdf',
            userId: mockTestUser.id,
          })
        );
      });
    });

    it('should validate file size limits', async () => {
      await FeatureTestUtils.testFileFeature(async () => {
        mockAuth.auth.mockResolvedValue(mockTestSession);

        const { POST } = await import('@/app/api/files/upload/route');
        
        const request = MockFactory.createRequest('http://localhost:3000/api/files/upload', {
          method: 'POST',
          body: {
            fileName: 'huge-file.zip',
            fileSize: 1024 * 1024 * 1024 * 10, // 10GB - too large
            mimeType: 'application/zip',
          },
        });

        const response = await POST(request);
        
        await TestAssertions.expectErrorWithContext(response, 'VALIDATION_ERROR');
        expect(mockFile.create).not.toHaveBeenCalled();
      });
    });

    it('should validate file types', async () => {
      await FeatureTestUtils.testFileFeature(async () => {
        mockAuth.auth.mockResolvedValue(mockTestSession);

        const { POST } = await import('@/app/api/files/upload/route');
        
        const request = MockFactory.createRequest('http://localhost:3000/api/files/upload', {
          method: 'POST',
          body: {
            fileName: 'malicious.exe',
            fileSize: 1024,
            mimeType: 'application/x-executable',
          },
        });

        const response = await POST(request);
        
        await TestAssertions.expectErrorWithContext(response, 'VALIDATION_ERROR');
        expect(mockFile.create).not.toHaveBeenCalled();
      });
    });

    it('should handle S3 service errors gracefully', async () => {
      await FeatureTestUtils.testFileFeature(async () => {
        mockAuth.auth.mockResolvedValue(mockTestSession);
        mockS3.getPresignedUploadUrl.mockRejectedValue(new Error('S3 service unavailable'));

        const { POST } = await import('@/app/api/files/upload/route');
        
        const request = MockFactory.createRequest('http://localhost:3000/api/files/upload', {
          method: 'POST',
          body: {
            fileName: 'test.pdf',
            fileSize: 1024,
            mimeType: 'application/pdf',
          },
        });

        const response = await POST(request);
        
        await TestAssertions.expectErrorWithContext(response);
        expect(response.status).toBe(503); // Service unavailable
      });
    });
  });

  describe('File Download', () => {
    it('should generate presigned download URL for file owner', async () => {
      await FeatureTestUtils.testFileFeature(async () => {
        const testFile = TestDataGenerator.generateFile({
          userId: mockTestUser.id,
          isPublic: false,
        });

        mockAuth.auth.mockResolvedValue(mockTestSession);
        mockFile.findById.mockResolvedValue(testFile);
        mockS3.getPresignedDownloadUrl.mockResolvedValue('https://example.com/download-url');

        const { GET } = await import('@/app/api/files/[id]/download/route');
        
        const request = MockFactory.createRequest(
          `http://localhost:3000/api/files/${testFile._id}/download`
        );

        const response = await GET(request, { params: { id: testFile._id } });
        const data = await response.json();

        TestAssertions.expectSuccessResponse(response);
        expect(data.downloadUrl).toBe('https://example.com/download-url');
        expect(mockS3.getPresignedDownloadUrl).toHaveBeenCalled();
      });
    });

    it('should allow download of public files without authentication', async () => {
      await FeatureTestUtils.testFileFeature(async () => {
        const testFile = TestDataGenerator.generateFile({
          isPublic: true,
        });

        mockAuth.auth.mockResolvedValue(null); // No session
        mockFile.findById.mockResolvedValue(testFile);
        mockS3.getPresignedDownloadUrl.mockResolvedValue('https://example.com/download-url');

        const { GET } = await import('@/app/api/files/[id]/download/route');
        
        const request = MockFactory.createRequest(
          `http://localhost:3000/api/files/${testFile._id}/download`
        );

        const response = await GET(request, { params: { id: testFile._id } });
        
        TestAssertions.expectSuccessResponse(response);
      });
    });

    it('should deny access to private files for non-owners', async () => {
      await FeatureTestUtils.testFileFeature(async () => {
        const testFile = TestDataGenerator.generateFile({
          userId: 'different-user-id',
          isPublic: false,
        });

        mockAuth.auth.mockResolvedValue(mockTestSession);
        mockFile.findById.mockResolvedValue(testFile);

        const { GET } = await import('@/app/api/files/[id]/download/route');
        
        const request = MockFactory.createRequest(
          `http://localhost:3000/api/files/${testFile._id}/download`
        );

        const response = await GET(request, { params: { id: testFile._id } });
        
        TestAssertions.expectForbiddenError(response);
      });
    });

    it('should increment download count', async () => {
      await FeatureTestUtils.testFileFeature(async () => {
        const testFile = TestDataGenerator.generateFile({
          userId: mockTestUser.id,
          downloadCount: 5,
        });

        mockAuth.auth.mockResolvedValue(mockTestSession);
        mockFile.findById.mockResolvedValue(testFile);
        mockFile.findByIdAndUpdate.mockResolvedValue({ ...testFile, downloadCount: 6 });
        mockS3.getPresignedDownloadUrl.mockResolvedValue('https://example.com/download-url');

        const { GET } = await import('@/app/api/files/[id]/download/route');
        
        const request = MockFactory.createRequest(
          `http://localhost:3000/api/files/${testFile._id}/download`
        );

        await GET(request, { params: { id: testFile._id } });

        expect(mockFile.findByIdAndUpdate).toHaveBeenCalledWith(
          testFile._id,
          { $inc: { downloadCount: 1 } },
          { new: true }
        );
      });
    });
  });

  describe('File Organization', () => {
    it('should create folder successfully', async () => {
      await FeatureTestUtils.testFileFeature(async () => {
        mockAuth.auth.mockResolvedValue(mockTestSession);
        mockFolder.findOne.mockResolvedValue(null); // Folder doesn't exist
        mockFolder.create.mockResolvedValue({
          _id: 'new-folder-id',
          name: 'New Folder',
          userId: mockTestUser.id,
          path: '/New Folder',
        });

        const { POST } = await import('@/app/api/folders/route');
        
        const request = MockFactory.createRequest('http://localhost:3000/api/folders', {
          method: 'POST',
          body: {
            name: 'New Folder',
            parentId: null,
          },
        });

        const response = await POST(request);
        const data = await response.json();

        TestAssertions.expectSuccessResponse(response);
        expect(response.status).toBe(201);
        expect(data.folder.name).toBe('New Folder');
        expect(mockFolder.create).toHaveBeenCalled();
      });
    });

    it('should move file to folder', async () => {
      await FeatureTestUtils.testFileFeature(async () => {
        const testFile = TestDataGenerator.generateFile({
          userId: mockTestUser.id,
          folderId: null,
        });
        const testFolder = TestDataGenerator.generateFolder({
          userId: mockTestUser.id,
        });

        mockAuth.auth.mockResolvedValue(mockTestSession);
        mockFile.findById.mockResolvedValue(testFile);
        mockFolder.findById.mockResolvedValue(testFolder);
        mockFile.findByIdAndUpdate.mockResolvedValue({
          ...testFile,
          folderId: testFolder._id,
        });

        const { PUT } = await import('@/app/api/files/[id]/route');
        
        const request = MockFactory.createRequest(
          `http://localhost:3000/api/files/${testFile._id}`,
          {
            method: 'PUT',
            body: {
              folderId: testFolder._id,
            },
          }
        );

        const response = await PUT(request, { params: { id: testFile._id } });
        
        TestAssertions.expectSuccessResponse(response);
        expect(mockFile.findByIdAndUpdate).toHaveBeenCalledWith(
          testFile._id,
          expect.objectContaining({ folderId: testFolder._id }),
          { new: true }
        );
      });
    });

    it('should list files in folder', async () => {
      await FeatureTestUtils.testFileFeature(async () => {
        const testFolder = TestDataGenerator.generateFolder({
          userId: mockTestUser.id,
        });
        const filesInFolder = [
          TestDataGenerator.generateFile({ folderId: testFolder._id }),
          TestDataGenerator.generateFile({ folderId: testFolder._id }),
        ];

        mockAuth.auth.mockResolvedValue(mockTestSession);
        mockFile.find.mockResolvedValue(filesInFolder);

        const { GET } = await import('@/app/api/files/route');
        
        const request = MockFactory.createRequest(
          `http://localhost:3000/api/files?folderId=${testFolder._id}`
        );

        const response = await GET(request);
        const data = await response.json();

        TestAssertions.expectSuccessResponse(response);
        expect(data.files).toHaveLength(2);
        expect(mockFile.find).toHaveBeenCalledWith(
          expect.objectContaining({
            userId: mockTestUser.id,
            folderId: testFolder._id,
          })
        );
      });
    });
  });

  describe('File Deletion', () => {
    it('should delete file and S3 object', async () => {
      await FeatureTestUtils.testFileFeature(async () => {
        const testFile = TestDataGenerator.generateFile({
          userId: mockTestUser.id,
        });

        mockAuth.auth.mockResolvedValue(mockTestSession);
        mockFile.findById.mockResolvedValue(testFile);
        mockFile.findByIdAndDelete.mockResolvedValue(testFile);
        mockS3.deleteFile.mockResolvedValue(undefined);

        const { DELETE } = await import('@/app/api/files/[id]/route');
        
        const request = MockFactory.createRequest(
          `http://localhost:3000/api/files/${testFile._id}`,
          { method: 'DELETE' }
        );

        const response = await DELETE(request, { params: { id: testFile._id } });
        
        TestAssertions.expectSuccessResponse(response);
        expect(mockFile.findByIdAndDelete).toHaveBeenCalledWith(testFile._id);
        expect(mockS3.deleteFile).toHaveBeenCalledWith(testFile.s3Key);
      });
    });

    it('should handle S3 deletion errors gracefully', async () => {
      await FeatureTestUtils.testFileFeature(async () => {
        const testFile = TestDataGenerator.generateFile({
          userId: mockTestUser.id,
        });

        mockAuth.auth.mockResolvedValue(mockTestSession);
        mockFile.findById.mockResolvedValue(testFile);
        mockFile.findByIdAndDelete.mockResolvedValue(testFile);
        mockS3.deleteFile.mockRejectedValue(new Error('S3 deletion failed'));

        const { DELETE } = await import('@/app/api/files/[id]/route');
        
        const request = MockFactory.createRequest(
          `http://localhost:3000/api/files/${testFile._id}`,
          { method: 'DELETE' }
        );

        const response = await DELETE(request, { params: { id: testFile._id } });
        
        // Should still succeed even if S3 deletion fails
        TestAssertions.expectSuccessResponse(response);
        expect(mockFile.findByIdAndDelete).toHaveBeenCalled();
      });
    });
  });
});
