'use client';

import React from 'react';
import { FileCard } from '@/components/ui/FileCard';
import { SkeletonFileCard } from '@/components/ui/Skeleton';
import { Breadcrumb } from '@/components/ui/Breadcrumb';
import { useRouter, useSearchParams } from 'next/navigation';
import { toast } from '@/components/ui/Toast';
import { ShareModal } from '@/components/modals/ShareModal';
import { DeleteConfirmModal } from '@/components/modals/DeleteConfirmModal';
import { RenameModal } from '@/components/modals/RenameModal';
import { FilePreviewModal } from '@/components/modals/FilePreviewModal';
import {
  ChevronRightIcon,
  HomeIcon,
  FolderIcon,
  DocumentIcon,
  CloudArrowUpIcon,
  FolderPlusIcon,
  ViewColumnsIcon,
  Squares2X2Icon
} from '@heroicons/react/24/outline';

interface File {
  id: string;
  name: string;
  size: number;
  mimeType: string;
  createdAt: string;
  downloadCount: number;
}

interface Folder {
  id: string;
  name: string;
  createdAt: string;
  path: string;
}

interface BreadcrumbItem {
  id: string;
  name: string;
  path: string;
}

export default function FilesPage() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const currentFolderId = searchParams.get('folder') || 'root';

  const [files, setFiles] = React.useState<File[]>([]);
  const [folders, setFolders] = React.useState<Folder[]>([]);
  const [breadcrumbs, setBreadcrumbs] = React.useState<BreadcrumbItem[]>([]);
  const [loading, setLoading] = React.useState(true);
  const [viewMode, setViewMode] = React.useState<'grid' | 'list'>('grid');

  // Modal states
  const [shareModalOpen, setShareModalOpen] = React.useState(false);
  const [deleteModalOpen, setDeleteModalOpen] = React.useState(false);
  const [renameModalOpen, setRenameModalOpen] = React.useState(false);
  const [previewModalOpen, setPreviewModalOpen] = React.useState(false);
  const [selectedItem, setSelectedItem] = React.useState<{ id: string; name: string; type: 'file' | 'folder' } | null>(null);
  const [selectedFile, setSelectedFile] = React.useState<File | null>(null);

  React.useEffect(() => {
    fetchFilesAndFolders();
  }, [currentFolderId]);

  const fetchFilesAndFolders = async () => {
    setLoading(true);
    try {
      // Fetch files
      const filesRes = await fetch(`/api/files?folderId=${currentFolderId}`);
      if (!filesRes.ok) throw new Error('Failed to fetch files');
      const filesData = await filesRes.json();
      setFiles(filesData.files);

      // Fetch folders
      const foldersRes = await fetch(`/api/folders?parentFolderId=${currentFolderId === 'root' ? '' : currentFolderId}`);
      if (!foldersRes.ok) throw new Error('Failed to fetch folders');
      const foldersData = await foldersRes.json();
      setFolders(foldersData.folders);

      // Build breadcrumbs with full path support
      if (currentFolderId === 'root') {
        setBreadcrumbs([]);
      } else {
        try {
          // Fetch folder details for breadcrumbs
          const folderRes = await fetch(`/api/folder/${currentFolderId}`);
          if (folderRes.ok) {
            const folderData = await folderRes.json();
            const folder = folderData.folder;

            // Build breadcrumb path by parsing the folder path
            const pathParts = folder.path.split('/').filter(Boolean);
            const breadcrumbItems: BreadcrumbItem[] = [];

            // Build breadcrumbs from path parts
            let currentPath = '';
            for (let i = 0; i < pathParts.length; i++) {
              currentPath += '/' + pathParts[i];

              if (i === pathParts.length - 1) {
                // This is the current folder
                breadcrumbItems.push({
                  id: currentFolderId,
                  name: pathParts[i],
                  path: currentPath
                });
              } else {
                // This is a parent folder - we'd need to fetch its ID for proper navigation
                // For now, we'll use a placeholder ID based on the path
                breadcrumbItems.push({
                  id: `path-${i}`,
                  name: pathParts[i],
                  path: currentPath
                });
              }
            }

            setBreadcrumbs(breadcrumbItems);
          }
        } catch (error) {
          console.error('Error building breadcrumbs:', error);
          setBreadcrumbs([]);
        }
      }
    } catch (error) {
      toast.error('Failed to load files and folders');
    } finally {
      setLoading(false);
    }
  };

  const handleDownload = async (fileId: string) => {
    try {
      // Use proxy download to avoid S3 redirect issues
      const downloadUrl = `/api/files/${fileId}/download?proxy=true`;

      // Create a temporary link to trigger download
      const link = document.createElement('a');
      link.href = downloadUrl;
      link.style.display = 'none';
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);

      toast.success('Download started');
    } catch (error) {
      toast.error('Failed to download file');
    }
  };

  const handleShare = (fileId: string, fileName: string) => {
    setSelectedItem({ id: fileId, name: fileName, type: 'file' });
    setShareModalOpen(true);
  };

  const handlePreview = (file: File) => {
    setSelectedFile(file);
    setPreviewModalOpen(true);
  };

  const handleDelete = (itemId: string, itemName: string, itemType: 'file' | 'folder') => {
    setSelectedItem({ id: itemId, name: itemName, type: itemType });
    setDeleteModalOpen(true);
  };

  const handleRename = (itemId: string, itemName: string, itemType: 'file' | 'folder') => {
    setSelectedItem({ id: itemId, name: itemName, type: itemType });
    setRenameModalOpen(true);
  };

  const handleFolderClick = (folderId: string) => {
    router.push(`/dashboard/files?folder=${folderId}`);
  };

  const handleBreadcrumbClick = (item: BreadcrumbItem) => {
    if (item.id === 'root') {
      router.push('/dashboard/files');
    } else if (item.id.startsWith('path-')) {
      // For path-based breadcrumbs, we need to find the folder by path
      // This is a simplified implementation - in a real app you'd want to fetch folder by path
      router.push('/dashboard/files');
    } else {
      router.push(`/dashboard/files?folder=${item.id}`);
    }
  };

  const confirmDelete = async () => {
    if (!selectedItem) return;

    try {
      const endpoint = selectedItem.type === 'file'
        ? `/api/files/${selectedItem.id}`
        : `/api/folder/${selectedItem.id}`;

      const res = await fetch(endpoint, { method: 'DELETE' });
      if (!res.ok) throw new Error(`Failed to delete ${selectedItem.type}`);

      toast.success(`${selectedItem.type === 'file' ? 'File' : 'Folder'} deleted successfully`);
      fetchFilesAndFolders();
    } catch (error) {
      toast.error(`Failed to delete ${selectedItem?.type}`);
    } finally {
      setDeleteModalOpen(false);
      setSelectedItem(null);
    }
  };

  const confirmRename = async (newName: string) => {
    if (!selectedItem) return;

    try {
      const endpoint = selectedItem.type === 'file'
        ? `/api/files/${selectedItem.id}`
        : `/api/folder/${selectedItem.id}`;

      const res = await fetch(endpoint, {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ name: newName }),
      });

      if (!res.ok) throw new Error(`Failed to rename ${selectedItem.type}`);

      toast.success(`${selectedItem.type === 'file' ? 'File' : 'Folder'} renamed successfully`);
      fetchFilesAndFolders();
    } catch (error) {
      toast.error(`Failed to rename ${selectedItem?.type}`);
    } finally {
      setRenameModalOpen(false);
      setSelectedItem(null);
    }
  };

  return (
    <div className="p-6 space-y-6">
      {/* Header */}
      <div className="space-y-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <h1 className="text-2xl font-bold text-text-primary">My Files</h1>
            {(files.length > 0 || folders.length > 0) && (
              <div className="flex items-center space-x-2 text-sm text-text-secondary">
                <FolderIcon className="h-4 w-4" />
                <span>{folders.length} folders</span>
                <DocumentIcon className="h-4 w-4 ml-3" />
                <span>{files.length} files</span>
              </div>
            )}
          </div>

          <div className="flex items-center space-x-3">
            {/* View Mode Toggle */}
            <div className="flex items-center bg-secondary rounded-lg p-1">
              <button
                onClick={() => setViewMode('grid')}
                className={`p-1.5 rounded ${
                  viewMode === 'grid'
                    ? 'bg-background text-text-primary shadow-sm'
                    : 'text-text-secondary hover:text-text-primary'
                }`}
                title="Grid view"
              >
                <Squares2X2Icon className="h-4 w-4" />
              </button>
              <button
                onClick={() => setViewMode('list')}
                className={`p-1.5 rounded ${
                  viewMode === 'list'
                    ? 'bg-background text-text-primary shadow-sm'
                    : 'text-text-secondary hover:text-text-primary'
                }`}
                title="List view"
              >
                <ViewColumnsIcon className="h-4 w-4" />
              </button>
            </div>

            {/* Action Buttons */}
            <button className="flex items-center px-3 py-2 bg-secondary text-text-primary border border-divider rounded-lg hover:bg-secondary/80 text-sm">
              <FolderPlusIcon className="h-4 w-4 mr-2" />
              New Folder
            </button>
            <button className="flex items-center px-3 py-2 bg-accent text-white rounded-lg hover:bg-accent/90 text-sm">
              <CloudArrowUpIcon className="h-4 w-4 mr-2" />
              Upload
            </button>
          </div>
        </div>

        {/* Breadcrumbs */}
        <Breadcrumb
          items={breadcrumbs}
          onItemClick={handleBreadcrumbClick}
          className="text-text-secondary"
        />
      </div>

      {/* Content */}
      {loading ? (
        <div className={`grid gap-4 ${
          viewMode === 'grid'
            ? 'grid-cols-1 md:grid-cols-2 lg:grid-cols-4'
            : 'grid-cols-1'
        }`}>
          {[...Array(8)].map((_, i) => (
            <SkeletonFileCard key={i} />
          ))}
        </div>
      ) : (files.length === 0 && folders.length === 0) ? (
        <div className="text-center py-16">
          <div className="max-w-sm mx-auto">
            <DocumentIcon className="h-16 w-16 text-text-secondary mx-auto mb-4" />
            <h3 className="text-lg font-medium text-text-primary mb-2">No files or folders</h3>
            <p className="text-text-secondary mb-6">
              This folder is empty. Upload files or create new folders to get started.
            </p>
            <div className="flex justify-center space-x-3">
              <button className="flex items-center px-4 py-2 bg-accent text-white rounded-lg hover:bg-accent/90">
                <CloudArrowUpIcon className="h-4 w-4 mr-2" />
                Upload Files
              </button>
              <button className="flex items-center px-4 py-2 bg-secondary text-text-primary border border-divider rounded-lg hover:bg-secondary/80">
                <FolderPlusIcon className="h-4 w-4 mr-2" />
                New Folder
              </button>
            </div>
          </div>
        </div>
      ) : (
        <div className="space-y-6">
          {/* Folders Section */}
          {folders.length > 0 && (
            <div>
              <h3 className="text-sm font-medium text-text-secondary mb-3 flex items-center">
                📁 Folders ({folders.length})
              </h3>
              <div className={`grid gap-4 ${
                viewMode === 'grid'
                  ? 'grid-cols-1 md:grid-cols-2 lg:grid-cols-4'
                  : 'grid-cols-1'
              }`}>
                {folders.map((folder) => (
                  <FileCard
                    key={`folder-${folder.id}`}
                    id={folder.id}
                    name={folder.name}
                    type="folder"
                    size={0}
                    mimeType="folder"
                    createdAt={new Date(folder.createdAt)}
                    downloadCount={0}
                    onClick={() => handleFolderClick(folder.id)}
                    onShare={() => {}} // Folders don't have share for now
                    onDelete={() => handleDelete(folder.id, folder.name, 'folder')}
                    onRename={() => handleRename(folder.id, folder.name, 'folder')}
                  />
                ))}
              </div>
            </div>
          )}

          {/* Files Section */}
          {files.length > 0 && (
            <div>
              <h3 className="text-sm font-medium text-text-secondary mb-3 flex items-center">
                📄 Files ({files.length})
              </h3>
              <div className={`grid gap-4 ${
                viewMode === 'grid'
                  ? 'grid-cols-1 md:grid-cols-2 lg:grid-cols-4'
                  : 'grid-cols-1'
              }`}>
                {files.map((file) => (
                  <FileCard
                    key={`file-${file.id}`}
                    id={file.id}
                    name={file.name}
                    size={file.size}
                    mimeType={file.mimeType}
                    createdAt={new Date(file.createdAt)}
                    downloadCount={file.downloadCount}
                    type="file"
                    onPreview={() => handlePreview(file)}
                    onDownload={() => handleDownload(file.id)}
                    onShare={() => handleShare(file.id, file.name)}
                    onDelete={() => handleDelete(file.id, file.name, 'file')}
                    onRename={() => handleRename(file.id, file.name, 'file')}
                  />
                ))}
              </div>
            </div>
          )}
        </div>
      )}

      {/* Modals */}
      {selectedItem && selectedItem.type === 'file' && (
        <ShareModal
          isOpen={shareModalOpen}
          onClose={() => {
            setShareModalOpen(false);
            setSelectedItem(null);
          }}
          fileId={selectedItem.id}
          fileName={selectedItem.name}
        />
      )}

      <DeleteConfirmModal
        isOpen={deleteModalOpen}
        onClose={() => {
          setDeleteModalOpen(false);
          setSelectedItem(null);
        }}
        onConfirm={confirmDelete}
        itemName={selectedItem?.name || ''}
        itemType={selectedItem?.type || 'file'}
      />

      <RenameModal
        isOpen={renameModalOpen}
        onClose={() => {
          setRenameModalOpen(false);
          setSelectedItem(null);
        }}
        onConfirm={confirmRename}
        currentName={selectedItem?.name || ''}
        itemType={selectedItem?.type || 'file'}
      />

      {selectedFile && (
        <FilePreviewModal
          isOpen={previewModalOpen}
          onClose={() => {
            setPreviewModalOpen(false);
            setSelectedFile(null);
          }}
          file={{
            id: selectedFile.id,
            name: selectedFile.name,
            size: selectedFile.size,
            mimeType: selectedFile.mimeType,
            createdAt: selectedFile.createdAt,
          }}
          onDownload={() => handleDownload(selectedFile.id)}
          onShare={() => handleShare(selectedFile.id, selectedFile.name)}
        />
      )}
    </div>
  );
}