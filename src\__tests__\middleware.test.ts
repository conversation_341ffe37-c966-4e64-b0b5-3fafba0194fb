import { describe, it, expect, beforeEach, afterEach, jest } from '@jest/globals';
import { NextRequest } from 'next/server';
import { middleware } from '@/middleware';
import { getEdgeSession, isAdminUser } from '@/lib/auth-edge';

// Mock the auth-edge module
jest.mock('@/lib/auth-edge');

const mockGetEdgeSession = getEdgeSession as jest.MockedFunction<typeof getEdgeSession>;
const mockIsAdminUser = isAdminUser as jest.MockedFunction<typeof isAdminUser>;

describe('Middleware Edge Runtime Compatibility', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  afterEach(() => {
    jest.resetAllMocks();
  });

  const createMockRequest = (pathname: string) => {
    return new NextRequest(new URL(`http://localhost:3000${pathname}`));
  };

  const mockSession = {
    user: {
      id: '1',
      email: '<EMAIL>',
      name: 'Test User',
      verified: true,
      allowPlatformS3: false,
    },
    expires: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString(),
  };

  const mockAdminSession = {
    user: {
      id: '2',
      email: '<EMAIL>',
      name: 'Admin User',
      verified: true,
      allowPlatformS3: true,
    },
    expires: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString(),
  };

  it('should allow access to public routes without authentication', async () => {
    mockGetEdgeSession.mockResolvedValue(null);

    const request = createMockRequest('/');
    const response = await middleware(request);

    expect(response.status).toBe(200);
  });

  it('should redirect unauthenticated users from protected routes', async () => {
    mockGetEdgeSession.mockResolvedValue(null);

    const request = createMockRequest('/dashboard');
    const response = await middleware(request);

    expect(response.status).toBe(307);
    expect(response.headers.get('location')).toContain('/login');
  });

  it('should allow authenticated users to access protected routes', async () => {
    mockGetEdgeSession.mockResolvedValue(mockSession);

    const request = createMockRequest('/dashboard');
    const response = await middleware(request);

    expect(response.status).toBe(200);
  });

  it('should redirect authenticated users away from public routes', async () => {
    mockGetEdgeSession.mockResolvedValue(mockSession);

    const request = createMockRequest('/login');
    const response = await middleware(request);

    expect(response.status).toBe(307);
    expect(response.headers.get('location')).toContain('/dashboard');
  });

  it('should allow admin users to access admin routes', async () => {
    mockGetEdgeSession.mockResolvedValue(mockAdminSession);
    mockIsAdminUser.mockReturnValue(true);

    const request = createMockRequest('/admin-dashboard');
    const response = await middleware(request);

    expect(response.status).toBe(200);
  });

  it('should deny non-admin users access to admin routes', async () => {
    mockGetEdgeSession.mockResolvedValue(mockSession);
    mockIsAdminUser.mockReturnValue(false);

    const request = createMockRequest('/admin-dashboard');
    const response = await middleware(request);

    expect(response.status).toBe(200);
    expect(response.url).toContain('/404');
  });

  it('should deny non-admin users access to admin API routes', async () => {
    mockGetEdgeSession.mockResolvedValue(mockSession);
    mockIsAdminUser.mockReturnValue(false);

    const request = createMockRequest('/api/admin/users');
    const response = await middleware(request);

    expect(response.status).toBe(403);
  });

  it('should allow admin users to access admin API routes', async () => {
    mockGetEdgeSession.mockResolvedValue(mockAdminSession);
    mockIsAdminUser.mockReturnValue(true);

    const request = createMockRequest('/api/admin/users');
    const response = await middleware(request);

    expect(response.status).toBe(200);
  });
});
