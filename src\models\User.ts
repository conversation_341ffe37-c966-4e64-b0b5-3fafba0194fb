import mongoose, { Document, Schema } from 'mongoose';

export interface IUser extends Document {
  _id: mongoose.Types.ObjectId;
  name: string;
  email: string;
  passwordHash?: string;
  avatarUrl?: string;
  verified: boolean;
  allowPlatformS3: boolean;
  lastSyncAt?: Date;
  syncEnabled?: boolean;
  createdAt: Date;
  lastLogin?: Date;
  emailVerificationToken?: string;
  emailVerificationExpires?: Date;
  passwordResetToken?: string;
  passwordResetExpires?: Date;
}

const UserSchema = new Schema<IUser>({
  name: {
    type: String,
    required: true,
    trim: true,
    maxlength: 100,
  },
  email: {
    type: String,
    required: true,
    unique: true,
    lowercase: true,
    trim: true,
    maxlength: 255,
  },
  passwordHash: {
    type: String,
    required: false, // Optional for OAuth users
  },
  avatarUrl: {
    type: String,
    required: false,
  },
  verified: {
    type: Boolean,
    default: false,
  },
  allowPlatformS3: {
    type: Boolean,
    default: false,
  },
  createdAt: {
    type: Date,
    default: Date.now,
  },
  lastLogin: {
    type: Date,
    required: false,
  },
  emailVerificationToken: {
    type: String,
    required: false,
  },
  emailVerificationExpires: {
    type: Date,
    required: false,
  },
  passwordResetToken: {
    type: String,
    required: false,
  },
  passwordResetExpires: {
    type: Date,
    required: false,
  },
  lastSyncAt: {
    type: Date,
    required: false,
  },
  syncEnabled: {
    type: Boolean,
    default: false,
  },
}, {
  timestamps: true,
});

// Indexes
UserSchema.index({ emailVerificationToken: 1 });
UserSchema.index({ passwordResetToken: 1 });

// Methods
UserSchema.methods.toJSON = function() {
  const user = this.toObject();
  delete user.passwordHash;
  delete user.emailVerificationToken;
  delete user.emailVerificationExpires;
  delete user.passwordResetToken;
  delete user.passwordResetExpires;
  return user;
};

export const User = mongoose.models.User || mongoose.model<IUser>('User', UserSchema);
