import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@/lib/auth';

export async function POST(request: NextRequest) {
  try {
    const session = await auth();
    if (!session) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Create response
    const response = NextResponse.json({
      message: 'S3 credentials cleared successfully',
    });

    // Clear the S3 credentials cookie
    response.cookies.set('s3-credentials', '', {
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'strict',
      maxAge: 0, // Expire immediately
      path: '/',
    });

    return response;

  } catch (error) {
    console.error('S3 reset error:', error);
    return NextResponse.json(
      { error: 'Failed to reset S3 credentials' },
      { status: 500 }
    );
  }
}
