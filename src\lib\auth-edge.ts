import { NextRequest } from 'next/server';
import { getToken } from 'next-auth/jwt';

/**
 * Edge-compatible authentication utilities
 * These functions work in Edge Runtime without mongoose dependencies
 */

export interface EdgeSession {
  user: {
    id: string;
    email: string;
    name: string;
    image?: string;
    verified: boolean;
    allowPlatformS3: boolean;
  };
  expires: string;
}

/**
 * Get session from JWT token in Edge Runtime
 * This is compatible with Edge Runtime and doesn't require database access
 */
export async function getEdgeSession(request: NextRequest): Promise<EdgeSession | null> {
  try {
    const token = await getToken({ 
      req: request,
      secret: process.env.NEXTAUTH_SECRET 
    });

    if (!token) {
      return null;
    }

    return {
      user: {
        id: token.sub!,
        email: token.email!,
        name: token.name!,
        image: token.picture,
        verified: token.verified as boolean || false,
        allowPlatformS3: token.allowPlatformS3 as boolean || false,
      },
      expires: new Date(token.exp! * 1000).toISOString(),
    };
  } catch (error) {
    console.error('Edge session error:', error);
    return null;
  }
}

/**
 * Check if user is admin based on email (Edge Runtime compatible)
 */
export function isAdminUser(email: string): boolean {
  const adminEmails = process.env.ADMIN_EMAILS?.split(',') || [];
  return adminEmails.includes(email);
}

/**
 * Validate session and check if user is admin (Edge Runtime compatible)
 */
export async function validateAdminAccess(request: NextRequest): Promise<{
  isValid: boolean;
  isAdmin: boolean;
  session: EdgeSession | null;
}> {
  const session = await getEdgeSession(request);
  
  if (!session) {
    return {
      isValid: false,
      isAdmin: false,
      session: null,
    };
  }

  const isAdmin = isAdminUser(session.user.email);

  return {
    isValid: true,
    isAdmin,
    session,
  };
}

/**
 * Check if a route requires authentication
 */
export function isProtectedRoute(pathname: string): boolean {
  const protectedRoutes = [
    '/dashboard',
    '/settings',
    '/admin-dashboard',
    '/shared',
  ];
  
  return protectedRoutes.some(route => pathname.startsWith(route));
}

/**
 * Check if a route is admin-only
 */
export function isAdminRoute(pathname: string): boolean {
  const adminRoutes = [
    '/admin-dashboard',
  ];
  
  return adminRoutes.some(route => pathname.startsWith(route));
}

/**
 * Check if a route is public (should redirect authenticated users)
 */
export function isPublicRoute(pathname: string): boolean {
  const publicRoutes = [
    '/login',
    '/signup',
  ];
  
  return publicRoutes.some(route => pathname.startsWith(route));
}

/**
 * Check if a route is an admin API route
 */
export function isAdminApiRoute(pathname: string): boolean {
  return pathname.startsWith('/api/admin');
}
