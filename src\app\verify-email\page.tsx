'use client';

import React from 'react';
import Link from 'next/link';
import { useRouter, useSearchParams } from 'next/navigation';
import { Button } from '@/components/ui/Button';
import { ThemeToggle } from '@/components/ui/ThemeToggle';
import { toast } from '@/components/ui/Toast';
import { CheckCircleIcon, XCircleIcon, ClockIcon } from '@heroicons/react/24/outline';

export default function VerifyEmailPage() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const token = searchParams.get('token');
  
  const [status, setStatus] = React.useState<'loading' | 'success' | 'error' | 'pending'>('pending');
  const [message, setMessage] = React.useState('');

  React.useEffect(() => {
    if (token) {
      verifyEmail(token);
    }
  }, [token]);

  const verifyEmail = async (verificationToken: string) => {
    setStatus('loading');
    
    try {
      const response = await fetch(`/api/auth/verify-email?token=${verificationToken}`);
      const data = await response.json();
      
      if (response.ok) {
        setStatus('success');
        setMessage('Your email has been verified successfully!');
        toast.success('Email verified successfully!');
        
        // Redirect to dashboard after 3 seconds
        setTimeout(() => {
          router.push('/dashboard');
        }, 3000);
      } else {
        setStatus('error');
        setMessage(data.error || 'Failed to verify email');
        toast.error(data.error || 'Failed to verify email');
      }
    } catch (error) {
      setStatus('error');
      setMessage('An error occurred while verifying your email');
      toast.error('An error occurred while verifying your email');
    }
  };

  const resendVerification = async () => {
    try {
      const response = await fetch('/api/auth/resend-verification', {
        method: 'POST',
      });
      
      if (response.ok) {
        toast.success('Verification email sent! Please check your inbox.');
      } else {
        const data = await response.json();
        toast.error(data.error || 'Failed to resend verification email');
      }
    } catch (error) {
      toast.error('Failed to resend verification email');
    }
  };

  const getStatusIcon = () => {
    switch (status) {
      case 'loading':
        return <ClockIcon className="h-16 w-16 text-accent animate-pulse" />;
      case 'success':
        return <CheckCircleIcon className="h-16 w-16 text-success" />;
      case 'error':
        return <XCircleIcon className="h-16 w-16 text-error" />;
      default:
        return <ClockIcon className="h-16 w-16 text-warning" />;
    }
  };

  const getStatusTitle = () => {
    switch (status) {
      case 'loading':
        return 'Verifying your email...';
      case 'success':
        return 'Email Verified!';
      case 'error':
        return 'Verification Failed';
      default:
        return 'Email Verification Required';
    }
  };

  const getStatusMessage = () => {
    if (message) return message;
    
    switch (status) {
      case 'loading':
        return 'Please wait while we verify your email address.';
      case 'success':
        return 'Your email has been verified successfully. You will be redirected to your dashboard shortly.';
      case 'error':
        return 'We could not verify your email address. The link may be expired or invalid.';
      default:
        return 'Please check your email for a verification link, or request a new one below.';
    }
  };

  return (
    <div className="min-h-screen bg-background flex flex-col">
      {/* Header */}
      <header className="border-b border-divider">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-6">
            <Link href="/" className="flex items-center space-x-2">
              <div className="w-8 h-8 bg-accent rounded-lg flex items-center justify-center">
                <span className="text-white font-bold text-lg">D</span>
              </div>
              <span className="text-2xl font-bold text-text-primary">Drivn</span>
            </Link>
            <ThemeToggle />
          </div>
        </div>
      </header>

      {/* Main Content */}
      <div className="flex-1 flex items-center justify-center px-4 sm:px-6 lg:px-8 py-12">
        <div className="max-w-md w-full text-center space-y-8">
          {/* Status Icon */}
          <div className="flex justify-center">
            {getStatusIcon()}
          </div>

          {/* Status Title */}
          <div>
            <h1 className="text-3xl font-bold text-text-primary">
              {getStatusTitle()}
            </h1>
            <p className="mt-4 text-text-secondary">
              {getStatusMessage()}
            </p>
          </div>

          {/* Actions */}
          <div className="space-y-4">
            {status === 'success' && (
              <div className="space-y-4">
                <p className="text-sm text-text-secondary">
                  Redirecting to dashboard in 3 seconds...
                </p>
                <Button onClick={() => router.push('/dashboard')} className="w-full">
                  Go to Dashboard Now
                </Button>
              </div>
            )}

            {status === 'error' && (
              <div className="space-y-4">
                <Button onClick={resendVerification} className="w-full">
                  Resend Verification Email
                </Button>
                <Link href="/login">
                  <Button variant="ghost" className="w-full">
                    Back to Login
                  </Button>
                </Link>
              </div>
            )}

            {status === 'pending' && (
              <div className="space-y-4">
                <Button onClick={resendVerification} className="w-full">
                  Resend Verification Email
                </Button>
                <Link href="/login">
                  <Button variant="ghost" className="w-full">
                    Back to Login
                  </Button>
                </Link>
              </div>
            )}

            {status === 'loading' && (
              <div className="space-y-4">
                <div className="animate-pulse">
                  <div className="h-10 bg-secondary rounded-lg"></div>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}
