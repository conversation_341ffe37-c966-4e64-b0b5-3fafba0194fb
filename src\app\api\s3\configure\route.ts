import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';
import { auth } from '@/lib/auth';
import { S3Service } from '@/lib/s3';
import { encryptS3Credentials, S3Credentials } from '@/lib/encryption';

const s3ConfigSchema = z.object({
  endpoint: z.string().url('Invalid endpoint URL'),
  region: z.string().min(1, 'Region is required'),
  accessKeyId: z.string().min(1, 'Access Key ID is required'),
  secretAccessKey: z.string().min(1, 'Secret Access Key is required'),
  bucket: z.string().min(1, 'Bucket name is required').optional(),
});

export async function POST(request: NextRequest) {
  try {
    const session = await auth();
    if (!session) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const body = await request.json();
    const credentials = s3ConfigSchema.parse(body);

    // Create S3 service instance to validate credentials
    const s3Service = new S3Service(credentials as S3Credentials);
    
    // Validate credentials by attempting to list buckets
    const isValid = await s3Service.validateCredentials();
    if (!isValid) {
      return NextResponse.json(
        { error: 'Invalid S3 credentials. Please check your configuration.' },
        { status: 400 }
      );
    }

    // Encrypt credentials
    const encryptedCredentials = encryptS3Credentials(credentials as S3Credentials);

    // Create response with encrypted cookie
    const response = NextResponse.json({
      message: 'S3 credentials configured successfully',
      validated: true,
    });

    // Set encrypted credentials in httpOnly cookie
    response.cookies.set('s3-credentials', encryptedCredentials, {
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'strict',
      maxAge: 7 * 24 * 60 * 60, // 7 days
      path: '/',
    });

    return response;

  } catch (error) {
    console.error('S3 configuration error:', error);
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Validation error', details: error.issues },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: 'Failed to configure S3 credentials' },
      { status: 500 }
    );
  }
}

export async function GET(request: NextRequest) {
  try {
    const session = await auth();
    if (!session) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const s3Cookie = request.cookies.get('s3-credentials');
    
    return NextResponse.json({
      hasCredentials: !!s3Cookie,
      allowPlatformS3: session.user.allowPlatformS3,
    });

  } catch (error) {
    console.error('S3 status check error:', error);
    return NextResponse.json(
      { error: 'Failed to check S3 status' },
      { status: 500 }
    );
  }
}
