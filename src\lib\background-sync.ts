import { GlobalSyncManager } from './sync';
import { User } from '@/models/User';
import { decryptS3Credentials } from './encryption';
import connectDB from './mongodb';

export class BackgroundSyncService {
  private static instance: BackgroundSyncService;
  private syncInterval: NodeJS.Timeout | null = null;
  private isRunning = false;

  static getInstance(): BackgroundSyncService {
    if (!BackgroundSyncService.instance) {
      BackgroundSyncService.instance = new BackgroundSyncService();
    }
    return BackgroundSyncService.instance;
  }

  /**
   * Start the background sync service
   * @param intervalMinutes - How often to run sync (in minutes)
   */
  start(intervalMinutes: number = 60): void {
    if (this.isRunning) {
      console.log('Background sync service is already running');
      return;
    }

    this.isRunning = true;
    const intervalMs = intervalMinutes * 60 * 1000;

    console.log(`Starting background sync service with ${intervalMinutes} minute interval`);

    // Run initial sync after a short delay
    setTimeout(() => {
      this.runSyncCycle();
    }, 30000); // 30 seconds delay

    // Set up recurring sync
    this.syncInterval = setInterval(() => {
      this.runSyncCycle();
    }, intervalMs);
  }

  /**
   * Stop the background sync service
   */
  stop(): void {
    if (this.syncInterval) {
      clearInterval(this.syncInterval);
      this.syncInterval = null;
    }
    this.isRunning = false;
    console.log('Background sync service stopped');
  }

  /**
   * Check if the service is running
   */
  isServiceRunning(): boolean {
    return this.isRunning;
  }

  /**
   * Run a single sync cycle for all eligible users
   */
  private async runSyncCycle(): Promise<void> {
    try {
      console.log('Starting background sync cycle...');
      
      await connectDB();
      
      // Find users who have sync enabled and S3 credentials
      // Note: This is a simplified approach. In production, you'd want to store
      // encrypted S3 credentials in the database or use a more sophisticated approach
      const users = await User.find({
        syncEnabled: true,
        verified: true
      }).select('_id name email lastSyncAt');

      console.log(`Found ${users.length} users eligible for sync`);

      const syncManager = GlobalSyncManager.getInstance();
      let successCount = 0;
      let errorCount = 0;

      for (const user of users) {
        try {
          // Skip if sync is already in progress for this user
          if (syncManager.isSyncInProgress(user._id.toString())) {
            console.log(`Skipping user ${user.email} - sync already in progress`);
            continue;
          }

          // Check if enough time has passed since last sync (minimum 30 minutes)
          if (user.lastSyncAt) {
            const timeSinceLastSync = Date.now() - user.lastSyncAt.getTime();
            const minSyncInterval = 30 * 60 * 1000; // 30 minutes
            
            if (timeSinceLastSync < minSyncInterval) {
              console.log(`Skipping user ${user.email} - synced recently`);
              continue;
            }
          }

          // For background sync, we would need to retrieve S3 credentials
          // This is a placeholder - in production you'd implement secure credential storage
          console.log(`Would sync user ${user.email} if credentials were available`);
          
          // Placeholder for actual sync
          // const result = await syncManager.syncUser(user._id.toString(), s3Credentials);
          // successCount++;

        } catch (error) {
          console.error(`Failed to sync user ${user.email}:`, error);
          errorCount++;
        }
      }

      console.log(`Background sync cycle completed. Success: ${successCount}, Errors: ${errorCount}`);

    } catch (error) {
      console.error('Background sync cycle failed:', error);
    }
  }

  /**
   * Manually trigger a sync cycle
   */
  async triggerSyncCycle(): Promise<void> {
    if (!this.isRunning) {
      throw new Error('Background sync service is not running');
    }
    
    await this.runSyncCycle();
  }

  /**
   * Get sync statistics
   */
  async getSyncStats(): Promise<{
    totalUsers: number;
    syncEnabledUsers: number;
    usersWithRecentSync: number;
    isServiceRunning: boolean;
  }> {
    await connectDB();
    
    const totalUsers = await User.countDocuments({ verified: true });
    const syncEnabledUsers = await User.countDocuments({ 
      syncEnabled: true, 
      verified: true 
    });
    
    const recentSyncThreshold = new Date(Date.now() - 24 * 60 * 60 * 1000); // 24 hours ago
    const usersWithRecentSync = await User.countDocuments({
      syncEnabled: true,
      verified: true,
      lastSyncAt: { $gte: recentSyncThreshold }
    });

    return {
      totalUsers,
      syncEnabledUsers,
      usersWithRecentSync,
      isServiceRunning: this.isRunning
    };
  }
}

// Utility function to start background sync in production
export function initializeBackgroundSync(): void {
  // Only run in production or when explicitly enabled
  if (process.env.NODE_ENV === 'production' || process.env.ENABLE_BACKGROUND_SYNC === 'true') {
    const syncService = BackgroundSyncService.getInstance();
    
    // Get sync interval from environment variable (default: 60 minutes)
    const intervalMinutes = parseInt(process.env.SYNC_INTERVAL_MINUTES || '60');
    
    syncService.start(intervalMinutes);
    
    // Graceful shutdown
    process.on('SIGTERM', () => {
      console.log('Received SIGTERM, stopping background sync service...');
      syncService.stop();
    });
    
    process.on('SIGINT', () => {
      console.log('Received SIGINT, stopping background sync service...');
      syncService.stop();
    });
  }
}

// Export singleton instance
export const backgroundSyncService = BackgroundSyncService.getInstance();
