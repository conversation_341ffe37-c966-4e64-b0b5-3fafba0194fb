import { NextRequest, NextResponse } from 'next/server';
import { ZodError } from 'zod';

// Custom error classes
export class AppError extends Error {
  public readonly statusCode: number;
  public readonly isOperational: boolean;
  public readonly code?: string;

  constructor(message: string, statusCode: number = 500, isOperational: boolean = true, code?: string) {
    super(message);
    this.statusCode = statusCode;
    this.isOperational = isOperational;
    this.code = code;

    Error.captureStackTrace(this, this.constructor);
  }
}

export class ValidationError extends AppError {
  constructor(message: string, details?: any) {
    super(message, 400, true, 'VALIDATION_ERROR');
    this.name = 'ValidationError';
  }
}

export class AuthenticationError extends AppError {
  constructor(message: string = 'Authentication required') {
    super(message, 401, true, 'AUTHENTICATION_ERROR');
    this.name = 'AuthenticationError';
  }
}

export class AuthorizationError extends AppError {
  constructor(message: string = 'Access denied') {
    super(message, 403, true, 'AUTHORIZATION_ERROR');
    this.name = 'AuthorizationError';
  }
}

export class NotFoundError extends AppError {
  constructor(message: string = 'Resource not found') {
    super(message, 404, true, 'NOT_FOUND_ERROR');
    this.name = 'NotFoundError';
  }
}

export class ConflictError extends AppError {
  constructor(message: string = 'Resource conflict') {
    super(message, 409, true, 'CONFLICT_ERROR');
    this.name = 'ConflictError';
  }
}

export class RateLimitError extends AppError {
  constructor(message: string = 'Rate limit exceeded') {
    super(message, 429, true, 'RATE_LIMIT_ERROR');
    this.name = 'RateLimitError';
  }
}

export class ExternalServiceError extends AppError {
  constructor(service: string, message: string = 'External service error') {
    super(`${service}: ${message}`, 502, true, 'EXTERNAL_SERVICE_ERROR');
    this.name = 'ExternalServiceError';
  }
}

// Error logging utility
export class ErrorLogger {
  private static logError(error: Error, context?: any): void {
    const timestamp = new Date().toISOString();
    const errorInfo = {
      timestamp,
      name: error.name,
      message: error.message,
      stack: error.stack,
      context,
    };

    if (process.env.NODE_ENV === 'production') {
      // In production, you might want to send to external logging service
      console.error('ERROR:', JSON.stringify(errorInfo, null, 2));
    } else {
      console.error('ERROR:', errorInfo);
    }
  }

  static logAndThrow(error: Error, context?: any): never {
    this.logError(error, context);
    throw error;
  }

  static log(error: Error, context?: any): void {
    this.logError(error, context);
  }
}

// Global error handler for API routes
export function handleApiError(error: unknown, request?: NextRequest): NextResponse {
  ErrorLogger.log(error as Error, {
    url: request?.url,
    method: request?.method,
    headers: request ? Object.fromEntries(request.headers.entries()) : undefined,
  });

  // Handle known error types
  if (error instanceof AppError) {
    return NextResponse.json(
      {
        error: error.message,
        code: error.code,
        statusCode: error.statusCode,
      },
      { status: error.statusCode }
    );
  }

  // Handle Zod validation errors
  if (error instanceof ZodError) {
    return NextResponse.json(
      {
        error: 'Validation error',
        code: 'VALIDATION_ERROR',
        details: error.issues.map(issue => ({
          field: issue.path.join('.'),
          message: issue.message,
        })),
      },
      { status: 400 }
    );
  }

  // Handle MongoDB errors
  if (error && typeof error === 'object' && 'name' in error) {
    const mongoError = error as any;
    
    if (mongoError.name === 'MongoServerError') {
      if (mongoError.code === 11000) {
        return NextResponse.json(
          {
            error: 'Duplicate entry',
            code: 'DUPLICATE_ERROR',
          },
          { status: 409 }
        );
      }
    }

    if (mongoError.name === 'ValidationError') {
      return NextResponse.json(
        {
          error: 'Database validation error',
          code: 'DB_VALIDATION_ERROR',
          details: Object.values(mongoError.errors || {}).map((err: any) => ({
            field: err.path,
            message: err.message,
          })),
        },
        { status: 400 }
      );
    }

    if (mongoError.name === 'CastError') {
      return NextResponse.json(
        {
          error: 'Invalid ID format',
          code: 'INVALID_ID_ERROR',
        },
        { status: 400 }
      );
    }
  }

  // Handle generic errors
  const genericError = error as Error;
  
  // Don't expose internal errors in production
  const message = process.env.NODE_ENV === 'production' 
    ? 'Internal server error' 
    : genericError.message || 'Unknown error';

  return NextResponse.json(
    {
      error: message,
      code: 'INTERNAL_ERROR',
    },
    { status: 500 }
  );
}

// Async error wrapper for API routes
export function asyncHandler(
  handler: (request: NextRequest, context?: any) => Promise<NextResponse>
) {
  return async (request: NextRequest, context?: any): Promise<NextResponse> => {
    try {
      return await handler(request, context);
    } catch (error) {
      return handleApiError(error, request);
    }
  };
}

// Error boundary for React components (to be used in error.tsx files)
export interface ErrorBoundaryProps {
  error: Error & { digest?: string };
  reset: () => void;
}

export function getErrorMessage(error: unknown): string {
  if (error instanceof Error) {
    return error.message;
  }
  
  if (typeof error === 'string') {
    return error;
  }
  
  return 'An unknown error occurred';
}

export function getErrorCode(error: unknown): string | undefined {
  if (error instanceof AppError) {
    return error.code;
  }
  
  return undefined;
}

// Utility to check if error is operational (safe to show to user)
export function isOperationalError(error: unknown): boolean {
  if (error instanceof AppError) {
    return error.isOperational;
  }
  
  return false;
}

// Error reporting utility (for external services like Sentry)
export function reportError(error: Error, context?: any): void {
  // In production, you might want to send to external error reporting service
  if (process.env.NODE_ENV === 'production') {
    // Example: Sentry.captureException(error, { extra: context });
    console.error('REPORTED ERROR:', error, context);
  }
}

// Health check error types
export class HealthCheckError extends AppError {
  constructor(service: string, message: string) {
    super(`Health check failed for ${service}: ${message}`, 503, true, 'HEALTH_CHECK_ERROR');
    this.name = 'HealthCheckError';
  }
}
