import { NextRequest, NextResponse } from 'next/server';
import { ZodError } from 'zod';
import { isDetailedErrorsEnabled, DevLogger } from './dev-config';

// Enhanced error context interface
export interface ErrorContext {
  location?: {
    file?: string;
    function?: string;
    line?: number;
    column?: number;
  };
  variables?: Record<string, any>;
  request?: {
    url?: string;
    method?: string;
    headers?: Record<string, string>;
    body?: any;
    params?: Record<string, any>;
    query?: Record<string, any>;
  };
  user?: {
    id?: string;
    email?: string;
  };
  timestamp?: string;
  suggestions?: string[];
  relatedErrors?: Error[];
}

// Custom error classes with enhanced context
export class AppError extends Error {
  public readonly statusCode: number;
  public readonly isOperational: boolean;
  public readonly code?: string;
  public readonly context?: ErrorContext;

  constructor(
    message: string,
    statusCode: number = 500,
    isOperational: boolean = true,
    code?: string,
    context?: ErrorContext
  ) {
    super(message);
    this.statusCode = statusCode;
    this.isOperational = isOperational;
    this.code = code;
    this.context = {
      ...context,
      timestamp: context?.timestamp || new Date().toISOString(),
    };

    Error.captureStackTrace(this, this.constructor);
  }

  /**
   * Create an enhanced error with location context
   */
  static withLocation(
    message: string,
    file: string,
    functionName: string,
    statusCode: number = 500,
    additionalContext?: Partial<ErrorContext>
  ): AppError {
    return new AppError(message, statusCode, true, undefined, {
      location: {
        file,
        function: functionName,
      },
      ...additionalContext,
    });
  }

  /**
   * Add variable context to the error
   */
  withVariables(variables: Record<string, any>): AppError {
    this.context!.variables = { ...this.context?.variables, ...variables };
    return this;
  }

  /**
   * Add suggestions for fixing the error
   */
  withSuggestions(suggestions: string[]): AppError {
    this.context!.suggestions = [...(this.context?.suggestions || []), ...suggestions];
    return this;
  }

  /**
   * Get AI-friendly error description
   */
  getAIFriendlyDescription(): string {
    const parts = [
      `ERROR: ${this.message}`,
      `CODE: ${this.code || 'UNKNOWN'}`,
      `STATUS: ${this.statusCode}`,
    ];

    if (this.context?.location) {
      const loc = this.context.location;
      parts.push(`LOCATION: ${loc.file}:${loc.function}${loc.line ? `:${loc.line}` : ''}`);
    }

    if (this.context?.variables && Object.keys(this.context.variables).length > 0) {
      parts.push(`VARIABLES: ${JSON.stringify(this.context.variables, null, 2)}`);
    }

    if (this.context?.suggestions && this.context.suggestions.length > 0) {
      parts.push(`SUGGESTIONS: ${this.context.suggestions.join('; ')}`);
    }

    if (this.stack) {
      parts.push(`STACK: ${this.stack}`);
    }

    return parts.join('\n');
  }
}

export class ValidationError extends AppError {
  constructor(message: string, details?: any, context?: ErrorContext) {
    super(message, 400, true, 'VALIDATION_ERROR', {
      ...context,
      variables: { ...context?.variables, validationDetails: details },
      suggestions: [
        'Check the input data format and types',
        'Ensure all required fields are provided',
        'Validate data against the expected schema',
        ...(context?.suggestions || [])
      ]
    });
    this.name = 'ValidationError';
  }
}

export class AuthenticationError extends AppError {
  constructor(message: string = 'Authentication required', context?: ErrorContext) {
    super(message, 401, true, 'AUTHENTICATION_ERROR', {
      ...context,
      suggestions: [
        'Ensure user is logged in',
        'Check authentication token validity',
        'Verify session has not expired',
        'In development mode, check if auth bypass is enabled',
        ...(context?.suggestions || [])
      ]
    });
    this.name = 'AuthenticationError';
  }
}

export class AuthorizationError extends AppError {
  constructor(message: string = 'Access denied', context?: ErrorContext) {
    super(message, 403, true, 'AUTHORIZATION_ERROR', {
      ...context,
      suggestions: [
        'Check user permissions and roles',
        'Verify user has required access level',
        'Ensure resource ownership if applicable',
        'Check admin status if accessing admin features',
        ...(context?.suggestions || [])
      ]
    });
    this.name = 'AuthorizationError';
  }
}

export class NotFoundError extends AppError {
  constructor(message: string = 'Resource not found', context?: ErrorContext) {
    super(message, 404, true, 'NOT_FOUND_ERROR', {
      ...context,
      suggestions: [
        'Verify the resource ID or path is correct',
        'Check if the resource was deleted',
        'Ensure user has access to view the resource',
        'Verify database connection and data integrity',
        ...(context?.suggestions || [])
      ]
    });
    this.name = 'NotFoundError';
  }
}

export class ConflictError extends AppError {
  constructor(message: string = 'Resource conflict', context?: ErrorContext) {
    super(message, 409, true, 'CONFLICT_ERROR', {
      ...context,
      suggestions: [
        'Check for duplicate entries or unique constraint violations',
        'Verify resource is not already in the desired state',
        'Handle concurrent modification conflicts',
        'Implement optimistic locking if needed',
        ...(context?.suggestions || [])
      ]
    });
    this.name = 'ConflictError';
  }
}

export class RateLimitError extends AppError {
  constructor(message: string = 'Rate limit exceeded', context?: ErrorContext) {
    super(message, 429, true, 'RATE_LIMIT_ERROR', {
      ...context,
      suggestions: [
        'Implement exponential backoff retry logic',
        'Reduce request frequency',
        'Check rate limit configuration',
        'Consider implementing request queuing',
        ...(context?.suggestions || [])
      ]
    });
    this.name = 'RateLimitError';
  }
}

export class ExternalServiceError extends AppError {
  constructor(service: string, message: string = 'External service error', context?: ErrorContext) {
    super(`${service}: ${message}`, 502, true, 'EXTERNAL_SERVICE_ERROR', {
      ...context,
      variables: { ...context?.variables, service },
      suggestions: [
        'Check external service availability and status',
        'Verify API credentials and configuration',
        'Implement retry logic with exponential backoff',
        'Check network connectivity and firewall rules',
        'Monitor service health and implement fallbacks',
        ...(context?.suggestions || [])
      ]
    });
    this.name = 'ExternalServiceError';
  }
}

// Enhanced error logging utility
export class ErrorLogger {
  private static formatErrorForLogging(error: Error, additionalContext?: any) {
    const timestamp = new Date().toISOString();
    const isAppError = error instanceof AppError;

    const errorInfo: any = {
      timestamp,
      name: error.name,
      message: error.message,
      stack: error.stack,
    };

    // Add AppError specific context
    if (isAppError) {
      errorInfo.statusCode = error.statusCode;
      errorInfo.code = error.code;
      errorInfo.isOperational = error.isOperational;
      errorInfo.context = error.context;

      // Add AI-friendly description in development
      if (isDetailedErrorsEnabled()) {
        errorInfo.aiFriendlyDescription = error.getAIFriendlyDescription();
      }
    }

    // Add additional context
    if (additionalContext) {
      errorInfo.additionalContext = additionalContext;
    }

    return errorInfo;
  }

  private static logError(error: Error, additionalContext?: any): void {
    const errorInfo = this.formatErrorForLogging(error, additionalContext);

    if (process.env.NODE_ENV === 'production') {
      // In production, log structured JSON for external services
      console.error('ERROR:', JSON.stringify(errorInfo, null, 2));
    } else {
      // In development, log with enhanced formatting
      console.error('🚨 ERROR OCCURRED 🚨');
      console.error('==================');

      if (error instanceof AppError && isDetailedErrorsEnabled()) {
        console.error(error.getAIFriendlyDescription());
        console.error('==================');
      }

      console.error('Full Error Details:', errorInfo);
      console.error('==================\n');
    }

    // Also log to development logger
    DevLogger.error('Error logged', { errorName: error.name, message: error.message });
  }

  static logAndThrow(error: Error, additionalContext?: any): never {
    this.logError(error, additionalContext);
    throw error;
  }

  static log(error: Error, additionalContext?: any): void {
    this.logError(error, additionalContext);
  }

  /**
   * Create an error with enhanced context from current execution
   */
  static createContextualError(
    message: string,
    file: string,
    functionName: string,
    variables?: Record<string, any>,
    statusCode: number = 500
  ): AppError {
    return AppError.withLocation(message, file, functionName, statusCode, {
      variables,
    });
  }

  /**
   * Log error with request context
   */
  static logWithRequest(error: Error, request: NextRequest): void {
    const requestContext = {
      url: request.url,
      method: request.method,
      headers: Object.fromEntries(request.headers.entries()),
      // Note: Don't log sensitive headers in production
    };

    this.log(error, { request: requestContext });
  }
}

// Enhanced global error handler for API routes
export function handleApiError(error: unknown, request?: NextRequest): NextResponse {
  const errorObj = error as Error;

  // Log with request context if available
  if (request) {
    ErrorLogger.logWithRequest(errorObj, request);
  } else {
    ErrorLogger.log(errorObj);
  }

  // Handle known error types
  if (error instanceof AppError) {
    const response: any = {
      error: error.message,
      code: error.code,
      statusCode: error.statusCode,
    };

    // Add detailed context in development mode
    if (isDetailedErrorsEnabled()) {
      response.context = error.context;
      response.aiFriendlyDescription = error.getAIFriendlyDescription();
      response.suggestions = error.context?.suggestions;
    }

    return NextResponse.json(response, { status: error.statusCode });
  }

  // Handle Zod validation errors
  if (error instanceof ZodError) {
    return NextResponse.json(
      {
        error: 'Validation error',
        code: 'VALIDATION_ERROR',
        details: error.issues.map(issue => ({
          field: issue.path.join('.'),
          message: issue.message,
        })),
      },
      { status: 400 }
    );
  }

  // Handle MongoDB errors
  if (error && typeof error === 'object' && 'name' in error) {
    const mongoError = error as any;
    
    if (mongoError.name === 'MongoServerError') {
      if (mongoError.code === 11000) {
        return NextResponse.json(
          {
            error: 'Duplicate entry',
            code: 'DUPLICATE_ERROR',
          },
          { status: 409 }
        );
      }
    }

    if (mongoError.name === 'ValidationError') {
      return NextResponse.json(
        {
          error: 'Database validation error',
          code: 'DB_VALIDATION_ERROR',
          details: Object.values(mongoError.errors || {}).map((err: any) => ({
            field: err.path,
            message: err.message,
          })),
        },
        { status: 400 }
      );
    }

    if (mongoError.name === 'CastError') {
      return NextResponse.json(
        {
          error: 'Invalid ID format',
          code: 'INVALID_ID_ERROR',
        },
        { status: 400 }
      );
    }
  }

  // Handle generic errors
  const genericError = error as Error;
  
  // Don't expose internal errors in production
  const message = process.env.NODE_ENV === 'production' 
    ? 'Internal server error' 
    : genericError.message || 'Unknown error';

  return NextResponse.json(
    {
      error: message,
      code: 'INTERNAL_ERROR',
    },
    { status: 500 }
  );
}

// Async error wrapper for API routes
export function asyncHandler(
  handler: (request: NextRequest, context?: any) => Promise<NextResponse>
) {
  return async (request: NextRequest, context?: any): Promise<NextResponse> => {
    try {
      return await handler(request, context);
    } catch (error) {
      return handleApiError(error, request);
    }
  };
}

// Error boundary for React components (to be used in error.tsx files)
export interface ErrorBoundaryProps {
  error: Error & { digest?: string };
  reset: () => void;
}

export function getErrorMessage(error: unknown): string {
  if (error instanceof Error) {
    return error.message;
  }
  
  if (typeof error === 'string') {
    return error;
  }
  
  return 'An unknown error occurred';
}

export function getErrorCode(error: unknown): string | undefined {
  if (error instanceof AppError) {
    return error.code;
  }
  
  return undefined;
}

// Utility to check if error is operational (safe to show to user)
export function isOperationalError(error: unknown): boolean {
  if (error instanceof AppError) {
    return error.isOperational;
  }
  
  return false;
}

// Error reporting utility (for external services like Sentry)
export function reportError(error: Error, context?: any): void {
  // In production, you might want to send to external error reporting service
  if (process.env.NODE_ENV === 'production') {
    // Example: Sentry.captureException(error, { extra: context });
    console.error('REPORTED ERROR:', error, context);
  }
}

// Health check error types
export class HealthCheckError extends AppError {
  constructor(service: string, message: string) {
    super(`Health check failed for ${service}: ${message}`, 503, true, 'HEALTH_CHECK_ERROR');
    this.name = 'HealthCheckError';
  }
}
