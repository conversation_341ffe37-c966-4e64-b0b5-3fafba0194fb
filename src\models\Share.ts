import mongoose, { Document, Schema, Model } from 'mongoose';

export interface IShare extends Document {
  _id: mongoose.Types.ObjectId;
  fileId: mongoose.Types.ObjectId;
  userId: mongoose.Types.ObjectId;
  shareId: string;
  permission: 'view' | 'edit';
  allowDownload: boolean;
  password?: string;
  expiresAt?: Date;
  maxAccessCount?: number;
  accessCount: number;
  isActive: boolean;
  sharedWithEmails: string[];
  createdAt: Date;
  updatedAt: Date;
}

export interface IShareModel extends Model<IShare> {
  generateShareId(): string;
  findActiveShare(shareId: string): Promise<IShare | null>;
}

const ShareSchema = new Schema<IShare>({
  fileId: {
    type: Schema.Types.ObjectId,
    ref: 'File',
    required: true,
  },
  userId: {
    type: Schema.Types.ObjectId,
    ref: 'User',
    required: true,
  },
  shareId: {
    type: String,
    required: true,
    unique: true,
    index: true,
  },
  permission: {
    type: String,
    enum: ['view', 'edit'],
    default: 'view',
  },
  allowDownload: {
    type: Boolean,
    default: true,
  },
  password: {
    type: String,
    required: false,
  },
  expiresAt: {
    type: Date,
    required: false,
  },
  maxAccessCount: {
    type: Number,
    required: false,
  },
  accessCount: {
    type: Number,
    default: 0,
  },
  isActive: {
    type: Boolean,
    default: true,
  },
  sharedWithEmails: [{
    type: String,
    lowercase: true,
    trim: true,
  }],
}, {
  timestamps: true,
});

// Indexes
ShareSchema.index({ shareId: 1 });
ShareSchema.index({ fileId: 1, userId: 1 });
ShareSchema.index({ userId: 1 });
ShareSchema.index({ expiresAt: 1 });
ShareSchema.index({ isActive: 1 });

// Static methods
ShareSchema.statics.generateShareId = function(): string {
  const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
  let result = '';
  for (let i = 0; i < 12; i++) {
    result += chars.charAt(Math.floor(Math.random() * chars.length));
  }
  return result;
};

ShareSchema.statics.findActiveShare = function(shareId: string) {
  return this.findOne({
    shareId,
    isActive: true,
    $or: [
      { expiresAt: { $exists: false } },
      { expiresAt: null },
      { expiresAt: { $gt: new Date() } }
    ]
  });
};

// Instance methods
ShareSchema.methods.isExpired = function(): boolean {
  if (!this.expiresAt) return false;
  return new Date() > this.expiresAt;
};

ShareSchema.methods.hasReachedMaxAccess = function(): boolean {
  if (!this.maxAccessCount) return false;
  return this.accessCount >= this.maxAccessCount;
};

ShareSchema.methods.canAccess = function(): boolean {
  return this.isActive && !this.isExpired() && !this.hasReachedMaxAccess();
};

ShareSchema.methods.incrementAccess = function() {
  this.accessCount += 1;
  return this.save();
};

ShareSchema.methods.getShareUrl = function() {
  const baseUrl = process.env.NEXTAUTH_URL || 'http://localhost:3000';
  return `${baseUrl}/s/${this.shareId}`;
};

// Pre-save middleware
ShareSchema.pre('save', function(next) {
  if (this.isNew && !this.shareId) {
    this.shareId = (this.constructor as any).generateShareId();
  }
  next();
});

export const Share = (mongoose.models.Share || mongoose.model<IShare, IShareModel>('Share', ShareSchema)) as IShareModel;
