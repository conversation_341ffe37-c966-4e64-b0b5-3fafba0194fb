import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@/lib/auth';
import { User } from '@/models/User';
import { File } from '@/models/File';
import { Folder } from '@/models/Folder';
import { Share } from '@/models/Share';
import connectDB from '@/lib/mongodb';

// Check if user is admin
async function isAdmin(session: any): Promise<boolean> {
  const adminEmails = process.env.ADMIN_EMAILS?.split(',') || [];
  return adminEmails.includes(session.user.email);
}

export async function GET(request: NextRequest) {
  try {
    const session = await auth();
    if (!session) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    if (!(await isAdmin(session))) {
      return NextResponse.json(
        { error: 'Access denied' },
        { status: 403 }
      );
    }

    await connectDB();

    // Get date range for recent activity (last 30 days)
    const thirtyDaysAgo = new Date();
    thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);

    // Get overview stats
    const [
      totalUsers,
      totalFiles,
      totalFolders,
      totalShares,
      verifiedUsers,
      platformS3Users,
      recentSignups,
      recentUploads,
      recentShares,
      totalStorageUsed,
      avgFileSize
    ] = await Promise.all([
      User.countDocuments(),
      File.countDocuments(),
      Folder.countDocuments(),
      Share.countDocuments(),
      User.countDocuments({ verified: true }),
      User.countDocuments({ allowPlatformS3: true }),
      User.countDocuments({ createdAt: { $gte: thirtyDaysAgo } }),
      File.countDocuments({ createdAt: { $gte: thirtyDaysAgo } }),
      Share.countDocuments({ createdAt: { $gte: thirtyDaysAgo } }),
      File.aggregate([
        { $group: { _id: null, total: { $sum: '$size' } } }
      ]).then(result => result[0]?.total || 0),
      File.aggregate([
        { $group: { _id: null, avg: { $avg: '$size' } } }
      ]).then(result => Math.round(result[0]?.avg || 0))
    ]);

    const stats = {
      overview: {
        totalUsers,
        totalFiles,
        totalFolders,
        totalShares,
        totalStorageUsed,
        avgFileSize,
      },
      users: {
        total: totalUsers,
        verified: verifiedUsers,
        unverified: totalUsers - verifiedUsers,
        platformS3Users,
        recentSignups,
      },
      files: {
        total: totalFiles,
        recentUploads,
        totalSize: totalStorageUsed,
        avgSize: avgFileSize,
      },
      shares: {
        total: totalShares,
        recentShares,
      },
    };

    return NextResponse.json(stats);

  } catch (error) {
    console.error('Admin stats error:', error);
    return NextResponse.json(
      { error: 'Failed to fetch admin stats' },
      { status: 500 }
    );
  }
}
