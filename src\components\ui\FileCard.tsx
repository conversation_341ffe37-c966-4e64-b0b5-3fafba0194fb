'use client';

import React from 'react';
import { cn, formatFileSize, formatDate, getFileIcon } from '@/lib/utils';
import { EllipsisVerticalIcon, EyeIcon, ArrowDownTrayIcon, ShareIcon, PencilIcon } from '@heroicons/react/24/outline';
import { FolderIcon } from '@heroicons/react/24/solid';

interface FileCardProps {
  id: string;
  name: string;
  type: 'file' | 'folder';
  size?: number;
  mimeType?: string;
  createdAt: Date;
  isPublic?: boolean;
  downloadCount?: number;
  onClick?: () => void;
  onDownload?: () => void;
  onShare?: () => void;
  onDelete?: () => void;
  onRename?: () => void;
  onPreview?: () => void;
  className?: string;
}

export const FileCard: React.FC<FileCardProps> = ({
  id,
  name,
  type,
  size,
  mimeType,
  createdAt,
  isPublic = false,
  downloadCount = 0,
  onClick,
  onDownload,
  onShare,
  onDelete,
  onRename,
  onPreview,
  className,
}) => {
  const [showMenu, setShowMenu] = React.useState(false);
  const menuRef = React.useRef<HTMLDivElement>(null);

  React.useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (menuRef.current && !menuRef.current.contains(event.target as Node)) {
        setShowMenu(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  const handleMenuClick = (e: React.MouseEvent) => {
    e.stopPropagation();
    setShowMenu(!showMenu);
  };

  const handleActionClick = (action: () => void) => (e: React.MouseEvent) => {
    e.stopPropagation();
    setShowMenu(false);
    action();
  };

  return (
    <div
      className={cn(
        'group relative p-4 bg-background border border-divider rounded-lg hover:shadow-md transition-all duration-200 cursor-pointer hover:border-accent/50',
        className
      )}
      onClick={onClick}
    >
      {/* File/Folder Icon and Info */}
      <div className="flex items-start space-x-3">
        <div className="flex-shrink-0">
          {type === 'folder' ? (
            <FolderIcon className="h-8 w-8 text-accent" />
          ) : (
            <div className="h-8 w-8 flex items-center justify-center text-2xl">
              {getFileIcon(name)}
            </div>
          )}
        </div>
        
        <div className="flex-1 min-w-0">
          <h3 className="text-sm font-medium text-text-primary truncate">
            {name}
          </h3>
          <div className="flex items-center space-x-2 mt-1">
            {type === 'file' && size && (
              <span className="text-xs text-text-secondary">
                {formatFileSize(size)}
              </span>
            )}
            <span className="text-xs text-text-secondary">
              {formatDate(createdAt)}
            </span>
            {isPublic && (
              <span className="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-accent/10 text-accent">
                Public
              </span>
            )}
          </div>
          {type === 'file' && downloadCount > 0 && (
            <div className="flex items-center mt-1">
              <ArrowDownTrayIcon className="h-3 w-3 text-text-secondary mr-1" />
              <span className="text-xs text-text-secondary">
                {downloadCount} downloads
              </span>
            </div>
          )}
        </div>

        {/* Menu Button */}
        <div className="relative" ref={menuRef}>
          <button
            onClick={handleMenuClick}
            className="opacity-0 group-hover:opacity-100 p-1 rounded hover:bg-secondary transition-all"
          >
            <EllipsisVerticalIcon className="h-4 w-4 text-text-secondary" />
          </button>

          {/* Dropdown Menu */}
          {showMenu && (
            <div className="absolute right-0 top-8 w-48 bg-background border border-divider rounded-lg shadow-lg z-10">
              <div className="py-1">
                {type === 'file' && onPreview && (
                  <button
                    onClick={handleActionClick(onPreview)}
                    className="flex items-center w-full px-4 py-2 text-sm text-text-primary hover:bg-secondary"
                  >
                    <EyeIcon className="h-4 w-4 mr-3" />
                    Preview
                  </button>
                )}
                {type === 'file' && onDownload && (
                  <button
                    onClick={handleActionClick(onDownload)}
                    className="flex items-center w-full px-4 py-2 text-sm text-text-primary hover:bg-secondary"
                  >
                    <ArrowDownTrayIcon className="h-4 w-4 mr-3" />
                    Download
                  </button>
                )}
                {onShare && (
                  <button
                    onClick={handleActionClick(onShare)}
                    className="flex items-center w-full px-4 py-2 text-sm text-text-primary hover:bg-secondary"
                  >
                    <ShareIcon className="h-4 w-4 mr-3" />
                    Share
                  </button>
                )}
                {onRename && (
                  <button
                    onClick={handleActionClick(onRename)}
                    className="flex items-center w-full px-4 py-2 text-sm text-text-primary hover:bg-secondary"
                  >
                    <PencilIcon className="h-4 w-4 mr-3" />
                    Rename
                  </button>
                )}
                {onDelete && (
                  <>
                    <div className="border-t border-divider my-1" />
                    <button
                      onClick={handleActionClick(onDelete)}
                      className="flex items-center w-full px-4 py-2 text-sm text-error hover:bg-error/10"
                    >
                      <svg className="h-4 w-4 mr-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                      </svg>
                      Delete
                    </button>
                  </>
                )}
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};
