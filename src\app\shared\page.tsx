'use client';

import React from 'react';
import { FileCard } from '@/components/ui/FileCard';
import { SkeletonFileCard } from '@/components/ui/Skeleton';
import { toast } from '@/components/ui/Toast';
import { ShareIcon, DocumentIcon } from '@heroicons/react/24/outline';
import { formatDate } from '@/lib/utils';

interface SharedFile {
  id: string;
  shareId: string;
  shareUrl: string;
  permission: 'view' | 'edit';
  expiresAt?: string;
  allowDownload: boolean;
  maxAccessCount?: number;
  accessCount: number;
  createdAt: string;
  lastAccessedAt?: string;
  file: {
    id: string;
    name: string;
    originalName: string;
    size: number;
    mimeType: string;
  };
}

export default function SharedPage() {
  const [sharedFiles, setSharedFiles] = React.useState<SharedFile[]>([]);
  const [loading, setLoading] = React.useState(true);

  React.useEffect(() => {
    fetchSharedFiles();
  }, []);

  const fetchSharedFiles = async () => {
    try {
      const response = await fetch('/api/share/file');
      if (!response.ok) {
        throw new Error('Failed to fetch shared files');
      }
      
      const data = await response.json();
      setSharedFiles(data.shares);
    } catch (error) {
      console.error('Failed to fetch shared files:', error);
      toast.error('Failed to load shared files');
    } finally {
      setLoading(false);
    }
  };

  const handleCopyShareUrl = async (shareUrl: string) => {
    try {
      await navigator.clipboard.writeText(shareUrl);
      toast.success('Share URL copied to clipboard!');
    } catch (error) {
      toast.error('Failed to copy URL');
    }
  };

  const handleRevokeShare = async (shareId: string) => {
    if (!confirm('Are you sure you want to revoke this share? The link will no longer work.')) {
      return;
    }

    try {
      const response = await fetch('/api/share/revoke', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ shareId }),
      });

      if (!response.ok) {
        throw new Error('Failed to revoke share');
      }

      toast.success('Share revoked successfully');
      fetchSharedFiles();
    } catch (error) {
      console.error('Failed to revoke share:', error);
      toast.error('Failed to revoke share');
    }
  };

  if (loading) {
    return (
      <div className="p-6">
        <div className="mb-6">
          <h1 className="text-2xl font-bold text-text-primary">Shared Files</h1>
          <p className="text-text-secondary">
            Files you've shared with others
          </p>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {Array.from({ length: 6 }).map((_, index) => (
            <SkeletonFileCard key={index} />
          ))}
        </div>
      </div>
    );
  }

  return (
    <div className="p-6 space-y-6">
      {/* Header */}
      <div>
        <h1 className="text-2xl font-bold text-text-primary">Shared Files</h1>
        <p className="text-text-secondary">
          Files you've shared with others via public links
        </p>
      </div>

      {/* Shared Files List */}
      {sharedFiles.length > 0 ? (
        <div className="space-y-4">
          {sharedFiles.map((share) => (
            <div
              key={share.id}
              className="bg-background border border-divider rounded-lg p-6"
            >
              <div className="flex items-start justify-between">
                <div className="flex items-start space-x-4 flex-1">
                  {/* File Icon */}
                  <div className="flex-shrink-0">
                    <DocumentIcon className="h-10 w-10 text-accent" />
                  </div>

                  {/* File Info */}
                  <div className="flex-1 min-w-0">
                    <h3 className="text-lg font-medium text-text-primary truncate">
                      {share.file.name}
                    </h3>
                    <p className="text-sm text-text-secondary">
                      {(share.file.size / 1024 / 1024).toFixed(2)} MB • {share.file.mimeType}
                    </p>
                    
                    {/* Share Details */}
                    <div className="mt-2 flex flex-wrap items-center gap-4 text-sm text-text-secondary">
                      <span className="flex items-center">
                        <ShareIcon className="h-4 w-4 mr-1" />
                        {share.permission === 'view' ? 'View Only' : 'Can Edit'}
                      </span>
                      <span>
                        Accessed {share.accessCount} time{share.accessCount !== 1 ? 's' : ''}
                      </span>
                      {share.maxAccessCount && (
                        <span>
                          Max: {share.maxAccessCount}
                        </span>
                      )}
                      {share.expiresAt && (
                        <span>
                          Expires: {formatDate(new Date(share.expiresAt))}
                        </span>
                      )}
                    </div>

                    {/* Share URL */}
                    <div className="mt-3">
                      <div className="flex items-center space-x-2">
                        <input
                          type="text"
                          value={share.shareUrl}
                          readOnly
                          className="flex-1 px-3 py-2 text-sm bg-secondary border border-divider rounded font-mono"
                        />
                        <button
                          onClick={() => handleCopyShareUrl(share.shareUrl)}
                          className="px-3 py-2 text-sm bg-accent text-white rounded hover:bg-accent/90 transition-colors"
                        >
                          Copy
                        </button>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Actions */}
                <div className="flex-shrink-0 ml-4">
                  <button
                    onClick={() => handleRevokeShare(share.shareId)}
                    className="px-3 py-2 text-sm text-error hover:bg-error/10 rounded transition-colors"
                  >
                    Revoke
                  </button>
                </div>
              </div>

              {/* Share Stats */}
              <div className="mt-4 pt-4 border-t border-divider">
                <div className="flex items-center justify-between text-sm text-text-secondary">
                  <span>
                    Shared on {formatDate(new Date(share.createdAt))}
                  </span>
                  {share.lastAccessedAt && (
                    <span>
                      Last accessed {formatDate(new Date(share.lastAccessedAt))}
                    </span>
                  )}
                </div>
              </div>
            </div>
          ))}
        </div>
      ) : (
        <div className="text-center py-12">
          <ShareIcon className="h-12 w-12 text-text-secondary mx-auto mb-4" />
          <h3 className="text-lg font-medium text-text-primary mb-2">No shared files</h3>
          <p className="text-text-secondary mb-4">
            You haven't shared any files yet. Share files from your dashboard to see them here.
          </p>
        </div>
      )}
    </div>
  );
}
