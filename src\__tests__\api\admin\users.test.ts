import { describe, it, expect, beforeEach, afterEach, jest } from '@jest/globals';
import { GET, PUT } from '@/app/api/admin/users/route';
import { 
  createMockRequest, 
  mockAuth,
  mockMongoose,
  mockSession,
  mockAdminSession,
  generateTestUser,
  setupTestEnvironment,
  cleanupMocks,
  expectAuthError,
  expectForbiddenError,
} from '@/__tests__/utils/test-helpers';

// Mock dependencies
jest.mock('@/lib/auth');
jest.mock('@/models/User');
jest.mock('@/lib/mongodb');

const mockAuthFn = mockAuth();
const mockUser = mockMongoose();

describe('/api/admin/users', () => {
  beforeEach(() => {
    setupTestEnvironment();
    cleanupMocks();
    
    // Setup default mocks
    require('@/lib/auth').auth = mockAuthFn;
    require('@/models/User').User = mockUser;
  });

  afterEach(() => {
    cleanupMocks();
  });

  describe('GET', () => {
    it('should return users list for admin', async () => {
      mockAuthFn.mockResolvedValue(mockAdminSession);
      
      const testUsers = [
        generateTestUser({ id: '1', name: 'User 1', email: '<EMAIL>' }),
        generateTestUser({ id: '2', name: 'User 2', email: '<EMAIL>' }),
      ];
      
      mockUser.find.mockReturnValue({
        select: jest.fn().mockReturnValue({
          sort: jest.fn().mockReturnValue({
            skip: jest.fn().mockReturnValue({
              limit: jest.fn().mockResolvedValue(testUsers),
            }),
          }),
        }),
      });
      
      mockUser.countDocuments.mockResolvedValue(2);

      const request = createMockRequest('http://localhost:3000/api/admin/users');

      const response = await GET(request);
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data.users).toHaveLength(2);
      expect(data.users[0].name).toBe('User 1');
      expect(data.pagination.totalCount).toBe(2);
    });

    it('should reject non-admin users', async () => {
      mockAuthFn.mockResolvedValue(mockSession);

      const request = createMockRequest('http://localhost:3000/api/admin/users');

      const response = await GET(request);
      
      expectForbiddenError(response);
    });

    it('should reject unauthenticated requests', async () => {
      mockAuthFn.mockResolvedValue(null);

      const request = createMockRequest('http://localhost:3000/api/admin/users');

      const response = await GET(request);
      
      expectAuthError(response);
    });

    it('should support search functionality', async () => {
      mockAuthFn.mockResolvedValue(mockAdminSession);
      
      mockUser.find.mockReturnValue({
        select: jest.fn().mockReturnValue({
          sort: jest.fn().mockReturnValue({
            skip: jest.fn().mockReturnValue({
              limit: jest.fn().mockResolvedValue([]),
            }),
          }),
        }),
      });
      
      mockUser.countDocuments.mockResolvedValue(0);

      const request = createMockRequest('http://localhost:3000/api/admin/users?search=john');

      await GET(request);

      expect(mockUser.find).toHaveBeenCalledWith({
        $or: [
          { name: { $regex: 'john', $options: 'i' } },
          { email: { $regex: 'john', $options: 'i' } },
        ],
      });
    });

    it('should support pagination', async () => {
      mockAuthFn.mockResolvedValue(mockAdminSession);
      
      mockUser.find.mockReturnValue({
        select: jest.fn().mockReturnValue({
          sort: jest.fn().mockReturnValue({
            skip: jest.fn().mockReturnValue({
              limit: jest.fn().mockResolvedValue([]),
            }),
          }),
        }),
      });
      
      mockUser.countDocuments.mockResolvedValue(0);

      const request = createMockRequest('http://localhost:3000/api/admin/users?page=2&limit=10');

      await GET(request);

      // Should skip 10 items (page 2 with limit 10)
      expect(mockUser.find().select().sort().skip).toHaveBeenCalledWith(10);
      expect(mockUser.find().select().sort().skip().limit).toHaveBeenCalledWith(10);
    });
  });

  describe('PUT', () => {
    const updateData = {
      userId: '507f1f77bcf86cd799439011',
      allowPlatformS3: true,
    };

    it('should update user for admin', async () => {
      mockAuthFn.mockResolvedValue(mockAdminSession);
      
      const updatedUser = generateTestUser({ 
        id: updateData.userId,
        allowPlatformS3: true,
      });
      
      mockUser.findByIdAndUpdate.mockReturnValue({
        select: jest.fn().mockResolvedValue(updatedUser),
      });

      const request = createMockRequest('http://localhost:3000/api/admin/users', {
        method: 'PUT',
        body: updateData,
      });

      const response = await PUT(request);
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data.message).toContain('User updated successfully');
      expect(data.user.allowPlatformS3).toBe(true);
      expect(mockUser.findByIdAndUpdate).toHaveBeenCalledWith(
        updateData.userId,
        { allowPlatformS3: updateData.allowPlatformS3 },
        { new: true }
      );
    });

    it('should reject non-admin users', async () => {
      mockAuthFn.mockResolvedValue(mockSession);

      const request = createMockRequest('http://localhost:3000/api/admin/users', {
        method: 'PUT',
        body: updateData,
      });

      const response = await PUT(request);
      
      expectForbiddenError(response);
    });

    it('should reject unauthenticated requests', async () => {
      mockAuthFn.mockResolvedValue(null);

      const request = createMockRequest('http://localhost:3000/api/admin/users', {
        method: 'PUT',
        body: updateData,
      });

      const response = await PUT(request);
      
      expectAuthError(response);
    });

    it('should return 404 for non-existent user', async () => {
      mockAuthFn.mockResolvedValue(mockAdminSession);
      
      mockUser.findByIdAndUpdate.mockReturnValue({
        select: jest.fn().mockResolvedValue(null),
      });

      const request = createMockRequest('http://localhost:3000/api/admin/users', {
        method: 'PUT',
        body: updateData,
      });

      const response = await PUT(request);
      const data = await response.json();

      expect(response.status).toBe(404);
      expect(data.error).toContain('User not found');
    });

    it('should validate request data', async () => {
      mockAuthFn.mockResolvedValue(mockAdminSession);

      const invalidData = {
        userId: '',
        allowPlatformS3: 'not-a-boolean',
      };

      const request = createMockRequest('http://localhost:3000/api/admin/users', {
        method: 'PUT',
        body: invalidData,
      });

      const response = await PUT(request);
      const data = await response.json();

      expect(response.status).toBe(400);
      expect(data.error).toContain('Validation error');
    });

    it('should handle database errors', async () => {
      mockAuthFn.mockResolvedValue(mockAdminSession);
      
      mockUser.findByIdAndUpdate.mockReturnValue({
        select: jest.fn().mockRejectedValue(new Error('Database error')),
      });

      const request = createMockRequest('http://localhost:3000/api/admin/users', {
        method: 'PUT',
        body: updateData,
      });

      const response = await PUT(request);
      const data = await response.json();

      expect(response.status).toBe(500);
      expect(data.error).toContain('Failed to update user');
    });
  });
});
