/**
 * Development Dashboard API
 * 
 * Provides a comprehensive development dashboard with:
 * - System status and health checks
 * - Feature flag management
 * - Development tools and utilities
 * - Code generation and workflow automation
 * 
 * SECURITY: Automatically disabled in production
 */

import { NextRequest, NextResponse } from 'next/server';
import { isDevelopmentMode, DevLogger, getDevConfig } from '@/lib/dev-config';
import { 
  FeatureFlagManager, 
  DevDebugger, 
  CodeGenerator, 
  WorkflowAutomation 
} from '@/lib/dev-workflow';
import { handleApiError } from '@/lib/error-handler';
import { ErrorUtils } from '@/lib/error-utils';

export async function GET(request: NextRequest) {
  // Ensure this endpoint is only available in development
  if (!isDevelopmentMode()) {
    return NextResponse.json(
      { error: 'This endpoint is only available in development mode' },
      { status: 404 }
    );
  }

  try {
    const { searchParams } = new URL(request.url);
    const section = searchParams.get('section') || 'overview';

    DevLogger.debug(`Development dashboard accessed: ${section}`);

    switch (section) {
      case 'overview':
        return getDashboardOverview();
      
      case 'features':
        return getFeatureFlags();
      
      case 'health':
        return getSystemHealth();
      
      case 'tools':
        return getDevelopmentTools();
      
      case 'config':
        return getConfiguration();
      
      default:
        return NextResponse.json({
          error: 'Invalid section',
          availableSections: ['overview', 'features', 'health', 'tools', 'config']
        }, { status: 400 });
    }

  } catch (error) {
    return handleApiError(error, request);
  }
}

async function getDashboardOverview() {
  const config = getDevConfig();
  
  return NextResponse.json({
    section: 'overview',
    data: {
      environment: process.env.NODE_ENV,
      developmentMode: config.isDevelopment,
      timestamp: new Date().toISOString(),
      features: {
        authBypass: config.bypassAuth,
        debugLogging: config.enableDebugLogging,
        detailedErrors: config.enableDetailedErrors,
        performanceLogging: config.enablePerformanceLogging,
      },
      stats: {
        featureFlags: FeatureFlagManager.getAllFlags().length,
        enabledFlags: FeatureFlagManager.getAllFlags().filter(f => f.enabled).length,
        developmentOnlyFlags: FeatureFlagManager.getAllFlags().filter(f => f.developmentOnly).length,
      },
      quickActions: [
        { name: 'Test Authentication', url: '/api/dev/auth-test' },
        { name: 'Test Errors', url: '/api/dev/error-test' },
        { name: 'Generate Code', action: 'generate-code' },
        { name: 'Initialize Feature', action: 'init-feature' },
      ],
    },
    timestamp: new Date().toISOString(),
  });
}

async function getFeatureFlags() {
  const flags = FeatureFlagManager.getAllFlags();
  
  return NextResponse.json({
    section: 'features',
    data: {
      flags: flags.map(flag => ({
        ...flag,
        status: flag.enabled ? 'enabled' : 'disabled',
        scope: flag.developmentOnly ? 'development' : 'global',
      })),
      summary: {
        total: flags.length,
        enabled: flags.filter(f => f.enabled).length,
        disabled: flags.filter(f => !f.enabled).length,
        developmentOnly: flags.filter(f => f.developmentOnly).length,
      },
    },
    timestamp: new Date().toISOString(),
  });
}

async function getSystemHealth() {
  const healthChecks = [];

  // Database health check
  try {
    const { connectToDatabase } = await import('@/lib/mongodb');
    await connectToDatabase();
    healthChecks.push({
      component: 'database',
      status: 'healthy',
      message: 'Database connection successful',
    });
  } catch (error) {
    healthChecks.push({
      component: 'database',
      status: 'unhealthy',
      message: error instanceof Error ? error.message : 'Database connection failed',
    });
  }

  // Environment variables check
  const requiredEnvVars = [
    'NEXTAUTH_SECRET',
    'MONGODB_URI',
    'ENCRYPTION_KEY',
  ];
  
  const missingEnvVars = requiredEnvVars.filter(varName => !process.env[varName]);
  healthChecks.push({
    component: 'environment',
    status: missingEnvVars.length === 0 ? 'healthy' : 'warning',
    message: missingEnvVars.length === 0 
      ? 'All required environment variables are set'
      : `Missing environment variables: ${missingEnvVars.join(', ')}`,
    details: {
      required: requiredEnvVars.length,
      missing: missingEnvVars.length,
      missingVars: missingEnvVars,
    },
  });

  // Development configuration check
  const config = getDevConfig();
  healthChecks.push({
    component: 'development-config',
    status: 'healthy',
    message: 'Development configuration loaded',
    details: {
      authBypass: config.bypassAuth,
      debugLogging: config.enableDebugLogging,
      detailedErrors: config.enableDetailedErrors,
      defaultUser: config.defaultUser?.email,
    },
  });

  const overallStatus = healthChecks.every(check => check.status === 'healthy') 
    ? 'healthy' 
    : healthChecks.some(check => check.status === 'unhealthy') 
      ? 'unhealthy' 
      : 'warning';

  return NextResponse.json({
    section: 'health',
    data: {
      overallStatus,
      checks: healthChecks,
      timestamp: new Date().toISOString(),
    },
    timestamp: new Date().toISOString(),
  });
}

async function getDevelopmentTools() {
  return NextResponse.json({
    section: 'tools',
    data: {
      codeGeneration: {
        available: true,
        tools: [
          {
            name: 'API Route Generator',
            description: 'Generate boilerplate API route with authentication',
            endpoint: '/api/dev/generate/api-route',
          },
          {
            name: 'Test File Generator',
            description: 'Generate test file boilerplate',
            endpoint: '/api/dev/generate/test-file',
          },
          {
            name: 'Model Generator',
            description: 'Generate Mongoose model boilerplate',
            endpoint: '/api/dev/generate/model',
          },
        ],
      },
      workflow: {
        available: true,
        tools: [
          {
            name: 'Feature Initializer',
            description: 'Initialize complete feature with all necessary files',
            endpoint: '/api/dev/workflow/init-feature',
          },
          {
            name: 'Checklist Generator',
            description: 'Generate development checklist for new features',
            endpoint: '/api/dev/workflow/checklist',
          },
        ],
      },
      testing: {
        available: true,
        tools: [
          {
            name: 'Authentication Test',
            description: 'Test authentication flows and bypass',
            endpoint: '/api/dev/auth-test',
          },
          {
            name: 'Error Handling Test',
            description: 'Test error handling and context generation',
            endpoint: '/api/dev/error-test',
          },
        ],
      },
      debugging: {
        available: true,
        features: [
          'Breakpoint logging',
          'Variable watching',
          'Performance timing',
          'API call logging',
          'Development assertions',
        ],
      },
    },
    timestamp: new Date().toISOString(),
  });
}

async function getConfiguration() {
  const config = getDevConfig();
  
  return NextResponse.json({
    section: 'config',
    data: {
      environment: {
        NODE_ENV: process.env.NODE_ENV,
        isDevelopment: config.isDevelopment,
      },
      development: {
        bypassAuth: config.bypassAuth,
        enableDebugLogging: config.enableDebugLogging,
        enableDetailedErrors: config.enableDetailedErrors,
        enablePerformanceLogging: config.enablePerformanceLogging,
        defaultUser: config.defaultUser,
      },
      database: {
        uri: process.env.MONGODB_URI ? '[SET]' : '[NOT SET]',
        connected: '[CHECK HEALTH SECTION]',
      },
      authentication: {
        secret: process.env.NEXTAUTH_SECRET ? '[SET]' : '[NOT SET]',
        adminEmails: process.env.ADMIN_EMAILS ? '[SET]' : '[NOT SET]',
      },
      encryption: {
        key: process.env.ENCRYPTION_KEY ? '[SET]' : '[NOT SET]',
      },
      s3: {
        accessKeyId: process.env.AWS_ACCESS_KEY_ID ? '[SET]' : '[NOT SET]',
        secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY ? '[SET]' : '[NOT SET]',
        region: process.env.AWS_REGION || '[NOT SET]',
        bucket: process.env.S3_BUCKET_NAME || '[NOT SET]',
      },
    },
    timestamp: new Date().toISOString(),
  });
}

export async function POST(request: NextRequest) {
  // Ensure this endpoint is only available in development
  if (!isDevelopmentMode()) {
    return NextResponse.json(
      { error: 'This endpoint is only available in development mode' },
      { status: 404 }
    );
  }

  try {
    const body = await request.json();
    const { action, data } = body;

    DevLogger.debug(`Development dashboard action: ${action}`, data);

    switch (action) {
      case 'toggle-feature-flag':
        return toggleFeatureFlag(data);
      
      case 'generate-code':
        return generateCode(data);
      
      case 'init-feature':
        return initializeFeature(data);
      
      case 'create-checklist':
        return createChecklist(data);
      
      default:
        return NextResponse.json({
          error: 'Invalid action',
          availableActions: ['toggle-feature-flag', 'generate-code', 'init-feature', 'create-checklist']
        }, { status: 400 });
    }

  } catch (error) {
    return handleApiError(error, request);
  }
}

async function toggleFeatureFlag(data: { flagName: string; enabled: boolean }) {
  const { flagName, enabled } = data;
  
  if (!flagName) {
    throw ErrorUtils.validation('Flag name is required', 'toggleFeatureFlag');
  }

  const success = FeatureFlagManager.updateFlag(flagName, { enabled });
  
  if (!success) {
    throw ErrorUtils.notFound(`Feature flag '${flagName}' not found`, 'toggleFeatureFlag');
  }

  return NextResponse.json({
    action: 'toggle-feature-flag',
    result: {
      flagName,
      enabled,
      message: `Feature flag '${flagName}' ${enabled ? 'enabled' : 'disabled'}`,
    },
    timestamp: new Date().toISOString(),
  });
}

async function generateCode(data: { type: string; options: any }) {
  const { type, options } = data;
  
  let generatedCode: string;
  
  switch (type) {
    case 'api-route':
      generatedCode = CodeGenerator.generateApiRoute(options);
      break;
    
    case 'test-file':
      generatedCode = CodeGenerator.generateTestFile(options);
      break;
    
    case 'model':
      generatedCode = CodeGenerator.generateModel(options);
      break;
    
    default:
      throw ErrorUtils.validation(`Invalid code generation type: ${type}`, 'generateCode');
  }

  return NextResponse.json({
    action: 'generate-code',
    result: {
      type,
      code: generatedCode,
      message: `${type} code generated successfully`,
    },
    timestamp: new Date().toISOString(),
  });
}

async function initializeFeature(data: { name: string; options?: any }) {
  const { name, options = {} } = data;
  
  if (!name) {
    throw ErrorUtils.validation('Feature name is required', 'initializeFeature');
  }

  const result = await WorkflowAutomation.initializeFeature({
    name,
    ...options,
  });

  return NextResponse.json({
    action: 'init-feature',
    result: {
      featureName: name,
      filesGenerated: result.files.length,
      files: result.files.map(f => f.path),
      instructions: result.instructions,
      message: `Feature '${name}' initialized successfully`,
    },
    timestamp: new Date().toISOString(),
  });
}

async function createChecklist(data: { featureName: string }) {
  const { featureName } = data;
  
  if (!featureName) {
    throw ErrorUtils.validation('Feature name is required', 'createChecklist');
  }

  const checklist = WorkflowAutomation.createFeatureChecklist(featureName);

  return NextResponse.json({
    action: 'create-checklist',
    result: {
      featureName,
      checklist,
      message: `Development checklist created for '${featureName}'`,
    },
    timestamp: new Date().toISOString(),
  });
}
