# S3 Real-time Synchronization

This document describes the real-time S3 synchronization feature implemented in Drivn.

## Overview

The S3 synchronization feature keeps your Drivn database in sync with your S3 bucket, ensuring that files added, modified, or deleted in S3 are automatically reflected in your Drivn interface.

## Components

### 1. S3SyncService (`src/lib/sync.ts`)

The core synchronization service that handles:
- **Full Sync**: Compares all files between S3 and database
- **File Detection**: Identifies new, updated, and deleted files
- **Metadata Sync**: Updates file sizes, ETags, and timestamps
- **MIME Type Detection**: Automatically determines file types

### 2. Webhook Handler (`src/app/api/webhooks/s3/route.ts`)

Processes S3 event notifications for real-time updates:
- **Object Created**: Adds new files to database
- **Object Deleted**: Removes files from database
- **Event Validation**: Verifies webhook signatures
- **User Context**: Maps S3 objects to user accounts

### 3. Manual Sync API (`src/app/api/sync/route.ts`)

Provides on-demand synchronization:
- **POST /api/sync**: Triggers manual sync
- **GET /api/sync**: Check sync status
- **Progress Tracking**: Prevents concurrent syncs
- **Error Reporting**: Detailed sync results

### 4. Background Sync Service (`src/lib/background-sync.ts`)

Handles periodic automatic synchronization:
- **Scheduled Sync**: Configurable intervals
- **User Management**: Syncs multiple users
- **Resource Management**: Prevents overlapping syncs
- **Statistics**: Tracks sync performance

### 5. Sync Manager UI (`src/components/sync/SyncManager.tsx`)

User interface for sync management:
- **Manual Sync**: One-click synchronization
- **Sync Status**: Real-time progress display
- **Detailed Results**: Shows added/updated/deleted counts
- **Settings**: Enable/disable automatic sync

## Setup Instructions

### 1. Database Schema Updates

The following fields have been added to support synchronization:

**File Model:**
```typescript
syncedAt?: Date;    // Last sync timestamp
etag?: string;      // S3 ETag for change detection
```

**User Model:**
```typescript
lastSyncAt?: Date;  // User's last sync time
syncEnabled?: boolean; // Auto-sync preference
```

### 2. Environment Variables

Add these optional environment variables:

```env
# Background sync configuration
ENABLE_BACKGROUND_SYNC=true
SYNC_INTERVAL_MINUTES=60

# Webhook security
S3_WEBHOOK_SECRET=your-webhook-secret
```

### 3. S3 Event Notifications (Optional)

For real-time sync, configure S3 event notifications:

1. **Create SNS Topic** (AWS) or equivalent
2. **Configure Bucket Events**:
   - `s3:ObjectCreated:*`
   - `s3:ObjectRemoved:*`
3. **Set Webhook URL**: `https://your-domain.com/api/webhooks/s3`

### 4. CORS Configuration

Ensure your S3 bucket has proper CORS configuration (already documented in storage settings).

## Usage

### Manual Sync

1. Navigate to **Settings > Storage**
2. Configure your S3 credentials
3. Use the **Sync Manager** section
4. Click **"Sync Now"** to start manual sync

### Automatic Sync

1. Enable **"Automatic Sync"** in Sync Manager
2. Background service will sync periodically
3. Check sync status and last sync time

### API Usage

```javascript
// Trigger manual sync
const response = await fetch('/api/sync', { method: 'POST' });
const result = await response.json();

// Check sync status
const status = await fetch('/api/sync');
const data = await status.json();

// Update sync settings
await fetch('/api/sync/settings', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({ syncEnabled: true })
});
```

## How It Works

### Sync Process

1. **List S3 Objects**: Retrieves all files in user's S3 prefix
2. **Compare with Database**: Identifies differences using ETags and timestamps
3. **Apply Changes**:
   - **Add**: Create database records for new S3 files
   - **Update**: Sync metadata for changed files
   - **Delete**: Remove database records for deleted S3 files

### Change Detection

- **ETags**: Primary method for detecting file changes
- **File Size**: Secondary validation
- **Timestamps**: Last resort comparison
- **User Prefix**: Files organized by `users/{userId}/` pattern

### Error Handling

- **Partial Failures**: Continues sync even if some files fail
- **Error Reporting**: Detailed error messages for troubleshooting
- **Retry Logic**: Built-in retry with exponential backoff
- **Graceful Degradation**: Manual sync available if webhooks fail

## Security Considerations

### Webhook Security

- **Signature Verification**: Validates webhook authenticity
- **Secret Token**: Prevents unauthorized webhook calls
- **User Validation**: Ensures files belong to valid users

### Access Control

- **User Isolation**: Each user can only sync their own files
- **S3 Credentials**: Stored encrypted in secure cookies
- **Admin Functions**: Background sync management restricted to admins

## Performance

### Optimization Features

- **Batch Processing**: Handles multiple files efficiently
- **Concurrent Limits**: Prevents resource exhaustion
- **Caching**: Reduces redundant API calls
- **Incremental Sync**: Only processes changed files

### Monitoring

- **Sync Statistics**: Track success/failure rates
- **Performance Metrics**: Monitor sync duration
- **Error Logging**: Detailed error tracking
- **Health Checks**: Service status monitoring

## Troubleshooting

### Common Issues

1. **Sync Fails**: Check S3 credentials and permissions
2. **Missing Files**: Verify S3 bucket and prefix structure
3. **Webhook Errors**: Validate webhook URL and secret
4. **Performance Issues**: Check sync interval and file count

### Debug Information

- Check browser console for client-side errors
- Review server logs for API errors
- Monitor S3 access logs for permission issues
- Verify database connectivity and schema

## Future Enhancements

- **Real-time WebSocket Updates**: Live sync status in UI
- **Selective Sync**: Choose specific folders to sync
- **Conflict Resolution**: Handle simultaneous changes
- **Sync History**: Track sync operations over time
- **Multi-bucket Support**: Sync from multiple S3 buckets
