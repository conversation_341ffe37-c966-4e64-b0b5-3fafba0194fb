'use client';

import React from 'react';
import { cn } from '@/lib/utils';

interface SkeletonProps extends React.HTMLAttributes<HTMLDivElement> {
  variant?: 'text' | 'circular' | 'rectangular';
  width?: string | number;
  height?: string | number;
}

const Skeleton: React.FC<SkeletonProps> = ({
  className,
  variant = 'rectangular',
  width,
  height,
  style,
  ...props
}) => {
  const variants = {
    text: 'rounded',
    circular: 'rounded-full',
    rectangular: 'rounded-lg',
  };

  return (
    <div
      className={cn(
        'animate-pulse bg-secondary',
        variants[variant],
        className
      )}
      style={{
        width,
        height,
        ...style,
      }}
      {...props}
    />
  );
};

// Predefined skeleton components for common use cases
const SkeletonText: React.FC<{ lines?: number; className?: string }> = ({
  lines = 1,
  className,
}) => (
  <div className={cn('space-y-2', className)}>
    {Array.from({ length: lines }).map((_, index) => (
      <Skeleton
        key={index}
        variant="text"
        height="1rem"
        width={index === lines - 1 ? '75%' : '100%'}
      />
    ))}
  </div>
);

const SkeletonCard: React.FC<{ className?: string }> = ({ className }) => (
  <div className={cn('p-4 border border-divider rounded-lg', className)}>
    <div className="flex items-center space-x-3 mb-3">
      <Skeleton variant="circular" width="2.5rem" height="2.5rem" />
      <div className="flex-1">
        <Skeleton variant="text" height="1rem" width="60%" className="mb-2" />
        <Skeleton variant="text" height="0.75rem" width="40%" />
      </div>
    </div>
    <SkeletonText lines={3} />
  </div>
);

const SkeletonFileCard: React.FC<{ className?: string }> = ({ className }) => (
  <div className={cn('p-4 border border-divider rounded-lg', className)}>
    <div className="flex items-center space-x-3">
      <Skeleton variant="rectangular" width="2.5rem" height="2.5rem" />
      <div className="flex-1">
        <Skeleton variant="text" height="1rem" width="70%" className="mb-1" />
        <Skeleton variant="text" height="0.75rem" width="40%" />
      </div>
      <Skeleton variant="rectangular" width="1.5rem" height="1.5rem" />
    </div>
  </div>
);

const SkeletonTable: React.FC<{ rows?: number; columns?: number; className?: string }> = ({
  rows = 5,
  columns = 4,
  className,
}) => (
  <div className={cn('space-y-3', className)}>
    {/* Header */}
    <div className="flex space-x-4">
      {Array.from({ length: columns }).map((_, index) => (
        <Skeleton key={index} variant="text" height="1rem" width="100%" />
      ))}
    </div>
    {/* Rows */}
    {Array.from({ length: rows }).map((_, rowIndex) => (
      <div key={rowIndex} className="flex space-x-4">
        {Array.from({ length: columns }).map((_, colIndex) => (
          <Skeleton key={colIndex} variant="text" height="0.875rem" width="100%" />
        ))}
      </div>
    ))}
  </div>
);

export { Skeleton, SkeletonText, SkeletonCard, SkeletonFileCard, SkeletonTable };
