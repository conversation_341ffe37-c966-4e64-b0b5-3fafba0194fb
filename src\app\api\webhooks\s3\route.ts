import { NextRequest, NextResponse } from 'next/server';
import { File } from '@/models/File';
import { User } from '@/models/User';
import connectDB from '@/lib/mongodb';
import { S3Service } from '@/lib/s3';
import { S3SyncService } from '@/lib/sync';

interface S3Event {
  eventVersion: string;
  eventSource: string;
  eventTime: string;
  eventName: string;
  s3: {
    bucket: {
      name: string;
    };
    object: {
      key: string;
      size?: number;
      eTag?: string;
    };
  };
}

interface S3WebhookPayload {
  Records: S3Event[];
}

export async function POST(request: NextRequest) {
  try {
    // Verify webhook signature if configured
    const signature = request.headers.get('x-amz-sns-signature');
    const webhookSecret = process.env.S3_WEBHOOK_SECRET;
    
    if (webhookSecret && signature) {
      // In a production environment, you should verify the SNS signature
      // This is a simplified implementation
      const providedSecret = request.headers.get('x-webhook-secret');
      if (providedSecret !== webhookSecret) {
        return NextResponse.json(
          { error: 'Invalid webhook signature' },
          { status: 401 }
        );
      }
    }

    const payload: S3WebhookPayload = await request.json();
    
    if (!payload.Records || !Array.isArray(payload.Records)) {
      return NextResponse.json(
        { error: 'Invalid webhook payload' },
        { status: 400 }
      );
    }

    await connectDB();

    const results = [];

    for (const record of payload.Records) {
      try {
        const result = await processS3Event(record);
        results.push(result);
      } catch (error) {
        console.error('Error processing S3 event:', error);
        results.push({
          success: false,
          error: error instanceof Error ? error.message : 'Unknown error',
          eventName: record.eventName,
          objectKey: record.s3.object.key
        });
      }
    }

    return NextResponse.json({
      message: 'Webhook processed',
      results
    });

  } catch (error) {
    console.error('S3 webhook error:', error);
    
    return NextResponse.json(
      { error: 'Webhook processing failed' },
      { status: 500 }
    );
  }
}

async function processS3Event(event: S3Event) {
  const { eventName, s3 } = event;
  const { bucket, object } = s3;
  const objectKey = object.key;

  // Extract user ID from object key (format: users/{userId}/...)
  const keyParts = objectKey.split('/');
  if (keyParts.length < 3 || keyParts[0] !== 'users') {
    return {
      success: false,
      error: 'Invalid object key format',
      eventName,
      objectKey
    };
  }

  const userId = keyParts[1];

  // Find user and their S3 credentials
  const user = await User.findById(userId);
  if (!user) {
    return {
      success: false,
      error: 'User not found',
      eventName,
      objectKey,
      userId
    };
  }

  // Handle different event types
  switch (eventName) {
    case 's3:ObjectCreated:Put':
    case 's3:ObjectCreated:Post':
    case 's3:ObjectCreated:Copy':
    case 's3:ObjectCreated:CompleteMultipartUpload':
      return await handleObjectCreated(userId, objectKey, object);

    case 's3:ObjectRemoved:Delete':
    case 's3:ObjectRemoved:DeleteMarkerCreated':
      return await handleObjectDeleted(userId, objectKey);

    default:
      return {
        success: true,
        message: 'Event type not handled',
        eventName,
        objectKey
      };
  }
}

async function handleObjectCreated(userId: string, objectKey: string, s3Object: any) {
  try {
    // Check if file already exists in database
    const existingFile = await File.findOne({
      userId,
      s3Key: objectKey
    });

    if (existingFile) {
      // Update existing file
      await File.findByIdAndUpdate(existingFile._id, {
        size: s3Object.size || existingFile.size,
        etag: s3Object.eTag?.replace(/"/g, '') || existingFile.etag,
        syncedAt: new Date(),
        updatedAt: new Date()
      });

      return {
        success: true,
        action: 'updated',
        objectKey,
        fileId: existingFile._id
      };
    } else {
      // Create new file record
      const fileName = objectKey.split('/').pop() || 'unknown';
      const cleanFileName = fileName.replace(/^\d+-/, '');
      
      const newFile = await File.create({
        name: cleanFileName,
        originalName: cleanFileName,
        size: s3Object.size || 0,
        mimeType: getMimeTypeFromExtension(cleanFileName),
        s3Key: objectKey,
        userId,
        isPublic: false,
        downloadCount: 0,
        syncedAt: new Date(),
        etag: s3Object.eTag?.replace(/"/g, '')
      });

      return {
        success: true,
        action: 'created',
        objectKey,
        fileId: newFile._id
      };
    }
  } catch (error) {
    throw new Error(`Failed to handle object created: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
}

async function handleObjectDeleted(userId: string, objectKey: string) {
  try {
    const deletedFile = await File.findOneAndDelete({
      userId,
      s3Key: objectKey
    });

    if (deletedFile) {
      return {
        success: true,
        action: 'deleted',
        objectKey,
        fileId: deletedFile._id
      };
    } else {
      return {
        success: true,
        action: 'not_found',
        objectKey,
        message: 'File not found in database'
      };
    }
  } catch (error) {
    throw new Error(`Failed to handle object deleted: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
}

function getMimeTypeFromExtension(fileName: string): string {
  const extension = fileName.split('.').pop()?.toLowerCase();
  
  const mimeTypes: Record<string, string> = {
    // Images
    'jpg': 'image/jpeg',
    'jpeg': 'image/jpeg',
    'png': 'image/png',
    'gif': 'image/gif',
    'webp': 'image/webp',
    'svg': 'image/svg+xml',
    
    // Documents
    'pdf': 'application/pdf',
    'doc': 'application/msword',
    'docx': 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    'txt': 'text/plain',
    'html': 'text/html',
    'css': 'text/css',
    'js': 'text/javascript',
    'json': 'application/json',
    
    // Video
    'mp4': 'video/mp4',
    'webm': 'video/webm',
    'ogg': 'video/ogg',
    
    // Audio
    'mp3': 'audio/mpeg',
    'wav': 'audio/wav',
    
    // Archives
    'zip': 'application/zip',
    'rar': 'application/x-rar-compressed'
  };

  return mimeTypes[extension || ''] || 'application/octet-stream';
}

// Handle SNS subscription confirmation
export async function GET(request: NextRequest) {
  const { searchParams } = new URL(request.url);
  const subscribeURL = searchParams.get('SubscribeURL');
  
  if (subscribeURL) {
    // In a real implementation, you would confirm the SNS subscription
    // by making a GET request to the SubscribeURL
    return NextResponse.json({
      message: 'SNS subscription confirmation received',
      subscribeURL
    });
  }

  return NextResponse.json({
    message: 'S3 webhook endpoint is active',
    timestamp: new Date().toISOString()
  });
}
