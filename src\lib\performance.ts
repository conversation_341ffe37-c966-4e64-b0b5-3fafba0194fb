import { NextRequest, NextResponse } from 'next/server';

// Performance monitoring utilities
export class PerformanceMonitor {
  private static metrics: Map<string, number[]> = new Map();
  private static readonly MAX_METRICS = 1000; // Keep last 1000 measurements per endpoint

  static startTimer(): () => number {
    const start = performance.now();
    return () => performance.now() - start;
  }

  static recordMetric(endpoint: string, duration: number): void {
    if (!this.metrics.has(endpoint)) {
      this.metrics.set(endpoint, []);
    }

    const metrics = this.metrics.get(endpoint)!;
    metrics.push(duration);

    // Keep only the last MAX_METRICS measurements
    if (metrics.length > this.MAX_METRICS) {
      metrics.shift();
    }
  }

  static getMetrics(endpoint: string): {
    count: number;
    avg: number;
    min: number;
    max: number;
    p95: number;
    p99: number;
  } | null {
    const metrics = this.metrics.get(endpoint);
    if (!metrics || metrics.length === 0) {
      return null;
    }

    const sorted = [...metrics].sort((a, b) => a - b);
    const count = sorted.length;
    const sum = sorted.reduce((a, b) => a + b, 0);

    return {
      count,
      avg: sum / count,
      min: sorted[0],
      max: sorted[count - 1],
      p95: sorted[Math.floor(count * 0.95)],
      p99: sorted[Math.floor(count * 0.99)],
    };
  }

  static getAllMetrics(): Record<string, ReturnType<typeof PerformanceMonitor.getMetrics>> {
    const result: Record<string, ReturnType<typeof PerformanceMonitor.getMetrics>> = {};
    
    for (const [endpoint] of this.metrics) {
      result[endpoint] = this.getMetrics(endpoint);
    }

    return result;
  }

  static clearMetrics(endpoint?: string): void {
    if (endpoint) {
      this.metrics.delete(endpoint);
    } else {
      this.metrics.clear();
    }
  }
}

// Middleware wrapper for performance monitoring
export function withPerformanceMonitoring(
  handler: (request: NextRequest) => Promise<NextResponse>
) {
  return async (request: NextRequest): Promise<NextResponse> => {
    const stopTimer = PerformanceMonitor.startTimer();
    const endpoint = `${request.method} ${request.nextUrl.pathname}`;

    try {
      const response = await handler(request);
      const duration = stopTimer();
      
      PerformanceMonitor.recordMetric(endpoint, duration);
      
      // Add performance headers
      response.headers.set('X-Response-Time', `${duration.toFixed(2)}ms`);
      
      return response;
    } catch (error) {
      const duration = stopTimer();
      PerformanceMonitor.recordMetric(`${endpoint}:error`, duration);
      throw error;
    }
  };
}

// Database query performance monitoring
export class DatabaseMonitor {
  private static queries: Map<string, number[]> = new Map();
  private static readonly MAX_QUERIES = 500;

  static recordQuery(operation: string, duration: number): void {
    if (!this.queries.has(operation)) {
      this.queries.set(operation, []);
    }

    const queries = this.queries.get(operation)!;
    queries.push(duration);

    if (queries.length > this.MAX_QUERIES) {
      queries.shift();
    }
  }

  static getQueryMetrics(operation: string): {
    count: number;
    avg: number;
    min: number;
    max: number;
    slowQueries: number; // queries > 1000ms
  } | null {
    const queries = this.queries.get(operation);
    if (!queries || queries.length === 0) {
      return null;
    }

    const count = queries.length;
    const sum = queries.reduce((a, b) => a + b, 0);
    const slowQueries = queries.filter(q => q > 1000).length;

    return {
      count,
      avg: sum / count,
      min: Math.min(...queries),
      max: Math.max(...queries),
      slowQueries,
    };
  }

  static getAllQueryMetrics(): Record<string, ReturnType<typeof DatabaseMonitor.getQueryMetrics>> {
    const result: Record<string, ReturnType<typeof DatabaseMonitor.getQueryMetrics>> = {};
    
    for (const [operation] of this.queries) {
      result[operation] = this.getQueryMetrics(operation);
    }

    return result;
  }
}

// Memory usage monitoring
export function getMemoryUsage(): {
  heapUsed: number;
  heapTotal: number;
  external: number;
  rss: number;
  heapUsedMB: number;
  heapTotalMB: number;
  externalMB: number;
  rssMB: number;
} {
  const usage = process.memoryUsage();
  
  return {
    heapUsed: usage.heapUsed,
    heapTotal: usage.heapTotal,
    external: usage.external,
    rss: usage.rss,
    heapUsedMB: Math.round(usage.heapUsed / 1024 / 1024 * 100) / 100,
    heapTotalMB: Math.round(usage.heapTotal / 1024 / 1024 * 100) / 100,
    externalMB: Math.round(usage.external / 1024 / 1024 * 100) / 100,
    rssMB: Math.round(usage.rss / 1024 / 1024 * 100) / 100,
  };
}

// Cache utilities for performance optimization
export class SimpleCache<T> {
  private cache = new Map<string, { value: T; expires: number }>();
  private readonly defaultTTL: number;

  constructor(defaultTTL: number = 5 * 60 * 1000) { // 5 minutes default
    this.defaultTTL = defaultTTL;
    
    // Cleanup expired entries every minute
    setInterval(() => this.cleanup(), 60 * 1000);
  }

  set(key: string, value: T, ttl?: number): void {
    const expires = Date.now() + (ttl || this.defaultTTL);
    this.cache.set(key, { value, expires });
  }

  get(key: string): T | null {
    const entry = this.cache.get(key);
    
    if (!entry) {
      return null;
    }

    if (Date.now() > entry.expires) {
      this.cache.delete(key);
      return null;
    }

    return entry.value;
  }

  has(key: string): boolean {
    return this.get(key) !== null;
  }

  delete(key: string): boolean {
    return this.cache.delete(key);
  }

  clear(): void {
    this.cache.clear();
  }

  size(): number {
    return this.cache.size;
  }

  private cleanup(): void {
    const now = Date.now();
    for (const [key, entry] of this.cache.entries()) {
      if (now > entry.expires) {
        this.cache.delete(key);
      }
    }
  }
}

// Global cache instances
export const responseCache = new SimpleCache<any>(5 * 60 * 1000); // 5 minutes
export const userCache = new SimpleCache<any>(10 * 60 * 1000); // 10 minutes
export const fileCache = new SimpleCache<any>(15 * 60 * 1000); // 15 minutes

// Performance optimization helpers
export function debounce<T extends (...args: any[]) => any>(
  func: T,
  wait: number
): (...args: Parameters<T>) => void {
  let timeout: NodeJS.Timeout;
  return (...args: Parameters<T>) => {
    clearTimeout(timeout);
    timeout = setTimeout(() => func(...args), wait);
  };
}

export function throttle<T extends (...args: any[]) => any>(
  func: T,
  limit: number
): (...args: Parameters<T>) => void {
  let inThrottle: boolean;
  return (...args: Parameters<T>) => {
    if (!inThrottle) {
      func(...args);
      inThrottle = true;
      setTimeout(() => inThrottle = false, limit);
    }
  };
}

// Async retry utility with exponential backoff
export async function retryWithBackoff<T>(
  fn: () => Promise<T>,
  maxRetries: number = 3,
  baseDelay: number = 1000
): Promise<T> {
  let lastError: Error;

  for (let attempt = 0; attempt <= maxRetries; attempt++) {
    try {
      return await fn();
    } catch (error) {
      lastError = error as Error;
      
      if (attempt === maxRetries) {
        break;
      }

      const delay = baseDelay * Math.pow(2, attempt);
      await new Promise(resolve => setTimeout(resolve, delay));
    }
  }

  throw lastError!;
}
