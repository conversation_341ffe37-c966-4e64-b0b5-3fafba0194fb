/**
 * Error Utilities for Enhanced Error Handling
 * 
 * This module provides utilities for creating errors with rich context,
 * making debugging easier and providing AI-friendly error descriptions.
 */

import { 
  AppError, 
  ValidationError, 
  AuthenticationError, 
  AuthorizationError, 
  NotFoundError, 
  Conflict<PERSON>rror,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON>rro<PERSON><PERSON><PERSON><PERSON>
} from './error-handler';
import { NextRequest } from 'next/server';

/**
 * Error creation utilities with automatic context detection
 */
export class ErrorUtils {
  /**
   * Create an error with automatic location detection
   * Usage: throw ErrorUtils.create('Something went wrong', 'MyFunction', { userId: 123 });
   */
  static create(
    message: string,
    functionName: string,
    variables?: Record<string, any>,
    statusCode: number = 500
  ): AppError {
    // Get the calling file from stack trace
    const stack = new Error().stack;
    const callerLine = stack?.split('\n')[2];
    const fileMatch = callerLine?.match(/\((.+):(\d+):(\d+)\)/) || callerLine?.match(/at (.+):(\d+):(\d+)/);
    
    const file = fileMatch ? fileMatch[1].split('/').pop() || fileMatch[1] : 'unknown';
    const line = fileMatch ? parseInt(fileMatch[2]) : undefined;
    const column = fileMatch ? parseInt(fileMatch[3]) : undefined;

    return new AppError(message, statusCode, true, undefined, {
      location: {
        file,
        function: functionName,
        line,
        column,
      },
      variables,
    });
  }

  /**
   * Create a validation error with context
   */
  static validation(
    message: string,
    functionName: string,
    validationDetails?: any,
    variables?: Record<string, any>
  ): ValidationError {
    const context = this.getLocationContext(functionName);
    return new ValidationError(message, validationDetails, {
      ...context,
      variables,
    });
  }

  /**
   * Create an authentication error with context
   */
  static authentication(
    message: string = 'Authentication required',
    functionName: string,
    variables?: Record<string, any>
  ): AuthenticationError {
    const context = this.getLocationContext(functionName);
    return new AuthenticationError(message, {
      ...context,
      variables,
    });
  }

  /**
   * Create an authorization error with context
   */
  static authorization(
    message: string = 'Access denied',
    functionName: string,
    variables?: Record<string, any>
  ): AuthorizationError {
    const context = this.getLocationContext(functionName);
    return new AuthorizationError(message, {
      ...context,
      variables,
    });
  }

  /**
   * Create a not found error with context
   */
  static notFound(
    message: string = 'Resource not found',
    functionName: string,
    variables?: Record<string, any>
  ): NotFoundError {
    const context = this.getLocationContext(functionName);
    return new NotFoundError(message, {
      ...context,
      variables,
    });
  }

  /**
   * Create a conflict error with context
   */
  static conflict(
    message: string = 'Resource conflict',
    functionName: string,
    variables?: Record<string, any>
  ): ConflictError {
    const context = this.getLocationContext(functionName);
    return new ConflictError(message, {
      ...context,
      variables,
    });
  }

  /**
   * Wrap an async function with error handling
   */
  static async wrap<T>(
    fn: () => Promise<T>,
    functionName: string,
    context?: Partial<ErrorContext>
  ): Promise<T> {
    try {
      return await fn();
    } catch (error) {
      if (error instanceof AppError) {
        // Add additional context to existing AppError
        if (context) {
          error.context = { ...error.context, ...context };
        }
        throw error;
      } else {
        // Convert unknown error to AppError with context
        const locationContext = this.getLocationContext(functionName);
        throw new AppError(
          error instanceof Error ? error.message : 'Unknown error',
          500,
          true,
          'WRAPPED_ERROR',
          {
            ...locationContext,
            ...context,
            originalError: error instanceof Error ? {
              name: error.name,
              message: error.message,
              stack: error.stack,
            } : error,
          }
        );
      }
    }
  }

  /**
   * Create error with request context
   */
  static withRequest(
    message: string,
    functionName: string,
    request: NextRequest,
    variables?: Record<string, any>,
    statusCode: number = 500
  ): AppError {
    const locationContext = this.getLocationContext(functionName);
    
    return new AppError(message, statusCode, true, undefined, {
      ...locationContext,
      variables,
      request: {
        url: request.url,
        method: request.method,
        headers: this.sanitizeHeaders(Object.fromEntries(request.headers.entries())),
      },
    });
  }

  /**
   * Get location context from stack trace
   */
  private static getLocationContext(functionName: string): ErrorContext {
    const stack = new Error().stack;
    const callerLine = stack?.split('\n')[3]; // Go one level deeper since we're in a utility
    const fileMatch = callerLine?.match(/\((.+):(\d+):(\d+)\)/) || callerLine?.match(/at (.+):(\d+):(\d+)/);
    
    const file = fileMatch ? fileMatch[1].split('/').pop() || fileMatch[1] : 'unknown';
    const line = fileMatch ? parseInt(fileMatch[2]) : undefined;
    const column = fileMatch ? parseInt(fileMatch[3]) : undefined;

    return {
      location: {
        file,
        function: functionName,
        line,
        column,
      },
    };
  }

  /**
   * Sanitize headers to remove sensitive information
   */
  private static sanitizeHeaders(headers: Record<string, string>): Record<string, string> {
    const sensitiveHeaders = ['authorization', 'cookie', 'x-api-key', 'x-auth-token'];
    const sanitized = { ...headers };
    
    for (const header of sensitiveHeaders) {
      if (sanitized[header]) {
        sanitized[header] = '[REDACTED]';
      }
    }
    
    return sanitized;
  }
}

/**
 * Decorator for automatic error handling in class methods
 */
export function withErrorHandling(functionName?: string) {
  return function (target: any, propertyKey: string, descriptor: PropertyDescriptor) {
    const originalMethod = descriptor.value;
    const methodName = functionName || `${target.constructor.name}.${propertyKey}`;

    descriptor.value = async function (...args: any[]) {
      try {
        return await originalMethod.apply(this, args);
      } catch (error) {
        if (error instanceof AppError) {
          // Add method context to existing AppError
          if (!error.context?.location?.function) {
            error.context = {
              ...error.context,
              location: {
                ...error.context?.location,
                function: methodName,
              },
            };
          }
          throw error;
        } else {
          // Convert to AppError with context
          throw ErrorUtils.create(
            error instanceof Error ? error.message : 'Unknown error',
            methodName,
            { originalError: error }
          );
        }
      }
    };

    return descriptor;
  };
}

/**
 * Utility for creating database-related errors
 */
export class DatabaseErrorUtils {
  static connectionError(functionName: string, details?: any): AppError {
    return ErrorUtils.create(
      'Database connection failed',
      functionName,
      { details },
      503
    ).withSuggestions([
      'Check database connection string',
      'Verify database server is running',
      'Check network connectivity',
      'Verify authentication credentials',
    ]);
  }

  static queryError(functionName: string, query: string, error: any): AppError {
    return ErrorUtils.create(
      'Database query failed',
      functionName,
      { query, error: error.message },
      500
    ).withSuggestions([
      'Check query syntax and parameters',
      'Verify table/collection exists',
      'Check data types and constraints',
      'Review database schema',
    ]);
  }

  static notFound(functionName: string, resource: string, id: string): NotFoundError {
    return ErrorUtils.notFound(
      `${resource} not found`,
      functionName,
      { resource, id }
    );
  }
}

/**
 * Utility for creating API-related errors
 */
export class APIErrorUtils {
  static invalidRequest(functionName: string, details?: any): ValidationError {
    return ErrorUtils.validation(
      'Invalid request format',
      functionName,
      details
    );
  }

  static missingParameter(functionName: string, parameter: string): ValidationError {
    return ErrorUtils.validation(
      `Missing required parameter: ${parameter}`,
      functionName,
      { missingParameter: parameter }
    );
  }

  static invalidParameter(functionName: string, parameter: string, value: any, expected: string): ValidationError {
    return ErrorUtils.validation(
      `Invalid parameter '${parameter}': expected ${expected}, got ${typeof value}`,
      functionName,
      { parameter, value, expected }
    );
  }
}
