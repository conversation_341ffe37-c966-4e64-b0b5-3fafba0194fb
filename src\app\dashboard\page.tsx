'use client';

import React from 'react';
import { useRouter } from 'next/navigation';
import { useSession } from 'next-auth/react';
import { FileCard } from '@/components/ui/FileCard';
import { SkeletonFileCard } from '@/components/ui/Skeleton';
import { UploadModal } from '@/components/modals/UploadModal';
import { ShareModal } from '@/components/modals/ShareModal';
import { CreateFolderModal } from '@/components/modals/CreateFolderModal';
import { FilePreviewModal } from '@/components/modals/FilePreviewModal';
import { toast } from '@/components/ui/Toast';
import {
  CloudArrowUpIcon,
  FolderPlusIcon,
  ChartBarIcon,
  DocumentIcon,
} from '@heroicons/react/24/outline';
import { useSearch } from '@/contexts/SearchContext';

interface DashboardStats {
  totalFiles: number;
  totalSize: number;
  totalFolders: number;
  recentUploads: number;
}

interface RecentFile {
  id: string;
  name: string;
  size: number;
  mimeType: string;
  createdAt: string;
  downloadCount: number;
}

interface RecentFolder {
  id: string;
  name: string;
  path: string;
  createdAt: string;
}

export default function DashboardPage() {
  const router = useRouter();
  const { data: session } = useSession();
  const { searchQuery } = useSearch();
  const [stats, setStats] = React.useState<DashboardStats | null>(null);
  const [recentFiles, setRecentFiles] = React.useState<RecentFile[]>([]);
  const [recentFolders, setRecentFolders] = React.useState<RecentFolder[]>([]);
  const [loading, setLoading] = React.useState(true);
  const [uploadModalOpen, setUploadModalOpen] = React.useState(false);
  const [shareModalOpen, setShareModalOpen] = React.useState(false);
  const [createFolderModalOpen, setCreateFolderModalOpen] = React.useState(false);
  const [previewModalOpen, setPreviewModalOpen] = React.useState(false);
  const [selectedFile, setSelectedFile] = React.useState<{ id: string; name: string } | null>(null);
  const [selectedFileForPreview, setSelectedFileForPreview] = React.useState<RecentFile | null>(null);

  // Filter files and folders based on search query
  const filteredFiles = React.useMemo(() => {
    if (!searchQuery.trim()) return recentFiles;
    return recentFiles.filter(file =>
      file.name.toLowerCase().includes(searchQuery.toLowerCase())
    );
  }, [recentFiles, searchQuery]);

  const filteredFolders = React.useMemo(() => {
    if (!searchQuery.trim()) return recentFolders;
    return recentFolders.filter(folder =>
      folder.name.toLowerCase().includes(searchQuery.toLowerCase())
    );
  }, [recentFolders, searchQuery]);

  React.useEffect(() => {
    fetchDashboardData();
  }, []);

  const fetchDashboardData = async () => {
    try {
      // Fetch recent files
      const filesResponse = await fetch('/api/files?limit=6&sortBy=createdAt&sortOrder=desc');
      let filesData = null;
      if (filesResponse.ok) {
        filesData = await filesResponse.json();
        setRecentFiles(filesData.files);
      }

      // Fetch recent folders
      const foldersResponse = await fetch('/api/folders?limit=6&sortBy=createdAt&sortOrder=desc');
      let foldersData = null;
      if (foldersResponse.ok) {
        foldersData = await foldersResponse.json();
        setRecentFolders(foldersData.folders || []);
      }

      // Calculate stats from files and folders data
      if (filesData) {
        setStats({
          totalFiles: filesData.pagination.totalCount,
          totalSize: filesData.files.reduce((sum: number, file: any) => sum + file.size, 0),
          totalFolders: foldersData?.pagination?.totalCount || 0,
          recentUploads: filesData.files.filter((file: any) => {
            const uploadDate = new Date(file.createdAt);
            const weekAgo = new Date();
            weekAgo.setDate(weekAgo.getDate() - 7);
            return uploadDate > weekAgo;
          }).length,
        });
      }
    } catch (error) {
      console.error('Failed to fetch dashboard data:', error);
      toast.error('Failed to load dashboard data');
    } finally {
      setLoading(false);
    }
  };

  const handleFileDownload = async (fileId: string) => {
    try {
      // Use proxy download to avoid S3 redirect issues
      const downloadUrl = `/api/files/${fileId}/download?proxy=true`;

      // Create a temporary link to trigger download
      const link = document.createElement('a');
      link.href = downloadUrl;
      link.style.display = 'none';
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);

      toast.success('Download started');
    } catch (error) {
      toast.error('Failed to download file');
    }
  };

  const handleFileShare = (fileId: string, fileName: string) => {
    setSelectedFile({ id: fileId, name: fileName });
    setShareModalOpen(true);
  };

  const handleFilePreview = (file: RecentFile) => {
    setSelectedFileForPreview(file);
    setPreviewModalOpen(true);
  };

  const handleFileDelete = async (fileId: string) => {
    if (!confirm('Are you sure you want to delete this file?')) {
      return;
    }

    try {
      const response = await fetch(`/api/files/${fileId}`, {
        method: 'DELETE',
      });

      if (!response.ok) {
        throw new Error('Failed to delete file');
      }

      toast.success('File deleted successfully');
      fetchDashboardData();
    } catch (error) {
      toast.error('Failed to delete file');
    }
  };

  const handleFolderDelete = async (folderId: string) => {
    if (!confirm('Are you sure you want to delete this folder? This will also delete all files and subfolders inside it.')) {
      return;
    }

    try {
      const response = await fetch(`/api/folder/${folderId}`, {
        method: 'DELETE',
      });

      if (!response.ok) {
        throw new Error('Failed to delete folder');
      }

      toast.success('Folder deleted successfully');
      fetchDashboardData();
    } catch (error) {
      toast.error('Failed to delete folder');
    }
  };

  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  return (
    <div className="p-6 space-y-6">
      {/* Welcome Section */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-text-primary">
            Welcome back, {session?.user?.name}!
          </h1>
          <p className="text-text-secondary">
            Here's what's happening with your files today.
          </p>
        </div>
        <div className="flex space-x-3">
          <button
            onClick={() => setUploadModalOpen(true)}
            className="flex items-center px-4 py-2 bg-accent text-white rounded-lg hover:bg-accent/90"
          >
            <CloudArrowUpIcon className="h-4 w-4 mr-2" />
            Upload Files
          </button>
          <button
            onClick={() => setCreateFolderModalOpen(true)}
            className="flex items-center px-4 py-2 bg-secondary text-text-primary border border-divider rounded-lg hover:bg-secondary/80"
          >
            <FolderPlusIcon className="h-4 w-4 mr-2" />
            New Folder
          </button>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <div className="bg-background border border-divider rounded-lg p-6">
          <div className="flex items-center">
            <div className="p-2 bg-accent/10 rounded-lg">
              <DocumentIcon className="h-6 w-6 text-accent" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-text-secondary">Total Files</p>
              <p className="text-2xl font-bold text-text-primary">
                {loading ? '...' : stats?.totalFiles || 0}
              </p>
            </div>
          </div>
        </div>

        <div className="bg-background border border-divider rounded-lg p-6">
          <div className="flex items-center">
            <div className="p-2 bg-success/10 rounded-lg">
              <ChartBarIcon className="h-6 w-6 text-success" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-text-secondary">Storage Used</p>
              <p className="text-2xl font-bold text-text-primary">
                {loading ? '...' : formatFileSize(stats?.totalSize || 0)}
              </p>
            </div>
          </div>
        </div>

        <div className="bg-background border border-divider rounded-lg p-6">
          <div className="flex items-center">
            <div className="p-2 bg-warning/10 rounded-lg">
              <FolderPlusIcon className="h-6 w-6 text-warning" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-text-secondary">Folders</p>
              <p className="text-2xl font-bold text-text-primary">
                {loading ? '...' : stats?.totalFolders || 0}
              </p>
            </div>
          </div>
        </div>

        <div className="bg-background border border-divider rounded-lg p-6">
          <div className="flex items-center">
            <div className="p-2 bg-accent/10 rounded-lg">
              <CloudArrowUpIcon className="h-6 w-6 text-accent" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-text-secondary">Recent Uploads</p>
              <p className="text-2xl font-bold text-text-primary">
                {loading ? '...' : stats?.recentUploads || 0}
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Recent Activity Overview */}
      <div className="space-y-6">
        {/* Recent Files Section */}
        <div>
          <div className="flex items-center justify-between mb-4">
            <h2 className="text-lg font-semibold text-text-primary">
              {searchQuery.trim() ? `Search Results for "${searchQuery}"` : 'Recent Files'}
            </h2>
            {!searchQuery.trim() && (
              <button
                onClick={() => router.push('/dashboard/files')}
                className="px-3 py-1 text-sm text-accent hover:bg-accent/10 rounded-lg transition-colors font-medium"
              >
                View All Files →
              </button>
            )}
          </div>

          {loading ? (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
              {Array.from({ length: 8 }).map((_, index) => (
                <SkeletonFileCard key={index} />
              ))}
            </div>
          ) : searchQuery.trim() ? (
            // Search Results
            (filteredFiles.length > 0 || filteredFolders.length > 0) ? (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                {/* Display folders first */}
                {filteredFolders.map((folder) => (
                  <FileCard
                    key={`folder-${folder.id}`}
                    id={folder.id}
                    name={folder.name}
                    type="folder"
                    size={0}
                    mimeType="folder"
                    createdAt={new Date(folder.createdAt)}
                    downloadCount={0}
                    onClick={() => router.push(`/dashboard/files?folder=${folder.id}`)}
                    onDownload={() => {}} // Folders don't have download
                    onShare={() => {}} // Folders don't have share for now
                    onDelete={() => handleFolderDelete(folder.id)}
                  />
                ))}
                {/* Display files */}
                {filteredFiles.map((file) => (
                  <FileCard
                    key={`file-${file.id}`}
                    id={file.id}
                    name={file.name}
                    type="file"
                    size={file.size}
                    mimeType={file.mimeType}
                    createdAt={new Date(file.createdAt)}
                    downloadCount={file.downloadCount}
                    onPreview={() => handleFilePreview(file)}
                    onDownload={() => handleFileDownload(file.id)}
                    onShare={() => handleFileShare(file.id, file.name)}
                    onDelete={() => handleFileDelete(file.id)}
                  />
                ))}
              </div>
            ) : (
              <div className="text-center py-12">
                <DocumentIcon className="h-12 w-12 text-text-secondary mx-auto mb-4" />
                <h3 className="text-lg font-medium text-text-primary mb-2">No results found</h3>
                <p className="text-text-secondary">
                  No files or folders match "{searchQuery}". Try a different search term.
                </p>
              </div>
            )
          ) : (
            // Recent Files Overview
            <div className="space-y-8">
              {/* Recent Files */}
              {filteredFiles.length > 0 && (
                <div>
                  <h3 className="text-md font-medium text-text-primary mb-3 flex items-center">
                    📄 Recent Files
                    <span className="ml-2 text-sm text-text-secondary">({filteredFiles.length})</span>
                  </h3>
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                    {filteredFiles.slice(0, 8).map((file) => (
                      <FileCard
                        key={`file-${file.id}`}
                        id={file.id}
                        name={file.name}
                        type="file"
                        size={file.size}
                        mimeType={file.mimeType}
                        createdAt={new Date(file.createdAt)}
                        downloadCount={file.downloadCount}
                        onPreview={() => handleFilePreview(file)}
                        onDownload={() => handleFileDownload(file.id)}
                        onShare={() => handleFileShare(file.id, file.name)}
                        onDelete={() => handleFileDelete(file.id)}
                      />
                    ))}
                  </div>
                  {filteredFiles.length > 8 && (
                    <div className="text-center mt-4">
                      <button
                        onClick={() => router.push('/dashboard/files')}
                        className="text-sm text-accent hover:text-accent/80 font-medium"
                      >
                        View {filteredFiles.length - 8} more files →
                      </button>
                    </div>
                  )}
                </div>
              )}

              {/* Recent Folders */}
              {filteredFolders.length > 0 && (
                <div>
                  <h3 className="text-md font-medium text-text-primary mb-3 flex items-center">
                    📁 Recent Folders
                    <span className="ml-2 text-sm text-text-secondary">({filteredFolders.length})</span>
                  </h3>
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                    {filteredFolders.slice(0, 4).map((folder) => (
                      <FileCard
                        key={`folder-${folder.id}`}
                        id={folder.id}
                        name={folder.name}
                        type="folder"
                        size={0}
                        mimeType="folder"
                        createdAt={new Date(folder.createdAt)}
                        downloadCount={0}
                        onClick={() => router.push(`/dashboard/files?folder=${folder.id}`)}
                        onDownload={() => {}} // Folders don't have download
                        onShare={() => {}} // Folders don't have share for now
                        onDelete={() => handleFolderDelete(folder.id)}
                      />
                    ))}
                  </div>
                  {filteredFolders.length > 4 && (
                    <div className="text-center mt-4">
                      <button
                        onClick={() => router.push('/dashboard/files')}
                        className="text-sm text-accent hover:text-accent/80 font-medium"
                      >
                        View {filteredFolders.length - 4} more folders →
                      </button>
                    </div>
                  )}
                </div>
              )}

              {/* Empty State */}
              {filteredFiles.length === 0 && filteredFolders.length === 0 && (
                <div className="text-center py-12">
                  <DocumentIcon className="h-12 w-12 text-text-secondary mx-auto mb-4" />
                  <h3 className="text-lg font-medium text-text-primary mb-2">No files yet</h3>
                  <p className="text-text-secondary mb-6">
                    Upload your first file or create a folder to get started.
                  </p>
                  <div className="flex justify-center space-x-3">
                    <button
                      onClick={() => setUploadModalOpen(true)}
                      className="flex items-center px-4 py-2 bg-accent text-white rounded-lg hover:bg-accent/90"
                    >
                      <CloudArrowUpIcon className="h-4 w-4 mr-2" />
                      Upload Files
                    </button>
                    <button
                      onClick={() => setCreateFolderModalOpen(true)}
                      className="flex items-center px-4 py-2 bg-secondary text-text-primary border border-divider rounded-lg hover:bg-secondary/80"
                    >
                      <FolderPlusIcon className="h-4 w-4 mr-2" />
                      New Folder
                    </button>
                  </div>
                </div>
              )}
            </div>
          )}
        </div>
      </div>

      {/* Modals */}
      <UploadModal
        isOpen={uploadModalOpen}
        onClose={() => setUploadModalOpen(false)}
        onUploadComplete={() => {
          fetchDashboardData();
          setUploadModalOpen(false);
        }}
      />

      {selectedFile && (
        <ShareModal
          isOpen={shareModalOpen}
          onClose={() => {
            setShareModalOpen(false);
            setSelectedFile(null);
          }}
          fileId={selectedFile.id}
          fileName={selectedFile.name}
        />
      )}

      <CreateFolderModal
        isOpen={createFolderModalOpen}
        onClose={() => setCreateFolderModalOpen(false)}
        onFolderCreated={() => {
          fetchDashboardData();
          setCreateFolderModalOpen(false);
        }}
      />

      {selectedFileForPreview && (
        <FilePreviewModal
          isOpen={previewModalOpen}
          onClose={() => {
            setPreviewModalOpen(false);
            setSelectedFileForPreview(null);
          }}
          file={{
            id: selectedFileForPreview.id,
            name: selectedFileForPreview.name,
            size: selectedFileForPreview.size,
            mimeType: selectedFileForPreview.mimeType,
            createdAt: selectedFileForPreview.createdAt,
          }}
          onDownload={() => handleFileDownload(selectedFileForPreview.id)}
          onShare={() => handleFileShare(selectedFileForPreview.id, selectedFileForPreview.name)}
        />
      )}
    </div>
  );
}
