'use client';

import React from 'react';
import { useSession } from 'next-auth/react';
import { toast } from '@/components/ui/Toast';
import {
  ChartBarIcon,
  DocumentIcon,
  EyeIcon,
  ArrowDownTrayIcon,
  ShareIcon,
  CalendarIcon,
} from '@heroicons/react/24/outline';
import { formatFileSize, formatDate } from '@/lib/utils';

interface AnalyticsData {
  overview: {
    totalFiles: number;
    totalSize: number;
    totalViews: number;
    totalDownloads: number;
    totalShares: number;
  };
  recentActivity: {
    date: string;
    action: string;
    fileName: string;
    details: string;
  }[];
  topFiles: {
    id: string;
    name: string;
    downloads: number;
    views: number;
    size: number;
  }[];
  storageUsage: {
    used: number;
    total: number;
    percentage: number;
  };
}

export default function AnalyticsPage() {
  const { data: session } = useSession();
  const [analytics, setAnalytics] = React.useState<AnalyticsData | null>(null);
  const [loading, setLoading] = React.useState(true);
  const [timeRange, setTimeRange] = React.useState('30d');

  React.useEffect(() => {
    fetchAnalytics();
  }, [timeRange]);

  const fetchAnalytics = async () => {
    try {
      setLoading(true);
      const response = await fetch(`/api/analytics?range=${timeRange}`);
      if (!response.ok) {
        throw new Error('Failed to fetch analytics');
      }
      const data = await response.json();
      setAnalytics(data);
    } catch (error) {
      console.error('Failed to fetch analytics:', error);
      toast.error('Failed to load analytics data');
      // Set mock data for now
      setAnalytics({
        overview: {
          totalFiles: 0,
          totalSize: 0,
          totalViews: 0,
          totalDownloads: 0,
          totalShares: 0,
        },
        recentActivity: [],
        topFiles: [],
        storageUsage: {
          used: 0,
          total: 5 * 1024 * 1024 * 1024, // 5GB default
          percentage: 0,
        },
      });
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <div className="p-6">
        <div className="animate-pulse space-y-6">
          <div className="h-8 bg-secondary rounded w-1/3"></div>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-6">
            {Array.from({ length: 5 }).map((_, i) => (
              <div key={i} className="h-24 bg-secondary rounded"></div>
            ))}
          </div>
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <div className="h-64 bg-secondary rounded"></div>
            <div className="h-64 bg-secondary rounded"></div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="p-6 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-text-primary">Usage & Analytics</h1>
          <p className="text-text-secondary">
            Track your file usage and storage statistics
          </p>
        </div>
        <div className="flex items-center space-x-2">
          <select
            value={timeRange}
            onChange={(e) => setTimeRange(e.target.value)}
            className="px-3 py-2 border border-divider rounded-lg bg-background text-text-primary focus:outline-none focus:ring-2 focus:ring-accent"
          >
            <option value="7d">Last 7 days</option>
            <option value="30d">Last 30 days</option>
            <option value="90d">Last 90 days</option>
            <option value="1y">Last year</option>
          </select>
        </div>
      </div>

      {/* Overview Stats */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-6">
        <div className="bg-background border border-divider rounded-lg p-6">
          <div className="flex items-center">
            <div className="p-2 bg-accent/10 rounded-lg">
              <DocumentIcon className="h-6 w-6 text-accent" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-text-secondary">Total Files</p>
              <p className="text-2xl font-bold text-text-primary">
                {analytics?.overview.totalFiles || 0}
              </p>
            </div>
          </div>
        </div>

        <div className="bg-background border border-divider rounded-lg p-6">
          <div className="flex items-center">
            <div className="p-2 bg-blue-500/10 rounded-lg">
              <ChartBarIcon className="h-6 w-6 text-blue-500" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-text-secondary">Storage Used</p>
              <p className="text-2xl font-bold text-text-primary">
                {formatFileSize(analytics?.overview.totalSize || 0)}
              </p>
            </div>
          </div>
        </div>

        <div className="bg-background border border-divider rounded-lg p-6">
          <div className="flex items-center">
            <div className="p-2 bg-green-500/10 rounded-lg">
              <EyeIcon className="h-6 w-6 text-green-500" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-text-secondary">Total Views</p>
              <p className="text-2xl font-bold text-text-primary">
                {analytics?.overview.totalViews || 0}
              </p>
            </div>
          </div>
        </div>

        <div className="bg-background border border-divider rounded-lg p-6">
          <div className="flex items-center">
            <div className="p-2 bg-purple-500/10 rounded-lg">
              <ArrowDownTrayIcon className="h-6 w-6 text-purple-500" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-text-secondary">Downloads</p>
              <p className="text-2xl font-bold text-text-primary">
                {analytics?.overview.totalDownloads || 0}
              </p>
            </div>
          </div>
        </div>

        <div className="bg-background border border-divider rounded-lg p-6">
          <div className="flex items-center">
            <div className="p-2 bg-orange-500/10 rounded-lg">
              <ShareIcon className="h-6 w-6 text-orange-500" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-text-secondary">Shares</p>
              <p className="text-2xl font-bold text-text-primary">
                {analytics?.overview.totalShares || 0}
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Storage Usage */}
      <div className="bg-background border border-divider rounded-lg p-6">
        <h2 className="text-lg font-semibold text-text-primary mb-4">Storage Usage</h2>
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <span className="text-text-secondary">
              {formatFileSize(analytics?.storageUsage.used || 0)} of {formatFileSize(analytics?.storageUsage.total || 0)} used
            </span>
            <span className="text-text-secondary">
              {analytics?.storageUsage.percentage.toFixed(1) || 0}%
            </span>
          </div>
          <div className="w-full bg-secondary rounded-full h-2">
            <div
              className="bg-accent h-2 rounded-full transition-all duration-300"
              style={{ width: `${analytics?.storageUsage.percentage || 0}%` }}
            ></div>
          </div>
        </div>
      </div>

      {/* Charts and Tables Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Top Files */}
        <div className="bg-background border border-divider rounded-lg p-6">
          <h2 className="text-lg font-semibold text-text-primary mb-4">Most Popular Files</h2>
          <div className="space-y-3">
            {analytics?.topFiles.length ? (
              analytics.topFiles.map((file, index) => (
                <div key={file.id} className="flex items-center justify-between p-3 bg-secondary/50 rounded-lg">
                  <div className="flex items-center space-x-3">
                    <span className="text-sm font-medium text-text-secondary w-6">
                      #{index + 1}
                    </span>
                    <div>
                      <p className="text-sm font-medium text-text-primary truncate max-w-48">
                        {file.name}
                      </p>
                      <p className="text-xs text-text-secondary">
                        {formatFileSize(file.size)}
                      </p>
                    </div>
                  </div>
                  <div className="text-right">
                    <p className="text-sm font-medium text-text-primary">
                      {file.downloads} downloads
                    </p>
                    <p className="text-xs text-text-secondary">
                      {file.views} views
                    </p>
                  </div>
                </div>
              ))
            ) : (
              <div className="text-center py-8 text-text-secondary">
                <DocumentIcon className="h-12 w-12 mx-auto mb-2 opacity-50" />
                <p>No file activity yet</p>
              </div>
            )}
          </div>
        </div>

        {/* Recent Activity */}
        <div className="bg-background border border-divider rounded-lg p-6">
          <h2 className="text-lg font-semibold text-text-primary mb-4">Recent Activity</h2>
          <div className="space-y-3">
            {analytics?.recentActivity.length ? (
              analytics.recentActivity.map((activity, index) => (
                <div key={index} className="flex items-start space-x-3 p-3 bg-secondary/50 rounded-lg">
                  <CalendarIcon className="h-4 w-4 text-text-secondary mt-0.5 flex-shrink-0" />
                  <div className="flex-1 min-w-0">
                    <p className="text-sm font-medium text-text-primary">
                      {activity.action}
                    </p>
                    <p className="text-sm text-text-secondary truncate">
                      {activity.fileName}
                    </p>
                    <p className="text-xs text-text-secondary">
                      {formatDate(activity.date)}
                    </p>
                  </div>
                </div>
              ))
            ) : (
              <div className="text-center py-8 text-text-secondary">
                <CalendarIcon className="h-12 w-12 mx-auto mb-2 opacity-50" />
                <p>No recent activity</p>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}
