import React from 'react';
import { ChevronRightIcon, HomeIcon } from '@heroicons/react/24/outline';
import { cn } from '@/lib/utils';

interface BreadcrumbItem {
  id: string;
  name: string;
  path: string;
}

interface BreadcrumbProps {
  items: BreadcrumbItem[];
  onItemClick: (item: BreadcrumbItem) => void;
  className?: string;
}

export const Breadcrumb: React.FC<BreadcrumbProps> = ({
  items,
  onItemClick,
  className,
}) => {
  const allItems = [
    { id: 'root', name: 'Home', path: '/' },
    ...items,
  ];

  return (
    <nav className={cn('flex items-center space-x-1 text-sm', className)}>
      {allItems.map((item, index) => (
        <React.Fragment key={item.id}>
          {index > 0 && (
            <ChevronRightIcon className="h-4 w-4 text-text-secondary flex-shrink-0" />
          )}
          <button
            onClick={() => onItemClick(item)}
            className={cn(
              'flex items-center px-2 py-1 rounded hover:bg-secondary transition-colors',
              index === allItems.length - 1
                ? 'text-text-primary font-medium cursor-default'
                : 'text-text-secondary hover:text-text-primary'
            )}
            disabled={index === allItems.length - 1}
          >
            {index === 0 && (
              <HomeIcon className="h-4 w-4 mr-1" />
            )}
            <span className="truncate max-w-[150px]">
              {item.name}
            </span>
          </button>
        </React.Fragment>
      ))}
    </nav>
  );
};
