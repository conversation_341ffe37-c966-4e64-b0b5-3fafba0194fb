/**
 * Development Authentication Test Endpoint
 * 
 * This endpoint is only available in development mode and provides
 * utilities for testing authentication flows and debugging auth issues.
 * 
 * SECURITY: Automatically disabled in production
 */

import { NextRequest, NextResponse } from 'next/server';
import { isDevelopmentMode, DevLogger } from '@/lib/dev-config';
import { getSessionWithDevSupport, validateAdminAccessWithDevSupport } from '@/lib/auth-dev';

export async function GET(request: NextRequest) {
  // Ensure this endpoint is only available in development
  if (!isDevelopmentMode()) {
    return NextResponse.json(
      { error: 'This endpoint is only available in development mode' },
      { status: 404 }
    );
  }

  try {
    const { searchParams } = new URL(request.url);
    const testType = searchParams.get('test') || 'session';

    DevLogger.debug(`Running auth test: ${testType}`);

    switch (testType) {
      case 'session':
        return await testSession(request);
      
      case 'admin':
        return await testAdminAccess(request);
      
      case 'routes':
        return await testRouteAccess(request);
      
      case 'config':
        return await testConfiguration(request);
      
      default:
        return NextResponse.json({
          error: 'Invalid test type',
          availableTests: ['session', 'admin', 'routes', 'config']
        }, { status: 400 });
    }

  } catch (error) {
    DevLogger.error('Auth test error', error);
    return NextResponse.json(
      { 
        error: 'Test failed',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}

async function testSession(request: NextRequest) {
  const session = await getSessionWithDevSupport(request);
  
  return NextResponse.json({
    test: 'session',
    result: {
      hasSession: !!session,
      user: session?.user || null,
      expires: session?.expires || null,
    },
    timestamp: new Date().toISOString(),
  });
}

async function testAdminAccess(request: NextRequest) {
  const { isValid, isAdmin, session } = await validateAdminAccessWithDevSupport(request);
  
  return NextResponse.json({
    test: 'admin',
    result: {
      isValid,
      isAdmin,
      user: session?.user || null,
    },
    timestamp: new Date().toISOString(),
  });
}

async function testRouteAccess(request: NextRequest) {
  const session = await getSessionWithDevSupport(request);
  
  // Test various route types
  const routes = [
    { path: '/', type: 'public' },
    { path: '/login', type: 'public' },
    { path: '/signup', type: 'public' },
    { path: '/dashboard', type: 'protected' },
    { path: '/settings', type: 'protected' },
    { path: '/admin-dashboard', type: 'admin' },
    { path: '/api/files', type: 'api-protected' },
    { path: '/api/admin/users', type: 'api-admin' },
  ];

  const results = routes.map(route => ({
    ...route,
    accessible: session ? true : false, // Simplified for testing
    reason: !session ? 'No session' : 'Has session'
  }));

  return NextResponse.json({
    test: 'routes',
    result: {
      hasSession: !!session,
      routes: results,
    },
    timestamp: new Date().toISOString(),
  });
}

async function testConfiguration(request: NextRequest) {
  const { isAuthBypassEnabled, getDevConfig } = await import('@/lib/dev-config');
  const config = getDevConfig();
  
  return NextResponse.json({
    test: 'config',
    result: {
      environment: process.env.NODE_ENV,
      developmentMode: config.isDevelopment,
      authBypass: config.bypassAuth,
      debugLogging: config.enableDebugLogging,
      performanceLogging: config.enablePerformanceLogging,
      detailedErrors: config.enableDetailedErrors,
      defaultUser: config.defaultUser,
    },
    timestamp: new Date().toISOString(),
  });
}

export async function POST(request: NextRequest) {
  // Ensure this endpoint is only available in development
  if (!isDevelopmentMode()) {
    return NextResponse.json(
      { error: 'This endpoint is only available in development mode' },
      { status: 404 }
    );
  }

  try {
    const body = await request.json();
    const { action } = body;

    DevLogger.debug(`Running auth action: ${action}`);

    switch (action) {
      case 'simulate-login':
        return await simulateLogin(request, body);
      
      case 'simulate-logout':
        return await simulateLogout(request, body);
      
      case 'test-permissions':
        return await testPermissions(request, body);
      
      default:
        return NextResponse.json({
          error: 'Invalid action',
          availableActions: ['simulate-login', 'simulate-logout', 'test-permissions']
        }, { status: 400 });
    }

  } catch (error) {
    DevLogger.error('Auth action error', error);
    return NextResponse.json(
      { 
        error: 'Action failed',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}

async function simulateLogin(request: NextRequest, body: any) {
  const { userOverrides } = body;
  const { createDevSession } = await import('@/lib/auth-dev');
  
  const session = createDevSession(userOverrides);
  
  return NextResponse.json({
    action: 'simulate-login',
    result: {
      success: true,
      session,
      message: 'Login simulation successful'
    },
    timestamp: new Date().toISOString(),
  });
}

async function simulateLogout(request: NextRequest, body: any) {
  return NextResponse.json({
    action: 'simulate-logout',
    result: {
      success: true,
      message: 'Logout simulation successful'
    },
    timestamp: new Date().toISOString(),
  });
}

async function testPermissions(request: NextRequest, body: any) {
  const { permissions } = body;
  const session = await getSessionWithDevSupport(request);
  
  if (!session) {
    return NextResponse.json({
      action: 'test-permissions',
      result: {
        hasSession: false,
        permissions: {},
        message: 'No session available'
      },
      timestamp: new Date().toISOString(),
    });
  }

  const { isAdminUserWithDevSupport } = await import('@/lib/auth-dev');
  
  const permissionResults = {
    canAccessDashboard: true, // Has session
    canAccessSettings: true, // Has session
    canAccessAdmin: isAdminUserWithDevSupport(session.user.email),
    canUploadFiles: session.user.verified,
    canUsePlatformS3: session.user.allowPlatformS3,
  };

  return NextResponse.json({
    action: 'test-permissions',
    result: {
      hasSession: true,
      user: session.user,
      permissions: permissionResults,
      message: 'Permission test completed'
    },
    timestamp: new Date().toISOString(),
  });
}
