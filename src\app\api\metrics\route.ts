import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@/lib/auth';
import { PerformanceMonitor, DatabaseMonitor, getMemoryUsage } from '@/lib/performance';

// Check if user is admin
async function isAdmin(session: any): Promise<boolean> {
  const adminEmails = process.env.ADMIN_EMAILS?.split(',') || [];
  return adminEmails.includes(session.user.email);
}

export async function GET(request: NextRequest) {
  try {
    const session = await auth();
    if (!session) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    if (!(await isAdmin(session))) {
      return NextResponse.json(
        { error: 'Access denied' },
        { status: 403 }
      );
    }

    const { searchParams } = new URL(request.url);
    const type = searchParams.get('type') || 'all';

    const metrics: any = {
      timestamp: new Date().toISOString(),
      uptime: process.uptime(),
    };

    if (type === 'all' || type === 'performance') {
      metrics.performance = {
        endpoints: PerformanceMonitor.getAllMetrics(),
        memory: getMemoryUsage(),
      };
    }

    if (type === 'all' || type === 'database') {
      metrics.database = {
        queries: DatabaseMonitor.getAllQueryMetrics(),
      };
    }

    if (type === 'all' || type === 'system') {
      metrics.system = {
        nodeVersion: process.version,
        platform: process.platform,
        arch: process.arch,
        cpuUsage: process.cpuUsage(),
        uptime: process.uptime(),
        pid: process.pid,
      };
    }

    return NextResponse.json(metrics, {
      headers: {
        'Cache-Control': 'no-cache, no-store, must-revalidate',
        'Pragma': 'no-cache',
        'Expires': '0',
      },
    });

  } catch (error) {
    console.error('Metrics fetch error:', error);
    return NextResponse.json(
      { error: 'Failed to fetch metrics' },
      { status: 500 }
    );
  }
}

export async function DELETE(request: NextRequest) {
  try {
    const session = await auth();
    if (!session) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    if (!(await isAdmin(session))) {
      return NextResponse.json(
        { error: 'Access denied' },
        { status: 403 }
      );
    }

    const { searchParams } = new URL(request.url);
    const endpoint = searchParams.get('endpoint');

    if (endpoint) {
      PerformanceMonitor.clearMetrics(endpoint);
    } else {
      PerformanceMonitor.clearMetrics();
    }

    return NextResponse.json({
      message: endpoint 
        ? `Metrics cleared for endpoint: ${endpoint}`
        : 'All metrics cleared',
    });

  } catch (error) {
    console.error('Metrics clear error:', error);
    return NextResponse.json(
      { error: 'Failed to clear metrics' },
      { status: 500 }
    );
  }
}
