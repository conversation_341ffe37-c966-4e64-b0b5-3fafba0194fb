import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@/lib/auth';
import { backgroundSyncService } from '@/lib/background-sync';
import { User } from '@/models/User';
import connectDB from '@/lib/mongodb';

export async function GET(request: NextRequest) {
  try {
    const session = await auth();
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    await connectDB();

    // Get user's sync information
    const user = await User.findById(session.user.id).select('syncEnabled lastSyncAt');
    
    if (!user) {
      return NextResponse.json(
        { error: 'User not found' },
        { status: 404 }
      );
    }

    // Get global sync stats (for admin users)
    let globalStats = null;
    const adminEmails = process.env.ADMIN_EMAILS?.split(',').map(email => email.trim()) || [];
    const isAdmin = adminEmails.includes(session.user.email || '');

    if (isAdmin) {
      globalStats = await backgroundSyncService.getSyncStats();
    }

    return NextResponse.json({
      user: {
        syncEnabled: user.syncEnabled || false,
        lastSyncAt: user.lastSyncAt,
      },
      global: globalStats
    });

  } catch (error) {
    console.error('Sync stats error:', error);
    
    return NextResponse.json(
      { error: 'Failed to fetch sync statistics' },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const session = await auth();
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const body = await request.json();
    const { action } = body;

    await connectDB();

    switch (action) {
      case 'enable_sync':
        await User.findByIdAndUpdate(session.user.id, {
          syncEnabled: true
        });
        
        return NextResponse.json({
          message: 'Sync enabled successfully'
        });

      case 'disable_sync':
        await User.findByIdAndUpdate(session.user.id, {
          syncEnabled: false
        });
        
        return NextResponse.json({
          message: 'Sync disabled successfully'
        });

      case 'trigger_background_sync':
        // Only allow admins to trigger background sync
        const adminEmails = process.env.ADMIN_EMAILS?.split(',').map(email => email.trim()) || [];
        const isAdmin = adminEmails.includes(session.user.email || '');
        
        if (!isAdmin) {
          return NextResponse.json(
            { error: 'Admin access required' },
            { status: 403 }
          );
        }

        if (!backgroundSyncService.isServiceRunning()) {
          return NextResponse.json(
            { error: 'Background sync service is not running' },
            { status: 400 }
          );
        }

        // Trigger background sync cycle
        await backgroundSyncService.triggerSyncCycle();
        
        return NextResponse.json({
          message: 'Background sync cycle triggered successfully'
        });

      default:
        return NextResponse.json(
          { error: 'Invalid action' },
          { status: 400 }
        );
    }

  } catch (error) {
    console.error('Sync action error:', error);
    
    return NextResponse.json(
      { 
        error: 'Failed to perform sync action',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}
