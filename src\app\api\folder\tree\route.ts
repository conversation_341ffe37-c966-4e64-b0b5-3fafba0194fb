import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@/lib/auth';
import { Folder } from '@/models/Folder';
import connectDB from '@/lib/mongodb';

interface FolderTreeNode {
  id: string;
  name: string;
  path: string;
  parentFolderId?: string;
  children: FolderTreeNode[];
  createdAt: Date;
  updatedAt: Date;
}

export async function GET(request: NextRequest) {
  try {
    const session = await auth();
    if (!session) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    await connectDB();

    // Get all folders for the user
    const folders = await Folder.find({ userId: session.user.id }).sort({ path: 1 });

    // Build folder tree
    const folderMap = new Map<string, FolderTreeNode>();
    const rootFolders: FolderTreeNode[] = [];

    // First pass: create all nodes
    folders.forEach(folder => {
      const node: FolderTreeNode = {
        id: folder._id.toString(),
        name: folder.name,
        path: folder.path,
        parentFolderId: folder.parentFolderId?.toString(),
        children: [],
        createdAt: folder.createdAt,
        updatedAt: folder.updatedAt,
      };
      folderMap.set(node.id, node);
    });

    // Second pass: build tree structure
    folders.forEach(folder => {
      const node = folderMap.get(folder._id.toString())!;
      
      if (folder.parentFolderId) {
        const parent = folderMap.get(folder.parentFolderId.toString());
        if (parent) {
          parent.children.push(node);
        }
      } else {
        rootFolders.push(node);
      }
    });

    // Sort children by name
    const sortChildren = (nodes: FolderTreeNode[]) => {
      nodes.sort((a, b) => a.name.localeCompare(b.name));
      nodes.forEach(node => sortChildren(node.children));
    };

    sortChildren(rootFolders);

    return NextResponse.json({
      folders: rootFolders,
      totalCount: folders.length,
    });

  } catch (error) {
    console.error('Folder tree fetch error:', error);
    return NextResponse.json(
      { error: 'Failed to fetch folder tree' },
      { status: 500 }
    );
  }
}
