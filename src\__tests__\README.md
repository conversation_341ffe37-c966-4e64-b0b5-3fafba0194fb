# Drivn Testing Infrastructure

This directory contains the comprehensive testing infrastructure for the Drivn application, featuring both traditional integration tests and standalone feature tests for isolated development and debugging.

## 📁 Directory Structure

```
src/__tests__/
├── README.md                          # This file
├── api/                              # API integration tests
│   ├── auth/                         # Authentication API tests
│   ├── admin/                        # Admin API tests
│   └── upload/                       # File upload API tests
├── features/                         # Standalone feature tests
│   ├── auth.standalone.test.ts       # Authentication feature tests
│   ├── files.standalone.test.ts      # File management feature tests
│   └── admin.standalone.test.ts      # Admin feature tests
├── lib/                              # Library/utility tests
├── utils/                            # Test utilities and helpers
│   ├── test-helpers.ts               # Original test helpers
│   └── dev-test-helpers.ts           # Enhanced development test helpers
└── middleware.test.ts                # Middleware tests
```

## 🧪 Test Types

### 1. Integration Tests (`api/`, `lib/`, `middleware.test.ts`)
Traditional integration tests that test the interaction between multiple components:
- Test real API endpoints with mocked dependencies
- Test middleware behavior with various scenarios
- Test library functions with database interactions

### 2. Standalone Feature Tests (`features/`)
Isolated tests for individual features that can run independently:
- **Authentication Feature** (`auth.standalone.test.ts`)
- **File Management Feature** (`files.standalone.test.ts`)
- **Admin Feature** (`admin.standalone.test.ts`)

## 🚀 Running Tests

### Basic Test Commands

```bash
# Run all tests
npm test

# Run tests in watch mode
npm run test:watch

# Run tests with coverage
npm run test:coverage

# Run tests for CI
npm run test:ci
```

### Feature-Specific Test Commands

```bash
# Run specific feature tests
npm run test:auth          # Authentication feature only
npm run test:files         # File management feature only
npm run test:admin         # Admin feature only
npm run test:all-features  # All standalone feature tests

# Run with options
npm run test:auth -- --watch      # Watch mode
npm run test:files -- --coverage  # With coverage
npm run test:admin -- --verbose   # Verbose output
npm run test:auth -- --dev        # Development mode
```

### Advanced Usage

```bash
# Run feature test runner directly
node scripts/test-features.js auth --watch --dev

# Run all features with coverage
node scripts/test-features.js all --coverage --verbose

# Get help
node scripts/test-features.js
```

## 🛠️ Development Mode Testing

The testing infrastructure includes special support for development mode:

### Development Mode Features
- **Authentication Bypass**: Tests can run with or without authentication
- **Enhanced Error Context**: Detailed error information for debugging
- **Debug Logging**: Comprehensive logging during test execution
- **Isolated Environments**: Each feature test runs in complete isolation

### Using Development Mode

```bash
# Enable development mode for tests
npm run test:auth -- --dev

# This sets:
# NODE_ENV=development
# DEVELOPMENT_MODE_BYPASS_AUTH=true
# DEVELOPMENT_DETAILED_ERRORS=true
# DEVELOPMENT_DEBUG_LOGGING=true
```

## 🔧 Test Utilities

### Enhanced Test Helpers (`dev-test-helpers.ts`)

#### DevTestEnvironment
```typescript
// Setup isolated test environment
DevTestEnvironment.setup({
  developmentMode: true,
  authBypass: true,
  detailedErrors: true,
  debugLogging: true,
});

// Create feature-specific isolation
const env = DevTestEnvironment.createIsolated('auth', {
  withAuth: true,
  withDatabase: true,
  developmentMode: true,
});
```

#### MockFactory
```typescript
// Create comprehensive mocks
const mockUser = MockFactory.createDatabaseMocks();
const mockS3 = MockFactory.createS3Mocks();
const mockAuth = MockFactory.createAuthMocks({
  defaultSession: mockTestSession,
  developmentMode: true,
});

// Create mock requests
const request = MockFactory.createRequest('http://localhost:3000/api/test', {
  method: 'POST',
  body: { data: 'test' },
  session: mockTestSession,
  developmentMode: true,
});
```

#### FeatureTestUtils
```typescript
// Test features in isolation
await FeatureTestUtils.testAuthFeature(async () => {
  // Your test code here
});

await FeatureTestUtils.testFileFeature(async () => {
  // Your test code here
});
```

#### TestAssertions
```typescript
// Enhanced assertions
TestAssertions.expectSuccessResponse(response);
TestAssertions.expectValidationError(response, 'email');
await TestAssertions.expectErrorWithContext(response, 'VALIDATION_ERROR');
```

## 📋 Writing New Tests

### Creating a New Standalone Feature Test

1. Create a new test file in `src/__tests__/features/`:
```typescript
// src/__tests__/features/sharing.standalone.test.ts
import { describe, it, expect, beforeEach, afterEach, jest } from '@jest/globals';
import { 
  DevTestEnvironment, 
  MockFactory, 
  FeatureTestUtils, 
  TestAssertions 
} from '../utils/dev-test-helpers';

describe('Sharing Feature - Standalone Tests', () => {
  beforeEach(() => {
    DevTestEnvironment.setup({
      developmentMode: true,
      authBypass: false,
      detailedErrors: true,
    });
  });

  afterEach(() => {
    DevTestEnvironment.cleanup();
    jest.clearAllMocks();
  });

  it('should create file share successfully', async () => {
    await FeatureTestUtils.testSharingFeature(async () => {
      // Your test implementation
    });
  });
});
```

2. Add the feature to the test runner:
```javascript
// scripts/test-features.js
const FEATURES = {
  auth: 'src/__tests__/features/auth.standalone.test.ts',
  files: 'src/__tests__/features/files.standalone.test.ts',
  admin: 'src/__tests__/features/admin.standalone.test.ts',
  sharing: 'src/__tests__/features/sharing.standalone.test.ts', // Add this
};
```

3. Add npm script:
```json
{
  "scripts": {
    "test:sharing": "node scripts/test-features.js sharing"
  }
}
```

### Best Practices

1. **Isolation**: Each test should be completely independent
2. **Mocking**: Mock all external dependencies (database, S3, email, etc.)
3. **Development Mode**: Use development mode for enhanced debugging
4. **Error Testing**: Test both success and error scenarios
5. **Context**: Use enhanced error context for better debugging
6. **Cleanup**: Always clean up test environment and mocks

## 🐛 Debugging Tests

### Common Issues and Solutions

1. **Tests failing due to environment variables**:
   ```bash
   # Make sure test environment is properly set up
   DevTestEnvironment.setup({ developmentMode: true });
   ```

2. **Mocks not working**:
   ```typescript
   // Ensure mocks are applied before importing modules
   jest.mock('@/lib/mongodb');
   const mockUser = MockFactory.createDatabaseMocks();
   require('@/models/User').User = mockUser;
   ```

3. **Authentication issues**:
   ```typescript
   // Use development mode bypass for testing
   const mockAuth = MockFactory.createAuthMocks({
     defaultSession: mockTestSession,
     developmentMode: true,
   });
   ```

### Debug Mode

Run tests with verbose output and development mode:
```bash
npm run test:auth -- --dev --verbose
```

This provides:
- Detailed error context
- Debug logging
- Enhanced error messages
- Development mode features

## 📊 Coverage Reports

Generate coverage reports for specific features:
```bash
npm run test:auth -- --coverage
npm run test:files -- --coverage
npm run test:all-features -- --coverage
```

Coverage reports are generated in `coverage/features/` directory.

## 🔄 Continuous Integration

For CI environments, use:
```bash
npm run test:ci              # All tests
npm run test:all-features    # All feature tests
```

These commands run without watch mode and generate coverage reports suitable for CI systems.

## 🛠️ Development Workflow Tools

The testing infrastructure is part of a comprehensive development workflow system. See the main development documentation for additional tools:

### Development CLI
```bash
npm run dev:cli              # Show all available commands
npm run dev:init sharing     # Initialize new feature with tests
npm run dev:generate test sharing  # Generate test file only
```

### Development Dashboard
Access the development dashboard at `http://localhost:3000/api/dev/dashboard` when running in development mode for:
- System health monitoring
- Feature flag management
- Code generation tools
- Testing utilities

### Integration with Development Mode
All test utilities automatically integrate with the development mode system:
- Authentication bypass when `DEVELOPMENT_MODE_BYPASS_AUTH=true`
- Enhanced error context when `DEVELOPMENT_DETAILED_ERRORS=true`
- Debug logging when `DEVELOPMENT_DEBUG_LOGGING=true`

This creates a seamless development experience where testing, debugging, and development tools work together.
