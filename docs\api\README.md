# Drivn API Documentation

This document provides comprehensive information about the Drivn API endpoints, authentication, and usage patterns.

## Base URL

```
https://your-domain.com/api
```

For local development:
```
http://localhost:3000/api
```

## Authentication

Drivn uses NextAuth.js for authentication. Most API endpoints require authentication via session cookies.

### Authentication Methods

1. **Email/Password**: Traditional email and password authentication
2. **Google OAuth**: Sign in with Google account
3. **Session-based**: Uses HTTP-only cookies for session management

### Protected Routes

All API routes under `/api` (except public endpoints) require authentication. Unauthenticated requests will receive a `401 Unauthorized` response.

### Admin Routes

Routes under `/api/admin` require admin privileges. Non-admin users will receive a `403 Forbidden` response.

## Rate Limiting

API endpoints are rate-limited to prevent abuse:

- **Authentication endpoints**: 5 requests per 15 minutes
- **Admin endpoints**: 10 requests per minute
- **File operations**: 200 requests per minute
- **General API**: 60 requests per minute
- **Public endpoints**: 100 requests per minute

Rate limit headers are included in responses:
- `X-RateLimit-Limit`: Maximum requests allowed
- `X-RateLimit-Remaining`: Remaining requests in current window
- `X-RateLimit-Reset`: Timestamp when the rate limit resets

## Response Format

All API responses follow a consistent JSON format:

### Success Response
```json
{
  "message": "Operation completed successfully",
  "data": {
    // Response data
  }
}
```

### Error Response
```json
{
  "error": "Error message",
  "code": "ERROR_CODE",
  "details": {
    // Additional error details (optional)
  }
}
```

## HTTP Status Codes

- `200 OK`: Request successful
- `201 Created`: Resource created successfully
- `400 Bad Request`: Invalid request data
- `401 Unauthorized`: Authentication required
- `403 Forbidden`: Access denied
- `404 Not Found`: Resource not found
- `409 Conflict`: Resource conflict (e.g., duplicate email)
- `413 Payload Too Large`: Request body too large
- `429 Too Many Requests`: Rate limit exceeded
- `500 Internal Server Error`: Server error

## API Endpoints

### Authentication
- `POST /api/auth/signup` - Create new user account
- `POST /api/auth/signin` - Sign in user
- `POST /api/auth/signout` - Sign out user
- `POST /api/auth/verify-email` - Verify email address
- `POST /api/auth/forgot-password` - Request password reset
- `POST /api/auth/reset-password` - Reset password

### User Management
- `GET /api/user/profile` - Get user profile
- `PUT /api/user/profile` - Update user profile
- `PUT /api/user/password` - Change password

### S3 Configuration
- `GET /api/s3/configure` - Check S3 configuration status
- `POST /api/s3/configure` - Configure S3 credentials
- `POST /api/s3/reset` - Reset S3 credentials

### File Management
- `GET /api/files` - List files
- `GET /api/files/[fileId]` - Get file details
- `PUT /api/files/[fileId]` - Update file
- `DELETE /api/files/[fileId]` - Delete file
- `GET /api/files/[fileId]/download` - Download file

### File Upload
- `POST /api/upload/url` - Get upload URL
- `POST /api/upload/complete` - Complete upload

### Folder Management
- `POST /api/folder/create` - Create folder
- `GET /api/folder/[folderId]` - Get folder contents
- `PUT /api/folder/[folderId]` - Rename folder
- `DELETE /api/folder/[folderId]` - Delete folder
- `GET /api/folder/tree` - Get folder tree

### File Sharing
- `POST /api/share/file` - Create file share
- `GET /api/share/file` - List user's shares
- `GET /api/share/access` - Access shared file (public)
- `POST /api/share/access` - Access shared file with password (public)
- `GET /api/share/[shareId]/download` - Download shared file (public)
- `POST /api/share/revoke` - Revoke share
- `DELETE /api/share/revoke` - Delete share

### Admin Endpoints
- `GET /api/admin/users` - List all users
- `PUT /api/admin/users` - Update user settings
- `GET /api/admin/stats` - Get platform statistics

### System Endpoints
- `GET /api/health` - Health check
- `HEAD /api/health` - Simple health ping
- `GET /api/metrics` - System metrics (admin only)
- `DELETE /api/metrics` - Clear metrics (admin only)

## Request Examples

### Create User Account
```bash
curl -X POST https://your-domain.com/api/auth/signup \
  -H "Content-Type: application/json" \
  -d '{
    "name": "John Doe",
    "email": "<EMAIL>",
    "password": "SecurePassword123!"
  }'
```

### Upload File
```bash
# 1. Get upload URL
curl -X POST https://your-domain.com/api/upload/url \
  -H "Content-Type: application/json" \
  -H "Cookie: next-auth.session-token=..." \
  -d '{
    "fileName": "document.pdf",
    "contentType": "application/pdf",
    "fileSize": 1048576
  }'

# 2. Upload to S3 (use the uploadUrl from step 1)
curl -X PUT "https://s3.amazonaws.com/bucket/key" \
  -H "Content-Type: application/pdf" \
  --data-binary @document.pdf

# 3. Complete upload
curl -X POST https://your-domain.com/api/upload/complete \
  -H "Content-Type: application/json" \
  -H "Cookie: next-auth.session-token=..." \
  -d '{
    "fileKey": "users/123/document.pdf",
    "fileName": "document.pdf",
    "originalName": "document.pdf",
    "contentType": "application/pdf",
    "fileSize": 1048576
  }'
```

### Share File
```bash
curl -X POST https://your-domain.com/api/share/file \
  -H "Content-Type: application/json" \
  -H "Cookie: next-auth.session-token=..." \
  -d '{
    "fileId": "507f1f77bcf86cd799439011",
    "permission": "view",
    "allowDownload": true,
    "expiresAt": "2024-12-31T23:59:59.000Z",
    "password": "optional-password",
    "maxAccessCount": 100,
    "sharedWithEmails": ["<EMAIL>"]
  }'
```

### List Files
```bash
curl -X GET "https://your-domain.com/api/files?page=1&limit=20&sortBy=createdAt&sortOrder=desc" \
  -H "Cookie: next-auth.session-token=..."
```

## Error Handling

The API uses standard HTTP status codes and provides detailed error messages:

```json
{
  "error": "Validation error",
  "code": "VALIDATION_ERROR",
  "details": [
    {
      "field": "email",
      "message": "Invalid email format"
    }
  ]
}
```

Common error codes:
- `VALIDATION_ERROR`: Request validation failed
- `AUTHENTICATION_ERROR`: Authentication required
- `AUTHORIZATION_ERROR`: Access denied
- `NOT_FOUND_ERROR`: Resource not found
- `CONFLICT_ERROR`: Resource conflict
- `RATE_LIMIT_ERROR`: Rate limit exceeded
- `EXTERNAL_SERVICE_ERROR`: External service error
- `INTERNAL_ERROR`: Internal server error

## Security Considerations

1. **HTTPS Only**: All API requests should use HTTPS in production
2. **Rate Limiting**: Respect rate limits to avoid being blocked
3. **Input Validation**: All inputs are validated and sanitized
4. **File Type Restrictions**: Only allowed file types can be uploaded
5. **Size Limits**: Files are limited to 100MB maximum
6. **Virus Scanning**: Uploaded files are scanned for malicious content

## SDKs and Libraries

Currently, the API is REST-based and can be used with any HTTP client. Official SDKs may be provided in the future for popular programming languages.

## Support

For API support and questions:
- 🐛 Issues: [GitHub Issues](https://github.com/Emmraan/drivn/issues)
