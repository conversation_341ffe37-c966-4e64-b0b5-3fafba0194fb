import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';
import { auth } from '@/lib/auth';
import { File } from '@/models/File';
import { Folder } from '@/models/Folder';
import connectDB from '@/lib/mongodb';

const completeUploadSchema = z.object({
  fileKey: z.string().min(1, 'File key is required'),
  fileName: z.string().min(1, 'File name is required'),
  originalName: z.string().min(1, 'Original name is required'),
  contentType: z.string().min(1, 'Content type is required'),
  fileSize: z.number().min(1, 'File size must be greater than 0'),
  folderId: z.string().optional(),
  storageSource: z.enum(['user', 'platform']).optional(),
});

export async function POST(request: NextRequest) {
  try {
    const session = await auth();
    if (!session) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const body = await request.json();
    const { fileKey, fileName, originalName, contentType, fileSize, folderId, storageSource } = completeUploadSchema.parse(body);

    await connectDB();

    // Validate folder if provided
    if (folderId) {
      const folder = await Folder.findOne({
        _id: folderId,
        userId: session.user.id,
      });

      if (!folder) {
        return NextResponse.json(
          { error: 'Folder not found or access denied' },
          { status: 404 }
        );
      }
    }

    // Check if file with same name already exists in the same folder
    const existingFile = await File.findOne({
      name: fileName,
      userId: session.user.id,
      folderId: folderId || null,
    });

    if (existingFile) {
      return NextResponse.json(
        { error: 'A file with this name already exists in this location' },
        { status: 400 }
      );
    }

    // Create file record in database
    const file = await File.create({
      name: fileName,
      originalName: originalName,
      size: fileSize,
      mimeType: contentType,
      s3Key: fileKey,
      userId: session.user.id,
      folderId: folderId || undefined,
      isPublic: false,
      downloadCount: 0,
      storageSource: storageSource,
    });

    return NextResponse.json({
      message: 'File upload completed successfully',
      file: {
        id: file._id,
        name: file.name,
        originalName: file.originalName,
        size: file.size,
        mimeType: file.mimeType,
        s3Key: file.s3Key,
        folderId: file.folderId,
        isPublic: file.isPublic,
        downloadCount: file.downloadCount,
        createdAt: file.createdAt,
        updatedAt: file.updatedAt,
      },
    }, { status: 201 });

  } catch (error) {
    console.error('Upload completion error:', error);
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Validation error', details: error.issues },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: 'Failed to complete upload' },
      { status: 500 }
    );
  }
}
