import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';
import { auth } from '@/lib/auth';
import { S3Service } from '@/lib/s3';
import { File } from '@/models/File';
import { Folder } from '@/models/Folder';
import connectDB from '@/lib/mongodb';

const proxyUploadSchema = z.object({
  fileKey: z.string().min(1, 'File key is required'),
  contentType: z.string().min(1, 'Content type is required'),
  fileName: z.string().min(1, 'File name is required'),
  fileSize: z.number().min(1, 'File size must be greater than 0'),
  folderId: z.string().optional(),
});

export async function PUT(request: NextRequest) {
  try {
    const session = await auth();
    if (!session) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Get query parameters
    const url = new URL(request.url);
    const fileKey = url.searchParams.get('fileKey');
    const contentType = url.searchParams.get('contentType');
    const fileName = url.searchParams.get('fileName');
    const fileSize = url.searchParams.get('fileSize');
    const folderId = url.searchParams.get('folderId');

    if (!fileKey || !contentType || !fileName || !fileSize) {
      return NextResponse.json(
        { error: 'Missing required parameters: fileKey, contentType, fileName, fileSize' },
        { status: 400 }
      );
    }

    // Validate parameters
    const {
      fileKey: validatedFileKey,
      contentType: validatedContentType,
      fileName: validatedFileName,
      fileSize: validatedFileSize,
      folderId: validatedFolderId
    } = proxyUploadSchema.parse({
      fileKey,
      contentType,
      fileName,
      fileSize: parseInt(fileSize),
      folderId: folderId || undefined,
    });

    // Get S3 service instance and determine storage source
    let s3Service: S3Service;
    let storageSource: 'user' | 'platform';

    try {
      const s3Config = S3Service.getS3ServiceForRequest(request, session);
      s3Service = s3Config.service;
      storageSource = s3Config.storageSource;
    } catch (error) {
      return NextResponse.json(
        { error: 'No S3 credentials configured. Please configure your storage settings.' },
        { status: 400 }
      );
    }

    // Get file data from request body
    const fileBuffer = await request.arrayBuffer();
    
    if (!fileBuffer || fileBuffer.byteLength === 0) {
      return NextResponse.json(
        { error: 'No file data provided' },
        { status: 400 }
      );
    }

    await connectDB();

    // Validate folder if provided
    if (validatedFolderId) {
      const folder = await Folder.findOne({
        _id: validatedFolderId,
        userId: session.user.id,
      });

      if (!folder) {
        return NextResponse.json(
          { error: 'Folder not found or access denied' },
          { status: 404 }
        );
      }
    }

    // Check if file with same name already exists in the same folder
    const existingFile = await File.findOne({
      name: validatedFileName,
      userId: session.user.id,
      folderId: validatedFolderId || null,
    });

    if (existingFile) {
      return NextResponse.json(
        { error: 'A file with this name already exists in this location' },
        { status: 400 }
      );
    }

    // Upload to S3 directly from server
    await s3Service.uploadFile(
      validatedFileKey,
      new Uint8Array(fileBuffer),
      validatedContentType
    );

    // Create file record in database
    const file = await File.create({
      name: validatedFileName,
      originalName: validatedFileName,
      size: validatedFileSize,
      mimeType: validatedContentType,
      s3Key: validatedFileKey,
      userId: session.user.id,
      folderId: validatedFolderId || undefined,
      isPublic: false,
      downloadCount: 0,
      storageSource: storageSource,
    });

    return NextResponse.json({
      message: 'File uploaded successfully',
      file: {
        id: file._id,
        name: file.name,
        originalName: file.originalName,
        size: file.size,
        mimeType: file.mimeType,
        s3Key: file.s3Key,
        folderId: file.folderId,
        isPublic: file.isPublic,
        downloadCount: file.downloadCount,
        createdAt: file.createdAt,
        updatedAt: file.updatedAt,
      },
    });

  } catch (error) {
    console.error('Proxy upload error:', error);
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Validation error', details: error.issues },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: 'Failed to upload file' },
      { status: 500 }
    );
  }
}
