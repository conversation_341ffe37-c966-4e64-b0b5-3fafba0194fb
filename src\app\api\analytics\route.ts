import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@/lib/auth';
import { File } from '@/models/File';
import { Share } from '@/models/Share';
import connectDB from '@/lib/mongodb';

export async function GET(request: NextRequest) {
  try {
    const session = await auth();
    if (!session) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const { searchParams } = new URL(request.url);
    const range = searchParams.get('range') || '30d';

    await connectDB();

    // Calculate date range
    const now = new Date();
    let startDate = new Date();
    
    switch (range) {
      case '7d':
        startDate.setDate(now.getDate() - 7);
        break;
      case '30d':
        startDate.setDate(now.getDate() - 30);
        break;
      case '90d':
        startDate.setDate(now.getDate() - 90);
        break;
      case '1y':
        startDate.setFullYear(now.getFullYear() - 1);
        break;
      default:
        startDate.setDate(now.getDate() - 30);
    }

    // Get user's files
    const files = await File.find({ 
      userId: session.user.id,
      createdAt: { $gte: startDate }
    }).lean();

    // Get user's shares
    const shares = await Share.find({ 
      userId: session.user.id,
      createdAt: { $gte: startDate }
    }).lean();

    // Calculate overview stats
    const totalFiles = await File.countDocuments({ userId: session.user.id });
    const totalSize = files.reduce((sum, file) => sum + file.size, 0);
    const totalDownloads = files.reduce((sum, file) => sum + (file.downloadCount || 0), 0);
    const totalShares = await Share.countDocuments({ userId: session.user.id });

    // Get top files by downloads
    const topFiles = await File.find({ 
      userId: session.user.id 
    })
    .sort({ downloadCount: -1 })
    .limit(10)
    .lean();

    // Generate recent activity
    const recentActivity = [
      ...files.slice(-10).map(file => ({
        date: file.createdAt.toISOString(),
        action: 'File uploaded',
        fileName: file.name,
        details: `${(file.size / 1024 / 1024).toFixed(2)} MB`
      })),
      ...shares.slice(-10).map(share => ({
        date: share.createdAt.toISOString(),
        action: 'File shared',
        fileName: share.fileName || 'Unknown file',
        details: 'Public link created'
      }))
    ].sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime()).slice(0, 10);

    // Calculate storage usage (assuming 5GB limit for now)
    const storageLimit = 5 * 1024 * 1024 * 1024; // 5GB in bytes
    const storageUsage = {
      used: totalSize,
      total: storageLimit,
      percentage: (totalSize / storageLimit) * 100
    };

    return NextResponse.json({
      overview: {
        totalFiles,
        totalSize,
        totalViews: totalDownloads, // Using downloads as views for now
        totalDownloads,
        totalShares,
      },
      recentActivity,
      topFiles: topFiles.map(file => ({
        id: file._id.toString(),
        name: file.name,
        downloads: file.downloadCount || 0,
        views: file.downloadCount || 0, // Using downloads as views for now
        size: file.size,
      })),
      storageUsage,
    });

  } catch (error) {
    console.error('Analytics fetch error:', error);
    return NextResponse.json(
      { error: 'Failed to fetch analytics data' },
      { status: 500 }
    );
  }
}
