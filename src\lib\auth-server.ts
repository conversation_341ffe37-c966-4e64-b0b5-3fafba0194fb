import bcrypt from 'bcryptjs';
import { User } from '@/models/User';
import connectDB from './mongodb';

/**
 * Server-side authentication utilities that require database access
 * These functions should only be used in API routes or server components
 */

export interface AuthUser {
  id: string;
  email: string;
  name: string;
  image?: string;
  verified: boolean;
  allowPlatformS3: boolean;
}

/**
 * Authenticate user with email and password
 * This function requires database access and should only be used server-side
 */
export async function authenticateUser(email: string, password: string): Promise<AuthUser | null> {
  if (!email || !password) {
    return null;
  }

  try {
    await connectDB();
    const user = await User.findOne({ email });

    if (!user || !user.passwordHash) {
      return null;
    }

    const isPasswordValid = await bcrypt.compare(password, user.passwordHash);

    if (!isPasswordValid) {
      return null;
    }

    return {
      id: user._id.toString(),
      email: user.email,
      name: user.name,
      image: user.avatarUrl,
      verified: user.verified,
      allowPlatformS3: user.allowPlatformS3,
    };
  } catch (error) {
    console.error('Auth error:', error);
    return null;
  }
}

/**
 * Handle Google OAuth sign-in
 * Creates user if doesn't exist, requires database access
 */
export async function handleGoogleSignIn(user: any): Promise<boolean> {
  try {
    await connectDB();
    const existingUser = await User.findOne({ email: user.email });

    if (!existingUser) {
      await User.create({
        name: user.name,
        email: user.email,
        avatarUrl: user.image,
        verified: true, // Google accounts are pre-verified
        allowPlatformS3: false,
      });
    }
    return true;
  } catch (error) {
    console.error('Google sign-in error:', error);
    return false;
  }
}

/**
 * Check if user is admin (server-side with database access)
 * This can be used when you need to verify admin status with database
 */
export async function isAdminWithDbCheck(email: string): Promise<boolean> {
  try {
    // First check environment variable for quick lookup
    const adminEmails = process.env.ADMIN_EMAILS?.split(',') || [];
    if (adminEmails.includes(email)) {
      return true;
    }

    // Could add database-based admin role checking here if needed
    // await connectDB();
    // const user = await User.findOne({ email, isAdmin: true });
    // return !!user;

    return false;
  } catch (error) {
    console.error('Admin check error:', error);
    return false;
  }
}
