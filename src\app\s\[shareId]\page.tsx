'use client';

import React from 'react';
import { useParams } from 'next/navigation';
import { Button } from '@/components/ui/Button';
import { Input } from '@/components/ui/Input';
import { toast } from '@/components/ui/Toast';
import { ThemeToggle } from '@/components/ui/ThemeToggle';
import {
  ArrowDownTrayIcon,
  EyeIcon,
  LockClosedIcon,
  ExclamationTriangleIcon,
} from '@heroicons/react/24/outline';
import { formatFileSize, formatDate } from '@/lib/utils';

interface ShareInfo {
  shareId: string;
  requiresPassword: boolean;
  expiresAt?: string;
  accessCount: number;
  maxAccessCount?: number;
  file: {
    name: string;
    size: number;
    mimeType: string;
  };
  owner: {
    name: string;
  };
}

interface ShareAccess {
  share: {
    id: string;
    shareId: string;
    permission: 'view' | 'edit';
    allowDownload: boolean;
    accessCount: number;
    maxAccessCount?: number;
    expiresAt?: string;
  };
  file: {
    id: string;
    name: string;
    originalName: string;
    size: number;
    mimeType: string;
    downloadCount: number;
  };
  owner: {
    id: string;
    name: string;
  };
}

export default function PublicSharePage() {
  const params = useParams();
  const shareId = params.shareId as string;
  
  const [shareInfo, setShareInfo] = React.useState<ShareInfo | null>(null);
  const [shareAccess, setShareAccess] = React.useState<ShareAccess | null>(null);
  const [loading, setLoading] = React.useState(true);
  const [password, setPassword] = React.useState('');
  const [passwordError, setPasswordError] = React.useState('');
  const [accessLoading, setAccessLoading] = React.useState(false);

  React.useEffect(() => {
    fetchShareInfo();
  }, [shareId]);

  const fetchShareInfo = async () => {
    try {
      const response = await fetch(`/api/share/access?shareId=${shareId}`);
      
      if (!response.ok) {
        if (response.status === 404) {
          throw new Error('Share not found or has expired');
        }
        throw new Error('Failed to load share');
      }
      
      const data = await response.json();
      setShareInfo(data);
    } catch (error) {
      console.error('Failed to fetch share info:', error);
      toast.error(error instanceof Error ? error.message : 'Failed to load share');
    } finally {
      setLoading(false);
    }
  };

  const handleAccessShare = async (e: React.FormEvent) => {
    e.preventDefault();
    setAccessLoading(true);
    setPasswordError('');

    try {
      const response = await fetch('/api/share/access', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          shareId,
          password: shareInfo?.requiresPassword ? password : undefined,
        }),
      });

      if (!response.ok) {
        const error = await response.json();
        if (response.status === 401 && error.requiresPassword) {
          setPasswordError('Password is required');
          return;
        }
        throw new Error(error.error || 'Failed to access share');
      }

      const data = await response.json();
      setShareAccess(data);
      toast.success('File accessed successfully');
    } catch (error) {
      console.error('Failed to access share:', error);
      const errorMessage = error instanceof Error ? error.message : 'Failed to access share';
      if (errorMessage.includes('password')) {
        setPasswordError(errorMessage);
      } else {
        toast.error(errorMessage);
      }
    } finally {
      setAccessLoading(false);
    }
  };

  const handleDownload = async () => {
    if (!shareAccess?.share.allowDownload) {
      toast.error('Downloads are not allowed for this file');
      return;
    }

    try {
      // Use proxy download to avoid S3 redirect issues
      const downloadUrl = `/api/share/${shareId}/download?proxy=true`;

      // Create a temporary link to trigger download
      const link = document.createElement('a');
      link.href = downloadUrl;
      link.style.display = 'none';
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);

      toast.success('Download started');
    } catch (error) {
      console.error('Download error:', error);
      toast.error('Failed to download file');
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-background flex items-center justify-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-accent"></div>
      </div>
    );
  }

  if (!shareInfo) {
    return (
      <div className="min-h-screen bg-background flex items-center justify-center px-4">
        <div className="max-w-md w-full text-center">
          <ExclamationTriangleIcon className="h-16 w-16 text-error mx-auto mb-4" />
          <h1 className="text-2xl font-bold text-text-primary mb-2">Share Not Found</h1>
          <p className="text-text-secondary mb-6">
            This share link is invalid, has expired, or has been revoked.
          </p>
          <Button onClick={() => window.close()}>
            Close
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-background">
      {/* Header */}
      <header className="border-b border-divider">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-6">
            <div className="flex items-center space-x-2">
              <div className="w-8 h-8 bg-accent rounded-lg flex items-center justify-center">
                <span className="text-white font-bold text-lg">D</span>
              </div>
              <span className="text-2xl font-bold text-text-primary">Drivn</span>
            </div>
            <ThemeToggle />
          </div>
        </div>
      </header>

      {/* Main Content */}
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        {!shareAccess ? (
          /* Access Form */
          <div className="max-w-md mx-auto">
            <div className="text-center mb-8">
              <div className="w-16 h-16 bg-accent/10 rounded-full flex items-center justify-center mx-auto mb-4">
                {shareInfo.requiresPassword ? (
                  <LockClosedIcon className="h-8 w-8 text-accent" />
                ) : (
                  <EyeIcon className="h-8 w-8 text-accent" />
                )}
              </div>
              <h1 className="text-2xl font-bold text-text-primary mb-2">
                {shareInfo.requiresPassword ? 'Protected File' : 'Shared File'}
              </h1>
              <p className="text-text-secondary">
                {shareInfo.owner.name} shared `{shareInfo.file.name}` with you
              </p>
            </div>

            <div className="bg-background border border-divider rounded-lg p-6 mb-6">
              <div className="space-y-3 text-sm">
                <div className="flex justify-between">
                  <span className="text-text-secondary">File size:</span>
                  <span className="text-text-primary">{formatFileSize(shareInfo.file.size)}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-text-secondary">File type:</span>
                  <span className="text-text-primary">{shareInfo.file.mimeType}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-text-secondary">Accessed:</span>
                  <span className="text-text-primary">
                    {shareInfo.accessCount} time{shareInfo.accessCount !== 1 ? 's' : ''}
                    {shareInfo.maxAccessCount && ` / ${shareInfo.maxAccessCount}`}
                  </span>
                </div>
                {shareInfo.expiresAt && (
                  <div className="flex justify-between">
                    <span className="text-text-secondary">Expires:</span>
                    <span className="text-text-primary">
                      {formatDate(new Date(shareInfo.expiresAt))}
                    </span>
                  </div>
                )}
              </div>
            </div>

            <form onSubmit={handleAccessShare} className="space-y-4">
              {shareInfo.requiresPassword && (
                <Input
                  type="password"
                  label="Password"
                  value={password}
                  onChange={(e) => setPassword(e.target.value)}
                  error={passwordError}
                  placeholder="Enter password to access file"
                  required
                />
              )}
              
              <Button
                type="submit"
                className="w-full"
                loading={accessLoading}
              >
                {shareInfo.requiresPassword ? 'Access File' : 'View File'}
              </Button>
            </form>
          </div>
        ) : (
          /* File Access View */
          <div className="max-w-2xl mx-auto">
            <div className="text-center mb-8">
              <h1 className="text-3xl font-bold text-text-primary mb-2">
                {shareAccess.file.name}
              </h1>
              <p className="text-text-secondary">
                Shared by {shareAccess.owner.name}
              </p>
            </div>

            {/* File Info Card */}
            <div className="bg-background border border-divider rounded-lg p-6 mb-6">
              <div className="grid grid-cols-2 gap-4 text-sm">
                <div>
                  <span className="text-text-secondary block">File Size</span>
                  <span className="text-text-primary font-medium">
                    {formatFileSize(shareAccess.file.size)}
                  </span>
                </div>
                <div>
                  <span className="text-text-secondary block">File Type</span>
                  <span className="text-text-primary font-medium">
                    {shareAccess.file.mimeType}
                  </span>
                </div>
                <div>
                  <span className="text-text-secondary block">Permission</span>
                  <span className="text-text-primary font-medium">
                    {shareAccess.share.permission === 'view' ? 'View Only' : 'Can Edit'}
                  </span>
                </div>
                <div>
                  <span className="text-text-secondary block">Downloads</span>
                  <span className="text-text-primary font-medium">
                    {shareAccess.file.downloadCount}
                  </span>
                </div>
              </div>
            </div>

            {/* Actions */}
            <div className="flex justify-center space-x-4">
              {shareAccess.share.allowDownload && (
                <Button onClick={handleDownload} className="flex items-center">
                  <ArrowDownTrayIcon className="h-4 w-4 mr-2" />
                  Download File
                </Button>
              )}
              <Button variant="secondary" onClick={() => window.close()}>
                Close
              </Button>
            </div>

            {/* Access Info */}
            <div className="mt-8 text-center text-sm text-text-secondary">
              <p>
                This file has been accessed {shareAccess.share.accessCount} time
                {shareAccess.share.accessCount !== 1 ? 's' : ''}
                {shareAccess.share.maxAccessCount && (
                  <> out of {shareAccess.share.maxAccessCount} allowed</>
                )}
              </p>
              {shareAccess.share.expiresAt && (
                <p className="mt-1">
                  This link expires on {formatDate(new Date(shareAccess.share.expiresAt))}
                </p>
              )}
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
