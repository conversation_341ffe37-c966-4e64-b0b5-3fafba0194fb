import { NextRequest } from 'next/server';
import { Session } from 'next-auth';
import { jest } from '@jest/globals';

export const mockUser = {
  id: '507f1f77bcf86cd799439011',
  email: '<EMAIL>',
  name: 'Test User',
  verified: true,
  allowPlatformS3: false,
};

export const mockAdminUser = {
  id: '507f1f77bcf86cd799439012',
  email: '<EMAIL>',
  name: 'Admin User',
  verified: true,
  allowPlatformS3: true,
};

export const mockSession: Session = {
  user: mockUser,
  expires: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString(),
};

export const mockAdminSession: Session = {
  user: mockAdminUser,
  expires: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString(),
};

export function createMockRequest(
  url: string,
  options: {
    method?: string;
    headers?: Record<string, string>;
    body?: any;
    cookies?: Record<string, string>;
  } = {}
): NextRequest {
  const {
    method = 'GET',
    headers = {},
    body,
    cookies = {},
  } = options;

  const request = new NextRequest(url, {
    method,
    headers: {
      'Content-Type': 'application/json',
      ...headers,
    },
    body: body ? JSON.stringify(body) : undefined,
  });

  Object.entries(cookies).forEach(([name, value]) => {
    request.cookies.set(name, value);
  });

  return request;
}

export const mockFile = {
  _id: '507f1f77bcf86cd799439013',
  name: 'test-file.txt',
  originalName: 'test-file.txt',
  size: 1024,
  mimeType: 'text/plain',
  s3Key: 'users/507f1f77bcf86cd799439011/test-file.txt',
  userId: '507f1f77bcf86cd799439011',
  isPublic: false,
  downloadCount: 0,
  createdAt: new Date(),
  updatedAt: new Date(),
  tags: [],
  description: 'Test file',
};

export const mockFolder = {
  _id: '507f1f77bcf86cd799439014',
  name: 'Test Folder',
  userId: '507f1f77bcf86cd799439011',
  path: '/Test Folder',
  createdAt: new Date(),
  updatedAt: new Date(),
};

export const mockShare = {
  _id: '507f1f77bcf86cd799439015',
  fileId: '507f1f77bcf86cd799439013',
  userId: '507f1f77bcf86cd799439011',
  shareId: 'abc123def456',
  permission: 'view' as const,
  allowDownload: true,
  accessCount: 0,
  isActive: true,
  createdAt: new Date(),
  updatedAt: new Date(),
};

export function mockMongoose() {
  const mockModel = {
    find: jest.fn(),
    findOne: jest.fn(),
    findById: jest.fn(),
    findByIdAndUpdate: jest.fn(),
    findByIdAndDelete: jest.fn(),
    create: jest.fn(),
    updateOne: jest.fn(),
    deleteOne: jest.fn(),
    countDocuments: jest.fn(),
    aggregate: jest.fn(),
    sort: jest.fn(),
    skip: jest.fn(),
    limit: jest.fn(),
    populate: jest.fn(),
    select: jest.fn(),
    save: jest.fn(),
  };

  Object.keys(mockModel).forEach(method => {
    if (['find', 'findOne', 'aggregate'].includes(method)) {
      mockModel[method].mockReturnValue({
        ...mockModel,
        exec: jest.fn(),
      });
    }
  });

  return mockModel;
}

export function mockAuth(session: Session | null = mockSession) {
  return jest.fn().mockResolvedValue(session);
}

export function mockS3Service() {
  return {
    validateCredentials: jest.fn().mockResolvedValue(true),
    generateFileKey: jest.fn().mockReturnValue('test-key'),
    getPresignedUploadUrl: jest.fn().mockResolvedValue('https://example.com/upload'),
    getPresignedDownloadUrl: jest.fn().mockResolvedValue('https://example.com/download'),
    deleteFile: jest.fn().mockResolvedValue(undefined),
  };
}

export function mockEmailService() {
  return {
    sendVerificationEmail: jest.fn().mockResolvedValue(undefined),
    sendPasswordResetEmail: jest.fn().mockResolvedValue(undefined),
    sendShareNotificationEmail: jest.fn().mockResolvedValue(undefined),
  };
}

export function generateTestUser(overrides: Partial<typeof mockUser> = {}) {
  return {
    ...mockUser,
    ...overrides,
    _id: overrides.id || mockUser.id,
  };
}

export function generateTestFile(overrides: Partial<typeof mockFile> = {}) {
  return {
    ...mockFile,
    ...overrides,
  };
}

export function generateTestFolder(overrides: Partial<typeof mockFolder> = {}) {
  return {
    ...mockFolder,
    ...overrides,
  };
}

export function generateTestShare(overrides: Partial<typeof mockShare> = {}) {
  return {
    ...mockShare,
    ...overrides,
  };
}

export function expectValidationError(response: Response, field?: string) {
  expect(response.status).toBe(400);
  if (field) {
    // Additional validation error checks can be added here
  }
}

export function expectAuthError(response: Response) {
  expect(response.status).toBe(401);
}

export function expectForbiddenError(response: Response) {
  expect(response.status).toBe(403);
}

export function expectNotFoundError(response: Response) {
  expect(response.status).toBe(404);
}

export function expectRateLimitError(response: Response) {
  expect(response.status).toBe(429);
}

// Cleanup helpers
export function cleanupMocks() {
  jest.clearAllMocks();
  jest.resetAllMocks();
}

// Environment setup
export function setupTestEnvironment() {
  process.env.NODE_ENV = 'test';
  process.env.NEXTAUTH_SECRET = 'test-secret';
  process.env.MONGODB_URI = 'mongodb://localhost:27017/drivn-test';
  process.env.ENCRYPTION_KEY = 'test-encryption-key-32-characters';
  process.env.ADMIN_EMAILS = '<EMAIL>';
}

// Async test helpers
export async function waitFor(condition: () => boolean, timeout: number = 5000): Promise<void> {
  const start = Date.now();
  while (!condition() && Date.now() - start < timeout) {
    await new Promise(resolve => setTimeout(resolve, 100));
  }
  if (!condition()) {
    throw new Error('Condition not met within timeout');
  }
}

export function delay(ms: number): Promise<void> {
  return new Promise(resolve => setTimeout(resolve, ms));
}
