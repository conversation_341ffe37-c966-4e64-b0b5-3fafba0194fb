'use client';

import React from 'react';
import { Toaster, toast as hotToast } from 'react-hot-toast';
import { CheckCircleIcon, XCircleIcon, ExclamationTriangleIcon, InformationCircleIcon } from '@heroicons/react/24/outline';

// Custom toast component
const CustomToast: React.FC<{
  type: 'success' | 'error' | 'warning' | 'info';
  message: string;
  onDismiss: () => void;
}> = ({ type, message, onDismiss }) => {
  const icons = {
    success: <CheckCircleIcon className="h-5 w-5 text-success" />,
    error: <XCircleIcon className="h-5 w-5 text-error" />,
    warning: <ExclamationTriangleIcon className="h-5 w-5 text-warning" />,
    info: <InformationCircleIcon className="h-5 w-5 text-accent" />,
  };

  const bgColors = {
    success: 'bg-success/10 border-success/20',
    error: 'bg-error/10 border-error/20',
    warning: 'bg-warning/10 border-warning/20',
    info: 'bg-accent/10 border-accent/20',
  };

  return (
    <div className={`flex items-center p-4 rounded-lg border ${bgColors[type]} backdrop-blur-sm`}>
      <div className="flex-shrink-0">
        {icons[type]}
      </div>
      <div className="ml-3 flex-1">
        <p className="text-sm font-medium text-text-primary">{message}</p>
      </div>
      <button
        onClick={onDismiss}
        className="ml-4 flex-shrink-0 text-text-secondary hover:text-text-primary transition-colors"
      >
        <XCircleIcon className="h-4 w-4" />
      </button>
    </div>
  );
};

// Toast functions
export const toast = {
  success: (message: string) => {
    hotToast.custom((t) => (
      <CustomToast
        type="success"
        message={message}
        onDismiss={() => hotToast.dismiss(t.id)}
      />
    ), {
      duration: 4000,
    });
  },
  
  error: (message: string) => {
    hotToast.custom((t) => (
      <CustomToast
        type="error"
        message={message}
        onDismiss={() => hotToast.dismiss(t.id)}
      />
    ), {
      duration: 6000,
    });
  },
  
  warning: (message: string) => {
    hotToast.custom((t) => (
      <CustomToast
        type="warning"
        message={message}
        onDismiss={() => hotToast.dismiss(t.id)}
      />
    ), {
      duration: 5000,
    });
  },
  
  info: (message: string) => {
    hotToast.custom((t) => (
      <CustomToast
        type="info"
        message={message}
        onDismiss={() => hotToast.dismiss(t.id)}
      />
    ), {
      duration: 4000,
    });
  },
  
  loading: (message: string) => {
    return hotToast.loading(message, {
      style: {
        background: 'var(--secondary)',
        color: 'var(--text-primary)',
        border: '1px solid var(--divider)',
      },
    });
  },
  
  dismiss: (toastId?: string) => {
    hotToast.dismiss(toastId);
  },
  
  promise: <T,>(
    promise: Promise<T>,
    messages: {
      loading: string;
      success: string | ((data: T) => string);
      error: string | ((error: any) => string);
    }
  ) => {
    return hotToast.promise(promise, messages, {
      style: {
        background: 'var(--secondary)',
        color: 'var(--text-primary)',
        border: '1px solid var(--divider)',
      },
    });
  },
};

// Toast provider component
export const ToastProvider: React.FC = () => {
  return (
    <Toaster
      position="top-right"
      gutter={8}
      containerStyle={{
        top: 20,
        right: 20,
      }}
      toastOptions={{
        duration: 4000,
        style: {
          background: 'var(--background)',
          color: 'var(--text-primary)',
          border: '1px solid var(--divider)',
          borderRadius: '8px',
          fontSize: '14px',
          maxWidth: '400px',
        },
      }}
    />
  );
};
