import { S3Client, ListObjectsV2Command, HeadObjectCommand } from '@aws-sdk/client-s3';
import { File } from '@/models/File';
import { User } from '@/models/User';
import connectDB from './mongodb';
import { S3Service } from './s3';
import { S3Credentials } from './encryption';

export interface SyncResult {
  added: number;
  updated: number;
  deleted: number;
  errors: string[];
}

export interface S3Object {
  key: string;
  size: number;
  lastModified: Date;
  etag: string;
}

export class S3SyncService {
  private s3Service: S3Service;
  private userId: string;

  constructor(s3Service: S3Service, userId: string) {
    this.s3Service = s3Service;
    this.userId = userId;
  }

  /**
   * Perform a full synchronization between S3 and database
   */
  async performFullSync(): Promise<SyncResult> {
    const result: SyncResult = {
      added: 0,
      updated: 0,
      deleted: 0,
      errors: []
    };

    try {
      await connectDB();

      // Get all S3 objects for this user
      const s3Objects = await this.listS3Objects();
      
      // Get all database files for this user
      const dbFiles = await File.find({ userId: this.userId }).lean();

      // Create maps for efficient lookup
      const s3ObjectMap = new Map(s3Objects.map(obj => [obj.key, obj]));
      const dbFileMap = new Map(dbFiles.map(file => [file.s3Key, file]));

      // Find files to add (exist in S3 but not in DB)
      for (const [s3Key, s3Object] of s3ObjectMap) {
        if (!dbFileMap.has(s3Key)) {
          try {
            await this.addFileFromS3(s3Object);
            result.added++;
          } catch (error) {
            result.errors.push(`Failed to add ${s3Key}: ${error instanceof Error ? error.message : 'Unknown error'}`);
          }
        }
      }

      // Find files to update (exist in both but different)
      for (const [s3Key, s3Object] of s3ObjectMap) {
        const dbFile = dbFileMap.get(s3Key);
        if (dbFile && this.needsUpdate(dbFile, s3Object)) {
          try {
            await this.updateFileFromS3(dbFile, s3Object);
            result.updated++;
          } catch (error) {
            result.errors.push(`Failed to update ${s3Key}: ${error instanceof Error ? error.message : 'Unknown error'}`);
          }
        }
      }

      // Find files to delete (exist in DB but not in S3)
      for (const [s3Key, dbFile] of dbFileMap) {
        if (!s3ObjectMap.has(s3Key)) {
          try {
            await File.findByIdAndDelete(dbFile._id);
            result.deleted++;
          } catch (error) {
            result.errors.push(`Failed to delete ${s3Key}: ${error instanceof Error ? error.message : 'Unknown error'}`);
          }
        }
      }

    } catch (error) {
      result.errors.push(`Sync failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }

    return result;
  }

  /**
   * List all objects in S3 for this user
   */
  private async listS3Objects(): Promise<S3Object[]> {
    const objects: S3Object[] = [];
    const userPrefix = `users/${this.userId}/`;
    
    let continuationToken: string | undefined;
    
    do {
      const command = new ListObjectsV2Command({
        Bucket: (this.s3Service as any).bucket,
        Prefix: userPrefix,
        ContinuationToken: continuationToken,
        MaxKeys: 1000
      });

      const response = await (this.s3Service as any).client.send(command);
      
      if (response.Contents) {
        for (const object of response.Contents) {
          if (object.Key && object.Size !== undefined && object.LastModified && object.ETag) {
            objects.push({
              key: object.Key,
              size: object.Size,
              lastModified: object.LastModified,
              etag: object.ETag.replace(/"/g, '') // Remove quotes from ETag
            });
          }
        }
      }
      
      continuationToken = response.NextContinuationToken;
    } while (continuationToken);

    return objects;
  }

  /**
   * Add a new file to database from S3 object
   */
  private async addFileFromS3(s3Object: S3Object): Promise<void> {
    // Extract filename from S3 key
    const fileName = s3Object.key.split('/').pop() || 'unknown';
    
    // Remove timestamp prefix if it exists (format: timestamp-filename)
    const cleanFileName = fileName.replace(/^\d+-/, '');

    // Try to determine MIME type from file extension
    const mimeType = this.getMimeTypeFromExtension(cleanFileName);

    await File.create({
      name: cleanFileName,
      originalName: cleanFileName,
      size: s3Object.size,
      mimeType: mimeType,
      s3Key: s3Object.key,
      userId: this.userId,
      isPublic: false,
      downloadCount: 0,
      syncedAt: new Date(),
      etag: s3Object.etag
    });
  }

  /**
   * Update existing file from S3 object
   */
  private async updateFileFromS3(dbFile: any, s3Object: S3Object): Promise<void> {
    await File.findByIdAndUpdate(dbFile._id, {
      size: s3Object.size,
      syncedAt: new Date(),
      etag: s3Object.etag,
      updatedAt: new Date()
    });
  }

  /**
   * Check if file needs update based on S3 object
   */
  private needsUpdate(dbFile: any, s3Object: S3Object): boolean {
    // Compare ETags (most reliable way to detect changes)
    if (dbFile.etag && dbFile.etag !== s3Object.etag) {
      return true;
    }

    // Compare sizes as fallback
    if (dbFile.size !== s3Object.size) {
      return true;
    }

    // Compare last modified dates
    const dbLastModified = new Date(dbFile.updatedAt || dbFile.createdAt);
    if (s3Object.lastModified > dbLastModified) {
      return true;
    }

    return false;
  }

  /**
   * Get MIME type from file extension
   */
  private getMimeTypeFromExtension(fileName: string): string {
    const extension = fileName.split('.').pop()?.toLowerCase();
    
    const mimeTypes: Record<string, string> = {
      // Images
      'jpg': 'image/jpeg',
      'jpeg': 'image/jpeg',
      'png': 'image/png',
      'gif': 'image/gif',
      'webp': 'image/webp',
      'svg': 'image/svg+xml',
      
      // Documents
      'pdf': 'application/pdf',
      'doc': 'application/msword',
      'docx': 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
      'xls': 'application/vnd.ms-excel',
      'xlsx': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      'ppt': 'application/vnd.ms-powerpoint',
      'pptx': 'application/vnd.openxmlformats-officedocument.presentationml.presentation',
      
      // Text
      'txt': 'text/plain',
      'html': 'text/html',
      'css': 'text/css',
      'js': 'text/javascript',
      'json': 'application/json',
      'xml': 'application/xml',
      
      // Video
      'mp4': 'video/mp4',
      'webm': 'video/webm',
      'ogg': 'video/ogg',
      'avi': 'video/x-msvideo',
      'mov': 'video/quicktime',
      
      // Audio
      'mp3': 'audio/mpeg',
      'wav': 'audio/wav',
      'ogg': 'audio/ogg',
      'flac': 'audio/flac',
      
      // Archives
      'zip': 'application/zip',
      'rar': 'application/x-rar-compressed',
      '7z': 'application/x-7z-compressed',
      'tar': 'application/x-tar',
      'gz': 'application/gzip'
    };

    return mimeTypes[extension || ''] || 'application/octet-stream';
  }
}

/**
 * Global sync manager for handling multiple users
 */
export class GlobalSyncManager {
  private static instance: GlobalSyncManager;
  private syncInProgress = new Set<string>();

  static getInstance(): GlobalSyncManager {
    if (!GlobalSyncManager.instance) {
      GlobalSyncManager.instance = new GlobalSyncManager();
    }
    return GlobalSyncManager.instance;
  }

  /**
   * Sync a specific user's files
   */
  async syncUser(userId: string, s3Credentials: S3Credentials): Promise<SyncResult> {
    if (this.syncInProgress.has(userId)) {
      throw new Error('Sync already in progress for this user');
    }

    this.syncInProgress.add(userId);

    try {
      const s3Service = new S3Service(s3Credentials);
      const syncService = new S3SyncService(s3Service, userId);
      
      const result = await syncService.performFullSync();
      
      // Update user's last sync time
      await User.findByIdAndUpdate(userId, {
        lastSyncAt: new Date()
      });

      return result;
    } finally {
      this.syncInProgress.delete(userId);
    }
  }

  /**
   * Check if sync is in progress for a user
   */
  isSyncInProgress(userId: string): boolean {
    return this.syncInProgress.has(userId);
  }

  /**
   * Sync all users with configured S3 credentials
   */
  async syncAllUsers(): Promise<Record<string, SyncResult>> {
    await connectDB();
    
    // This would require storing S3 credentials in the database
    // For now, we'll skip this implementation as it requires architectural changes
    throw new Error('Global sync not implemented - requires S3 credentials storage');
  }
}
