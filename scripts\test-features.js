#!/usr/bin/env node

/**
 * Feature Test Runner
 * 
 * This script allows running individual feature tests in isolation,
 * making it easy to develop and debug specific features without
 * running the entire test suite.
 * 
 * Usage:
 *   npm run test:feature auth
 *   npm run test:feature files
 *   npm run test:feature admin
 *   npm run test:feature all
 */

const { spawn } = require('child_process');
const path = require('path');
const fs = require('fs');

// Available feature tests
const FEATURES = {
  auth: 'src/__tests__/features/auth.standalone.test.ts',
  files: 'src/__tests__/features/files.standalone.test.ts',
  admin: 'src/__tests__/features/admin.standalone.test.ts',
};

// Color codes for console output
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m',
};

function colorize(text, color) {
  return `${colors[color]}${text}${colors.reset}`;
}

function printUsage() {
  console.log(colorize('\n🧪 Feature Test Runner', 'cyan'));
  console.log(colorize('========================', 'cyan'));
  console.log('\nAvailable commands:');
  console.log(colorize('  npm run test:feature auth', 'green') + '   - Test authentication feature');
  console.log(colorize('  npm run test:feature files', 'green') + '  - Test file management feature');
  console.log(colorize('  npm run test:feature admin', 'green') + '  - Test admin feature');
  console.log(colorize('  npm run test:feature all', 'green') + '    - Test all features');
  console.log('\nOptions:');
  console.log('  --watch     - Run tests in watch mode');
  console.log('  --coverage  - Generate coverage report');
  console.log('  --verbose   - Show detailed output');
  console.log('  --dev       - Run with development mode enabled');
  console.log('\nExamples:');
  console.log(colorize('  npm run test:feature auth -- --watch', 'yellow'));
  console.log(colorize('  npm run test:feature files -- --coverage', 'yellow'));
  console.log(colorize('  npm run test:feature all -- --dev --verbose', 'yellow'));
  console.log('');
}

function validateFeature(feature) {
  if (feature === 'all') {
    return true;
  }
  
  if (!FEATURES[feature]) {
    console.error(colorize(`❌ Unknown feature: ${feature}`, 'red'));
    console.error(colorize(`Available features: ${Object.keys(FEATURES).join(', ')}, all`, 'yellow'));
    return false;
  }
  
  const testFile = path.join(process.cwd(), FEATURES[feature]);
  if (!fs.existsSync(testFile)) {
    console.error(colorize(`❌ Test file not found: ${testFile}`, 'red'));
    return false;
  }
  
  return true;
}

function buildJestCommand(feature, options) {
  const jestArgs = ['jest'];
  
  // Add test file pattern
  if (feature === 'all') {
    jestArgs.push('src/__tests__/features/*.standalone.test.ts');
  } else {
    jestArgs.push(FEATURES[feature]);
  }
  
  // Add Jest options
  if (options.watch) {
    jestArgs.push('--watch');
  }
  
  if (options.coverage) {
    jestArgs.push('--coverage');
    jestArgs.push('--coverageDirectory=coverage/features');
  }
  
  if (options.verbose) {
    jestArgs.push('--verbose');
  }
  
  // Add custom environment variables for development mode
  const env = { ...process.env };
  if (options.dev) {
    env.NODE_ENV = 'development';
    env.DEVELOPMENT_MODE_BYPASS_AUTH = 'true';
    env.DEVELOPMENT_DETAILED_ERRORS = 'true';
    env.DEVELOPMENT_DEBUG_LOGGING = 'true';
  }
  
  return { command: 'npx', args: jestArgs, env };
}

function runTest(feature, options) {
  console.log(colorize(`\n🚀 Running ${feature} feature tests...`, 'cyan'));
  console.log(colorize('='.repeat(40), 'cyan'));
  
  const { command, args, env } = buildJestCommand(feature, options);
  
  if (options.verbose) {
    console.log(colorize(`Command: ${command} ${args.join(' ')}`, 'blue'));
    if (options.dev) {
      console.log(colorize('Development mode: ENABLED', 'yellow'));
    }
    console.log('');
  }
  
  const child = spawn(command, args, {
    stdio: 'inherit',
    env,
    shell: true,
  });
  
  child.on('close', (code) => {
    if (code === 0) {
      console.log(colorize(`\n✅ ${feature} tests completed successfully!`, 'green'));
    } else {
      console.log(colorize(`\n❌ ${feature} tests failed with exit code ${code}`, 'red'));
      process.exit(code);
    }
  });
  
  child.on('error', (error) => {
    console.error(colorize(`❌ Failed to run tests: ${error.message}`, 'red'));
    process.exit(1);
  });
}

async function runAllFeatures(options) {
  console.log(colorize('\n🧪 Running all feature tests...', 'cyan'));
  console.log(colorize('='.repeat(40), 'cyan'));
  
  const features = Object.keys(FEATURES);
  let failedFeatures = [];
  
  for (const feature of features) {
    console.log(colorize(`\n📋 Testing ${feature} feature...`, 'magenta'));
    
    const { command, args, env } = buildJestCommand(feature, { ...options, watch: false });
    
    try {
      await new Promise((resolve, reject) => {
        const child = spawn(command, args, {
          stdio: options.verbose ? 'inherit' : 'pipe',
          env,
          shell: true,
        });
        
        let output = '';
        if (!options.verbose) {
          child.stdout?.on('data', (data) => {
            output += data.toString();
          });
          child.stderr?.on('data', (data) => {
            output += data.toString();
          });
        }
        
        child.on('close', (code) => {
          if (code === 0) {
            console.log(colorize(`✅ ${feature} tests passed`, 'green'));
            resolve();
          } else {
            console.log(colorize(`❌ ${feature} tests failed`, 'red'));
            if (!options.verbose && output) {
              console.log(colorize('Output:', 'yellow'));
              console.log(output);
            }
            failedFeatures.push(feature);
            resolve(); // Continue with other tests
          }
        });
        
        child.on('error', (error) => {
          console.error(colorize(`❌ Failed to run ${feature} tests: ${error.message}`, 'red'));
          failedFeatures.push(feature);
          resolve();
        });
      });
    } catch (error) {
      console.error(colorize(`❌ Error running ${feature} tests: ${error.message}`, 'red'));
      failedFeatures.push(feature);
    }
  }
  
  // Summary
  console.log(colorize('\n📊 Test Summary', 'cyan'));
  console.log(colorize('='.repeat(20), 'cyan'));
  
  const passedFeatures = features.filter(f => !failedFeatures.includes(f));
  
  if (passedFeatures.length > 0) {
    console.log(colorize(`✅ Passed: ${passedFeatures.join(', ')}`, 'green'));
  }
  
  if (failedFeatures.length > 0) {
    console.log(colorize(`❌ Failed: ${failedFeatures.join(', ')}`, 'red'));
    process.exit(1);
  } else {
    console.log(colorize('\n🎉 All feature tests passed!', 'green'));
  }
}

function parseArguments() {
  const args = process.argv.slice(2);
  
  if (args.length === 0) {
    printUsage();
    process.exit(0);
  }
  
  const feature = args[0];
  const options = {
    watch: args.includes('--watch'),
    coverage: args.includes('--coverage'),
    verbose: args.includes('--verbose'),
    dev: args.includes('--dev'),
  };
  
  return { feature, options };
}

async function main() {
  try {
    const { feature, options } = parseArguments();
    
    if (!validateFeature(feature)) {
      process.exit(1);
    }
    
    if (feature === 'all') {
      await runAllFeatures(options);
    } else {
      runTest(feature, options);
    }
  } catch (error) {
    console.error(colorize(`❌ Unexpected error: ${error.message}`, 'red'));
    process.exit(1);
  }
}

// Handle process termination
process.on('SIGINT', () => {
  console.log(colorize('\n\n👋 Test runner interrupted', 'yellow'));
  process.exit(0);
});

process.on('SIGTERM', () => {
  console.log(colorize('\n\n👋 Test runner terminated', 'yellow'));
  process.exit(0);
});

if (require.main === module) {
  main();
}

module.exports = {
  FEATURES,
  validateFeature,
  buildJestCommand,
  runTest,
  runAllFeatures,
};
