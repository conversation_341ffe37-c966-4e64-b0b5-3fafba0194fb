'use client';

import React, { useState, useEffect } from 'react';
import { 
  ArrowPathIcon, 
  CheckCircleIcon, 
  ExclamationTriangleIcon,
  InformationCircleIcon,
  ClockIcon
} from '@heroicons/react/24/outline';

interface SyncResult {
  added: number;
  updated: number;
  deleted: number;
  errors: string[];
}

interface SyncStatus {
  syncInProgress: boolean;
}

export default function SyncManager() {
  const [syncStatus, setSyncStatus] = useState<SyncStatus>({ syncInProgress: false });
  const [lastSyncResult, setLastSyncResult] = useState<SyncResult | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [showDetails, setShowDetails] = useState(false);
  const [syncEnabled, setSyncEnabled] = useState(false);
  const [lastSyncAt, setLastSyncAt] = useState<string | null>(null);

  // Check sync status and settings on component mount
  useEffect(() => {
    checkSyncStatus();
    fetchSyncSettings();
  }, []);

  const checkSyncStatus = async () => {
    try {
      const response = await fetch('/api/sync');
      if (response.ok) {
        const status = await response.json();
        setSyncStatus(status);
      }
    } catch (error) {
      console.error('Failed to check sync status:', error);
    }
  };

  const fetchSyncSettings = async () => {
    try {
      const response = await fetch('/api/sync/settings');
      if (response.ok) {
        const settings = await response.json();
        setSyncEnabled(settings.syncEnabled);
        setLastSyncAt(settings.lastSyncAt);
      }
    } catch (error) {
      console.error('Failed to fetch sync settings:', error);
    }
  };

  const toggleSyncEnabled = async () => {
    try {
      const response = await fetch('/api/sync/settings', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ syncEnabled: !syncEnabled })
      });

      if (response.ok) {
        setSyncEnabled(!syncEnabled);
      } else {
        const data = await response.json();
        setError(data.error || 'Failed to update sync settings');
      }
    } catch (error) {
      setError(error instanceof Error ? error.message : 'Network error');
    }
  };

  const startSync = async () => {
    if (syncStatus.syncInProgress) {
      return;
    }

    setIsLoading(true);
    setError(null);
    setLastSyncResult(null);

    try {
      const response = await fetch('/api/sync', {
        method: 'POST',
      });

      const data = await response.json();

      if (response.ok) {
        setLastSyncResult(data.result);
        setSyncStatus({ syncInProgress: false });
      } else {
        setError(data.error || 'Sync failed');
      }
    } catch (error) {
      setError(error instanceof Error ? error.message : 'Network error');
    } finally {
      setIsLoading(false);
    }
  };

  const getSyncStatusIcon = () => {
    if (isLoading || syncStatus.syncInProgress) {
      return <ArrowPathIcon className="h-5 w-5 animate-spin text-blue-500" />;
    }
    
    if (error) {
      return <ExclamationTriangleIcon className="h-5 w-5 text-red-500" />;
    }
    
    if (lastSyncResult) {
      return <CheckCircleIcon className="h-5 w-5 text-green-500" />;
    }
    
    return <ClockIcon className="h-5 w-5 text-gray-500" />;
  };

  const getSyncStatusText = () => {
    if (isLoading) {
      return 'Synchronizing...';
    }
    
    if (syncStatus.syncInProgress) {
      return 'Sync in progress...';
    }
    
    if (error) {
      return `Sync failed: ${error}`;
    }
    
    if (lastSyncResult) {
      const { added, updated, deleted } = lastSyncResult;
      const totalChanges = added + updated + deleted;
      
      if (totalChanges === 0) {
        return 'Everything is up to date';
      }
      
      return `Sync completed: ${totalChanges} changes`;
    }
    
    return 'Ready to sync';
  };

  const renderSyncDetails = () => {
    if (!lastSyncResult) return null;

    const { added, updated, deleted, errors } = lastSyncResult;
    const hasChanges = added > 0 || updated > 0 || deleted > 0;
    const hasErrors = errors.length > 0;

    return (
      <div className="mt-4 p-4 bg-gray-50 rounded-lg">
        <h4 className="font-medium text-gray-900 mb-3">Sync Details</h4>
        
        {hasChanges && (
          <div className="grid grid-cols-3 gap-4 mb-4">
            <div className="text-center">
              <div className="text-2xl font-bold text-green-600">{added}</div>
              <div className="text-sm text-gray-600">Added</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-blue-600">{updated}</div>
              <div className="text-sm text-gray-600">Updated</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-red-600">{deleted}</div>
              <div className="text-sm text-gray-600">Deleted</div>
            </div>
          </div>
        )}

        {!hasChanges && !hasErrors && (
          <div className="text-center py-4">
            <CheckCircleIcon className="h-12 w-12 text-green-500 mx-auto mb-2" />
            <p className="text-gray-600">No changes detected. Your files are already synchronized.</p>
          </div>
        )}

        {hasErrors && (
          <div className="mt-4">
            <h5 className="font-medium text-red-700 mb-2 flex items-center">
              <ExclamationTriangleIcon className="h-4 w-4 mr-1" />
              Errors ({errors.length})
            </h5>
            <div className="bg-red-50 border border-red-200 rounded p-3">
              <ul className="text-sm text-red-700 space-y-1">
                {errors.map((error, index) => (
                  <li key={index} className="break-words">{error}</li>
                ))}
              </ul>
            </div>
          </div>
        )}
      </div>
    );
  };

  return (
    <div className="bg-white rounded-lg border border-gray-200 p-6">
      <div className="flex items-center justify-between mb-4">
        <div>
          <h3 className="text-lg font-medium text-gray-900">S3 Synchronization</h3>
          <p className="text-sm text-gray-600 mt-1">
            Keep your files synchronized between Drivn and your S3 storage
          </p>
        </div>
        <button
          onClick={startSync}
          disabled={isLoading || syncStatus.syncInProgress}
          className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
        >
          <ArrowPathIcon className={`h-4 w-4 mr-2 ${(isLoading || syncStatus.syncInProgress) ? 'animate-spin' : ''}`} />
          {isLoading || syncStatus.syncInProgress ? 'Syncing...' : 'Sync Now'}
        </button>
      </div>

      <div className="flex items-center justify-between py-3 px-4 bg-gray-50 rounded-lg">
        <div className="flex items-center">
          {getSyncStatusIcon()}
          <span className="ml-3 text-sm font-medium text-gray-900">
            {getSyncStatusText()}
          </span>
        </div>
        
        {lastSyncResult && (
          <button
            onClick={() => setShowDetails(!showDetails)}
            className="text-sm text-blue-600 hover:text-blue-800 font-medium"
          >
            {showDetails ? 'Hide Details' : 'Show Details'}
          </button>
        )}
      </div>

      {showDetails && renderSyncDetails()}

      {/* Sync Settings */}
      <div className="mt-4 p-4 bg-gray-50 border border-gray-200 rounded-lg">
        <div className="flex items-center justify-between">
          <div>
            <h4 className="font-medium text-gray-900">Automatic Sync</h4>
            <p className="text-sm text-gray-600 mt-1">
              Enable automatic background synchronization (when available)
            </p>
          </div>
          <button
            onClick={toggleSyncEnabled}
            className={`relative inline-flex h-6 w-11 flex-shrink-0 cursor-pointer rounded-full border-2 border-transparent transition-colors duration-200 ease-in-out focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 ${
              syncEnabled ? 'bg-blue-600' : 'bg-gray-200'
            }`}
          >
            <span
              className={`pointer-events-none inline-block h-5 w-5 transform rounded-full bg-white shadow ring-0 transition duration-200 ease-in-out ${
                syncEnabled ? 'translate-x-5' : 'translate-x-0'
              }`}
            />
          </button>
        </div>

        {lastSyncAt && (
          <div className="mt-3 text-sm text-gray-600">
            Last sync: {new Date(lastSyncAt).toLocaleString()}
          </div>
        )}
      </div>

      <div className="mt-4 p-4 bg-blue-50 border border-blue-200 rounded-lg">
        <div className="flex items-start">
          <InformationCircleIcon className="h-5 w-5 text-blue-500 mt-0.5 mr-3 flex-shrink-0" />
          <div className="text-sm text-blue-700">
            <p className="font-medium mb-1">How S3 Sync Works:</p>
            <ul className="space-y-1 text-xs">
              <li>• Compares files between your S3 bucket and Drivn database</li>
              <li>• Adds new files found in S3 to your Drivn library</li>
              <li>• Updates file metadata for changed files</li>
              <li>• Removes database entries for deleted S3 files</li>
              <li>• Preserves your folder organization and file permissions</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  );
}
