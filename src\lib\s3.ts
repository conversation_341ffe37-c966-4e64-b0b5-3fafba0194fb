import { S3Client, <PERSON>BucketsCommand, PutO<PERSON>Command, GetObjectCommand, DeleteObjectCommand } from '@aws-sdk/client-s3';
import { getSignedUrl } from '@aws-sdk/s3-request-presigner';
import { S3Credentials, decryptS3Credentials } from './encryption';
import { NextRequest } from 'next/server';
import { IFile } from '@/models/File';

export class S3Service {
  private client: S3Client;
  private bucket: string;

  constructor(credentials: S3Credentials) {
    this.client = new S3Client({
      endpoint: credentials.endpoint,
      region: credentials.region,
      credentials: {
        accessKeyId: credentials.accessKeyId,
        secretAccessKey: credentials.secretAccessKey,
      },
      forcePathStyle: true, // Required for some S3-compatible services
    });
    this.bucket = credentials.bucket || 'drivn-files';
  }

  static fromEncryptedCookie(encryptedCredentials: string): S3Service {
    const credentials = decryptS3Credentials(encryptedCredentials);
    return new S3Service(credentials);
  }

  static getPlatformS3(): S3Service {
    const requiredVars = {
      endpoint: process.env.PLATFORM_S3_ENDPOINT,
      region: process.env.PLATFORM_S3_REGION,
      accessKeyId: process.env.PLATFORM_S3_ACCESS_KEY_ID,
      secretAccessKey: process.env.PLATFORM_S3_SECRET_ACCESS_KEY,
      bucket: process.env.PLATFORM_S3_BUCKET,
    };

    for (const [key, value] of Object.entries(requiredVars)) {
      if (!value) {
        throw new Error(`Missing required platform S3 environment variable: PLATFORM_${key.toUpperCase()}`);
      }
    }

    const credentials: S3Credentials = {
      endpoint: requiredVars.endpoint,
      region: requiredVars.region,
      accessKeyId: requiredVars.accessKeyId,
      secretAccessKey: requiredVars.secretAccessKey,
      bucket: requiredVars.bucket,
    };
    return new S3Service(credentials);
  }

  async validateCredentials(): Promise<boolean> {
    try {
      await this.client.send(new ListBucketsCommand({}));
      return true;
    } catch (error) {
      console.error('S3 validation error:', error);
      return false;
    }
  }

  async getPresignedUploadUrl(key: string, contentType: string): Promise<string> {
    const command = new PutObjectCommand({
      Bucket: this.bucket,
      Key: key,
      ContentType: contentType,
    });

    return await getSignedUrl(this.client, command, { expiresIn: 3600 }); // 1 hour
  }

  async getPresignedDownloadUrl(key: string): Promise<string> {
    const command = new GetObjectCommand({
      Bucket: this.bucket,
      Key: key,
    });

    return await getSignedUrl(this.client, command, { expiresIn: 3600 }); // 1 hour
  }

  async getFileStream(key: string): Promise<ReadableStream> {
    const command = new GetObjectCommand({
      Bucket: this.bucket,
      Key: key,
    });

    const response = await this.client.send(command);

    if (!response.Body) {
      throw new Error('File not found or empty');
    }

    // Convert the S3 body to a ReadableStream
    if (response.Body instanceof ReadableStream) {
      return response.Body;
    }

    // Handle other body types (Uint8Array, etc.)
    const chunks: Uint8Array[] = [];
    const reader = response.Body as any;

    if (reader.transformToByteArray) {
      // AWS SDK v3 specific method
      const byteArray = await reader.transformToByteArray();
      return new ReadableStream({
        start(controller) {
          controller.enqueue(byteArray);
          controller.close();
        }
      });
    }

    // Fallback for other types
    return new ReadableStream({
      start(controller) {
        controller.enqueue(new Uint8Array(reader));
        controller.close();
      }
    });
  }

  async deleteFile(key: string): Promise<void> {
    const command = new DeleteObjectCommand({
      Bucket: this.bucket,
      Key: key,
    });

    await this.client.send(command);
  }

  generateFileKey(userId: string, fileName: string): string {
    const timestamp = Date.now();
    const sanitizedFileName = fileName.replace(/[^a-zA-Z0-9.-]/g, '_');
    return `users/${userId}/${timestamp}-${sanitizedFileName}`;
  }

  async uploadFile(key: string, body: Uint8Array, contentType: string): Promise<void> {
    const command = new PutObjectCommand({
      Bucket: this.bucket,
      Key: key,
      Body: body,
      ContentType: contentType,
    });

    await this.client.send(command);
  }

  getBucket(): string {
    return this.bucket;
  }

  /**
   * Get the appropriate S3 service instance based on request context and file storage source
   */
  static getS3ServiceForFile(
    request: NextRequest,
    file: IFile,
    session: { user: { allowPlatformS3: boolean } }
  ): S3Service {
    const s3Cookie = request.cookies.get('s3-credentials');

    // If file has explicit storage source, use that to determine credentials
    if (file.storageSource === 'user' && s3Cookie) {
      return S3Service.fromEncryptedCookie(s3Cookie.value);
    } else if (file.storageSource === 'platform') {
      return S3Service.getPlatformS3();
    }

    // Fallback to standard credential resolution for files without storage source
    if (s3Cookie) {
      return S3Service.fromEncryptedCookie(s3Cookie.value);
    } else if (session.user.allowPlatformS3) {
      return S3Service.getPlatformS3();
    }

    throw new Error('No S3 credentials available');
  }

  /**
   * Get the appropriate S3 service instance based on request context (for new uploads)
   */
  static getS3ServiceForRequest(
    request: NextRequest,
    session: { user: { allowPlatformS3: boolean } }
  ): { service: S3Service; storageSource: 'user' | 'platform' } {
    const s3Cookie = request.cookies.get('s3-credentials');

    if (s3Cookie) {
      return {
        service: S3Service.fromEncryptedCookie(s3Cookie.value),
        storageSource: 'user'
      };
    } else if (session.user.allowPlatformS3) {
      return {
        service: S3Service.getPlatformS3(),
        storageSource: 'platform'
      };
    }

    throw new Error('No S3 credentials available');
  }
}
