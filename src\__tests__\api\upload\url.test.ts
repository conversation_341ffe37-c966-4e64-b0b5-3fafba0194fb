import { describe, it, expect, beforeEach, afterEach, jest } from '@jest/globals';
import { POST } from '@/app/api/upload/url/route';
import { 
  createMockRequest, 
  mockAuth,
  mockS3Service,
  mockSession,
  setupTestEnvironment,
  cleanupMocks,
  expectAuthError,
  expectValidationError,
} from '@/__tests__/utils/test-helpers';

// Mock dependencies
jest.mock('@/lib/auth');
jest.mock('@/lib/s3');

const mockAuthFn = mockAuth();
const mockS3 = mockS3Service();

describe('/api/upload/url', () => {
  beforeEach(() => {
    setupTestEnvironment();
    cleanupMocks();
    
    // Setup default mocks
    require('@/lib/auth').auth = mockAuthFn;
    require('@/lib/s3').S3Service.fromEncryptedCookie = jest.fn().mockReturnValue(mockS3);
    require('@/lib/s3').S3Service.getPlatformS3 = jest.fn().mockReturnValue(mockS3);
  });

  afterEach(() => {
    cleanupMocks();
  });

  describe('POST', () => {
    const validUploadData = {
      fileName: 'test-file.txt',
      contentType: 'text/plain',
      fileSize: 1024,
    };

    it('should generate upload URL for authenticated user', async () => {
      mockAuthFn.mockResolvedValue(mockSession);
      mockS3.getPresignedUploadUrl.mockResolvedValue('https://example.com/upload-url');
      mockS3.generateFileKey.mockReturnValue('users/123/test-file.txt');

      const request = createMockRequest('http://localhost:3000/api/upload/url', {
        method: 'POST',
        body: validUploadData,
        cookies: { 's3-credentials': 'encrypted-credentials' },
      });

      const response = await POST(request);
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data.uploadUrl).toBe('https://example.com/upload-url');
      expect(data.fileKey).toBe('users/123/test-file.txt');
      expect(data.fileName).toBe(validUploadData.fileName);
      expect(data.contentType).toBe(validUploadData.contentType);
      expect(data.fileSize).toBe(validUploadData.fileSize);
    });

    it('should reject unauthenticated requests', async () => {
      mockAuthFn.mockResolvedValue(null);

      const request = createMockRequest('http://localhost:3000/api/upload/url', {
        method: 'POST',
        body: validUploadData,
      });

      const response = await POST(request);
      
      expectAuthError(response);
    });

    it('should validate file size limit', async () => {
      mockAuthFn.mockResolvedValue(mockSession);

      const invalidData = {
        ...validUploadData,
        fileSize: 200 * 1024 * 1024, // 200MB - exceeds limit
      };

      const request = createMockRequest('http://localhost:3000/api/upload/url', {
        method: 'POST',
        body: invalidData,
      });

      const response = await POST(request);
      const data = await response.json();

      expect(response.status).toBe(400);
      expect(data.error).toContain('File size exceeds maximum limit');
    });

    it('should validate file type', async () => {
      mockAuthFn.mockResolvedValue(mockSession);

      const invalidData = {
        ...validUploadData,
        contentType: 'application/x-executable',
      };

      const request = createMockRequest('http://localhost:3000/api/upload/url', {
        method: 'POST',
        body: invalidData,
      });

      const response = await POST(request);
      const data = await response.json();

      expect(response.status).toBe(400);
      expect(data.error).toContain('File type not allowed');
    });

    it('should validate filename', async () => {
      mockAuthFn.mockResolvedValue(mockSession);

      const invalidData = {
        ...validUploadData,
        fileName: '../../../etc/passwd',
      };

      const request = createMockRequest('http://localhost:3000/api/upload/url', {
        method: 'POST',
        body: invalidData,
      });

      const response = await POST(request);
      const data = await response.json();

      expect(response.status).toBe(400);
      expect(data.error).toContain('Invalid filename');
    });

    it('should sanitize filename', async () => {
      mockAuthFn.mockResolvedValue(mockSession);
      mockS3.getPresignedUploadUrl.mockResolvedValue('https://example.com/upload-url');
      mockS3.generateFileKey.mockReturnValue('users/123/sanitized-file.txt');

      const invalidData = {
        ...validUploadData,
        fileName: 'test<script>alert("xss")</script>file.txt',
      };

      const request = createMockRequest('http://localhost:3000/api/upload/url', {
        method: 'POST',
        body: invalidData,
        cookies: { 's3-credentials': 'encrypted-credentials' },
      });

      const response = await POST(request);
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(mockS3.generateFileKey).toHaveBeenCalledWith(
        mockSession.user.id,
        expect.not.stringContaining('<script>')
      );
    });

    it('should use platform S3 when user has no credentials but has permission', async () => {
      const sessionWithPlatformS3 = {
        ...mockSession,
        user: { ...mockSession.user, allowPlatformS3: true },
      };
      mockAuthFn.mockResolvedValue(sessionWithPlatformS3);
      mockS3.getPresignedUploadUrl.mockResolvedValue('https://platform-s3.com/upload-url');

      const request = createMockRequest('http://localhost:3000/api/upload/url', {
        method: 'POST',
        body: validUploadData,
        // No S3 credentials cookie
      });

      const response = await POST(request);
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data.uploadUrl).toBe('https://platform-s3.com/upload-url');
      expect(require('@/lib/s3').S3Service.getPlatformS3).toHaveBeenCalled();
    });

    it('should reject when no S3 credentials and no platform permission', async () => {
      mockAuthFn.mockResolvedValue(mockSession);

      const request = createMockRequest('http://localhost:3000/api/upload/url', {
        method: 'POST',
        body: validUploadData,
      });

      const response = await POST(request);
      const data = await response.json();

      expect(response.status).toBe(400);
      expect(data.error).toContain('No S3 credentials configured');
    });

    it('should handle invalid S3 credentials', async () => {
      mockAuthFn.mockResolvedValue(mockSession);
      require('@/lib/s3').S3Service.fromEncryptedCookie.mockImplementation(() => {
        throw new Error('Invalid credentials');
      });

      const request = createMockRequest('http://localhost:3000/api/upload/url', {
        method: 'POST',
        body: validUploadData,
        cookies: { 's3-credentials': 'invalid-credentials' },
      });

      const response = await POST(request);
      const data = await response.json();

      expect(response.status).toBe(400);
      expect(data.error).toContain('Invalid S3 credentials');
    });

    it('should validate required fields', async () => {
      mockAuthFn.mockResolvedValue(mockSession);

      const invalidData = {
        fileName: '',
        contentType: 'text/plain',
        fileSize: 1024,
      };

      const request = createMockRequest('http://localhost:3000/api/upload/url', {
        method: 'POST',
        body: invalidData,
      });

      const response = await POST(request);
      
      expectValidationError(response);
    });

    it('should handle S3 service errors', async () => {
      mockAuthFn.mockResolvedValue(mockSession);
      mockS3.getPresignedUploadUrl.mockRejectedValue(new Error('S3 service error'));

      const request = createMockRequest('http://localhost:3000/api/upload/url', {
        method: 'POST',
        body: validUploadData,
        cookies: { 's3-credentials': 'encrypted-credentials' },
      });

      const response = await POST(request);
      const data = await response.json();

      expect(response.status).toBe(500);
      expect(data.error).toContain('Failed to generate upload URL');
    });
  });
});
