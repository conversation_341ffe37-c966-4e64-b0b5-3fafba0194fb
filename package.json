{"name": "drivn", "version": "3.1.1", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint", "lint:fix": "next lint --fix", "type-check": "tsc --noEmit", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:ci": "jest --ci --coverage --watchAll=false", "test:feature": "node scripts/test-features.js", "test:auth": "node scripts/test-features.js auth", "test:files": "node scripts/test-features.js files", "test:admin": "node scripts/test-features.js admin", "test:all-features": "node scripts/test-features.js all", "dev:cli": "node scripts/dev-cli.js", "dev:init": "node scripts/dev-cli.js init-feature", "dev:generate": "node scripts/dev-cli.js generate", "dev:health": "node scripts/dev-cli.js health-check", "dev:server": "node scripts/dev-cli.js dev-server", "dev:dashboard": "node scripts/dev-cli.js dashboard"}, "dependencies": {"dotenv": "^16.4.1", "@aws-sdk/client-s3": "^3.850.0", "@aws-sdk/s3-request-presigner": "^3.850.0", "@heroicons/react": "^2.2.0", "@types/bcryptjs": "^2.4.6", "@types/nodemailer": "^6.4.17", "bcryptjs": "^3.0.2", "clsx": "^2.1.1", "mongoose": "^8.16.4", "next": "15.4.3", "next-auth": "^5.0.0-beta.29", "nodemailer": "^6.10.1", "react": "19.1.0", "react-dom": "19.1.0", "react-hot-toast": "^2.5.2", "zod": "^4.0.8"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.4.3", "tailwindcss": "^4", "typescript": "^5"}, "packageManager": "pnpm@10.12.4"}