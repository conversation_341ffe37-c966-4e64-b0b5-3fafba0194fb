import Link from 'next/link';
import { Button } from '@/components/ui/Button';
import { ThemeToggle } from '@/components/ui/ThemeToggle';
import {
  CloudArrowUpIcon,
  ShareIcon,
  ShieldCheckIcon,
  BoltIcon,
  GlobeAltIcon,
  UserGroupIcon,
} from '@heroicons/react/24/outline';

const features = [
  {
    name: 'Secure Storage',
    description: 'Your files are encrypted and stored securely with enterprise-grade security.',
    icon: ShieldCheckIcon,
  },
  {
    name: 'Fast Upload',
    description: 'Lightning-fast file uploads with our optimized infrastructure.',
    icon: BoltIcon,
  },
  {
    name: 'Easy Sharing',
    description: 'Share files with anyone using secure, customizable links.',
    icon: ShareIcon,
  },
  {
    name: 'Global Access',
    description: 'Access your files from anywhere in the world, anytime.',
    icon: GlobeAltIcon,
  },
  {
    name: 'Team Collaboration',
    description: 'Work together with your team on shared files and folders.',
    icon: UserGroupIcon,
  },
  {
    name: 'Your Storage',
    description: 'Use your own S3-compatible storage provider for complete control.',
    icon: CloudArrowUpIcon,
  },
];

export default function Home() {
  const currentYear = new Date().getFullYear();

  return (
    <div className="min-h-screen bg-background">
      {/* Header */}
      <header className="border-b border-divider">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-6">
            <div className="flex items-center space-x-2">
              <div className="w-8 h-8 bg-accent rounded-lg flex items-center justify-center">
                <span className="text-white font-bold text-lg">D</span>
              </div>
              <span className="text-2xl font-bold text-text-primary">Drivn</span>
            </div>
            <div className="flex items-center space-x-4">
              <ThemeToggle />
              <Link href="/login">
                <Button variant="ghost">Sign In</Button>
              </Link>
              <Link href="/signup">
                <Button>Get Started</Button>
              </Link>
            </div>
          </div>
        </div>
      </header>

      {/* Hero Section */}
      <section className="py-20 px-4 sm:px-6 lg:px-8">
        <div className="max-w-7xl mx-auto text-center">
          <h1 className="text-4xl sm:text-6xl font-bold text-text-primary mb-6">
            Your Files,{' '}
            <span className="text-accent">Your Storage</span>,{' '}
            <span className="text-accent">Your Control</span>
          </h1>
          <p className="text-xl text-text-secondary mb-8 max-w-3xl mx-auto">
            Drivn is a secure cloud storage platform that lets you use your own S3-compatible
            storage provider while providing a beautiful, fast interface for managing your files.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Link href="/signup">
              <Button size="lg" className="w-full sm:w-auto">
                Start Free Today
              </Button>
            </Link>
            <Link href="/login">
              <Button variant="secondary" size="lg" className="w-full sm:w-auto">
                Sign In
              </Button>
            </Link>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="py-20 bg-secondary/50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl font-bold text-text-primary mb-4">
              Everything you need for file storage
            </h2>
            <p className="text-lg text-text-secondary max-w-2xl mx-auto">
              Powerful features designed to make file management simple, secure, and efficient.
            </p>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {features.map((feature) => (
              <div key={feature.name} className="bg-background p-6 rounded-xl border border-divider">
                <div className="w-12 h-12 bg-accent/10 rounded-lg flex items-center justify-center mb-4">
                  <feature.icon className="h-6 w-6 text-accent" />
                </div>
                <h3 className="text-lg font-semibold text-text-primary mb-2">
                  {feature.name}
                </h3>
                <p className="text-text-secondary">
                  {feature.description}
                </p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20">
        <div className="max-w-4xl mx-auto text-center px-4 sm:px-6 lg:px-8">
          <h2 className="text-3xl font-bold text-text-primary mb-4">
            Ready to take control of your files?
          </h2>
          <p className="text-lg text-text-secondary mb-8">
            Join thousands of users who trust Drivn for their file storage needs.
          </p>
          <Link href="/signup">
            <Button size="lg">
              Get Started Now
            </Button>
          </Link>
        </div>
      </section>

      {/* Footer */}
      <footer className="border-t border-divider py-12">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex flex-col md:flex-row justify-between items-center">
            <div className="flex items-center space-x-2 mb-4 md:mb-0">
              <div className="w-6 h-6 bg-accent rounded flex items-center justify-center">
                <span className="text-white font-bold text-sm">D</span>
              </div>
              <span className="text-lg font-bold text-text-primary">Drivn</span>
            </div>
            <div className="text-sm text-text-secondary">
              {currentYear} Drivn. MIT licence.
            </div>
          </div>
        </div>
      </footer>
    </div>
  );
}
