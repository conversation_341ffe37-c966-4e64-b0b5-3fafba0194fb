'use client';

import React from 'react';
import { Modal } from '@/components/ui/Modal';
import { Button } from '@/components/ui/Button';
import { toast } from '@/components/ui/Toast';
import { 
  ArrowDownTrayIcon, 
  ShareIcon, 
  DocumentIcon,
  PhotoIcon,
  VideoCameraIcon,
  MusicalNoteIcon,
  ExclamationTriangleIcon
} from '@heroicons/react/24/outline';
import { formatFileSize } from '@/lib/utils';

interface FilePreviewModalProps {
  isOpen: boolean;
  onClose: () => void;
  file: {
    id: string;
    name: string;
    size: number;
    mimeType: string;
    createdAt: string;
  };
  onDownload?: () => void;
  onShare?: () => void;
}

interface PreviewContentProps {
  file: FilePreviewModalProps['file'];
}

const PreviewContent: React.FC<PreviewContentProps> = ({ file }) => {
  const [loading, setLoading] = React.useState(true);
  const [error, setError] = React.useState<string | null>(null);
  const [previewUrl, setPreviewUrl] = React.useState<string | null>(null);

  React.useEffect(() => {
    const loadPreview = async () => {
      setLoading(true);
      setError(null);

      try {
        // Get preview URL from the API
        const response = await fetch(`/api/files/${file.id}/preview`);
        if (!response.ok) {
          throw new Error('Failed to load preview');
        }

        const blob = await response.blob();
        const url = URL.createObjectURL(blob);
        setPreviewUrl(url);
      } catch (err) {
        console.error('Preview error:', err);
        setError('Failed to load preview');
      } finally {
        setLoading(false);
      }
    };

    if (isPreviewable(file.mimeType)) {
      loadPreview();
    } else {
      setLoading(false);
    }

    return () => {
      if (previewUrl) {
        URL.revokeObjectURL(previewUrl);
      }
    };
  }, [file.id, file.mimeType]);

  const isPreviewable = (mimeType: string): boolean => {
    return (
      mimeType.startsWith('image/') ||
      mimeType.startsWith('video/') ||
      mimeType.startsWith('audio/') ||
      mimeType === 'application/pdf' ||
      mimeType.startsWith('text/')
    );
  };

  const getFileTypeIcon = (mimeType: string) => {
    if (mimeType.startsWith('image/')) return PhotoIcon;
    if (mimeType.startsWith('video/')) return VideoCameraIcon;
    if (mimeType.startsWith('audio/')) return MusicalNoteIcon;
    return DocumentIcon;
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center py-12">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-accent"></div>
        <span className="ml-3 text-text-secondary">Loading preview...</span>
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex flex-col items-center justify-center py-12">
        <ExclamationTriangleIcon className="h-12 w-12 text-error mb-4" />
        <p className="text-text-primary font-medium mb-2">Preview Error</p>
        <p className="text-text-secondary text-sm">{error}</p>
      </div>
    );
  }

  if (!isPreviewable(file.mimeType)) {
    const Icon = getFileTypeIcon(file.mimeType);
    return (
      <div className="flex flex-col items-center justify-center py-12">
        <Icon className="h-16 w-16 text-text-secondary mb-4" />
        <p className="text-text-primary font-medium mb-2">Preview Not Available</p>
        <p className="text-text-secondary text-sm text-center">
          This file type cannot be previewed in the browser.
          <br />
          Download the file to view its contents.
        </p>
      </div>
    );
  }

  // Render preview based on file type
  if (file.mimeType.startsWith('image/')) {
    return (
      <div className="flex justify-center">
        <img
          src={previewUrl || ''}
          alt={file.name}
          className="max-w-full max-h-96 object-contain rounded-lg"
          onError={() => setError('Failed to load image')}
        />
      </div>
    );
  }

  if (file.mimeType.startsWith('video/')) {
    return (
      <div className="flex justify-center">
        <video
          src={previewUrl || ''}
          controls
          className="max-w-full max-h-96 rounded-lg"
          onError={() => setError('Failed to load video')}
        >
          Your browser does not support video playback.
        </video>
      </div>
    );
  }

  if (file.mimeType.startsWith('audio/')) {
    return (
      <div className="flex flex-col items-center py-8">
        <MusicalNoteIcon className="h-16 w-16 text-accent mb-4" />
        <audio
          src={previewUrl || ''}
          controls
          className="w-full max-w-md"
          onError={() => setError('Failed to load audio')}
        >
          Your browser does not support audio playback.
        </audio>
      </div>
    );
  }

  if (file.mimeType === 'application/pdf') {
    return (
      <div className="w-full h-96">
        <iframe
          src={previewUrl || ''}
          className="w-full h-full border-0 rounded-lg"
          title={file.name}
          onError={() => setError('Failed to load PDF')}
        />
      </div>
    );
  }

  if (file.mimeType.startsWith('text/')) {
    return (
      <div className="bg-secondary rounded-lg p-4 max-h-96 overflow-auto">
        <iframe
          src={previewUrl || ''}
          className="w-full h-80 border-0"
          title={file.name}
          onError={() => setError('Failed to load text file')}
        />
      </div>
    );
  }

  return (
    <div className="flex flex-col items-center justify-center py-12">
      <DocumentIcon className="h-16 w-16 text-text-secondary mb-4" />
      <p className="text-text-primary font-medium mb-2">Preview Not Available</p>
      <p className="text-text-secondary text-sm">
        This file type is not supported for preview.
      </p>
    </div>
  );
};

export const FilePreviewModal: React.FC<FilePreviewModalProps> = ({
  isOpen,
  onClose,
  file,
  onDownload,
  onShare,
}) => {
  return (
    <Modal
      isOpen={isOpen}
      onClose={onClose}
      title={file.name}
      size="xl"
    >
      <div className="space-y-6">
        {/* File Info */}
        <div className="flex items-center justify-between p-4 bg-secondary rounded-lg">
          <div className="flex items-center space-x-3">
            <div className="text-2xl">
              {file.mimeType.startsWith('image/') && '🖼️'}
              {file.mimeType.startsWith('video/') && '🎥'}
              {file.mimeType.startsWith('audio/') && '🎵'}
              {file.mimeType === 'application/pdf' && '📄'}
              {file.mimeType.startsWith('text/') && '📝'}
              {!['image/', 'video/', 'audio/', 'application/pdf', 'text/'].some(type => 
                file.mimeType.startsWith(type) || file.mimeType === type
              ) && '📁'}
            </div>
            <div>
              <p className="font-medium text-text-primary">{file.name}</p>
              <p className="text-sm text-text-secondary">
                {formatFileSize(file.size)} • {new Date(file.createdAt).toLocaleDateString()}
              </p>
            </div>
          </div>
          
          <div className="flex space-x-2">
            {onShare && (
              <Button
                variant="secondary"
                size="sm"
                onClick={onShare}
              >
                <ShareIcon className="h-4 w-4 mr-2" />
                Share
              </Button>
            )}
            {onDownload && (
              <Button
                size="sm"
                onClick={onDownload}
              >
                <ArrowDownTrayIcon className="h-4 w-4 mr-2" />
                Download
              </Button>
            )}
          </div>
        </div>

        {/* Preview Content */}
        <PreviewContent file={file} />
      </div>
    </Modal>
  );
};
