import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@/lib/auth';
import { File } from '@/models/File';
import { Folder } from '@/models/Folder';
import connectDB from '@/lib/mongodb';

export async function GET(request: NextRequest) {
  try {
    const session = await auth();
    if (!session) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const { searchParams } = new URL(request.url);
    const folderId = searchParams.get('folderId');
    const search = searchParams.get('search');
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '20');
    const sortBy = searchParams.get('sortBy') || 'createdAt';
    const sortOrder = searchParams.get('sortOrder') || 'desc';

    await connectDB();

    // Build query
    const query: any = { userId: session.user.id };

    if (folderId) {
      // Validate folder ownership
      if (folderId !== 'root') {
        const folder = await Folder.findOne({
          _id: folderId,
          userId: session.user.id,
        });

        if (!folder) {
          return NextResponse.json(
            { error: 'Folder not found or access denied' },
            { status: 404 }
          );
        }

        query.folderId = folderId;
      } else {
        query.folderId = { $exists: false };
      }
    }

    if (search) {
      query.$or = [
        { name: { $regex: search, $options: 'i' } },
        { originalName: { $regex: search, $options: 'i' } },
        { description: { $regex: search, $options: 'i' } },
        { tags: { $in: [new RegExp(search, 'i')] } },
      ];
    }

    // Calculate pagination
    const skip = (page - 1) * limit;

    // Build sort object
    const sort: any = {};
    sort[sortBy] = sortOrder === 'asc' ? 1 : -1;

    // Get files
    const files = await File.find(query)
      .sort(sort)
      .skip(skip)
      .limit(limit);

    // Get total count
    const totalCount = await File.countDocuments(query);

    // Get folder info if folderId is provided
    let folderInfo = null;
    if (folderId && folderId !== 'root') {
      const folder = await Folder.findById(folderId);
      if (folder) {
        folderInfo = {
          id: folder._id,
          name: folder.name,
          path: folder.path,
          parentFolderId: folder.parentFolderId,
        };
      }
    }

    return NextResponse.json({
      files: files.map(file => ({
        id: file._id,
        name: file.name,
        originalName: file.originalName,
        size: file.size,
        mimeType: file.mimeType,
        isPublic: file.isPublic,
        downloadCount: file.downloadCount,
        createdAt: file.createdAt,
        updatedAt: file.updatedAt,
        lastAccessedAt: file.lastAccessedAt,
        tags: file.tags,
        description: file.description,
        folderId: file.folderId,
      })),
      pagination: {
        page,
        limit,
        totalCount,
        totalPages: Math.ceil(totalCount / limit),
        hasNext: page * limit < totalCount,
        hasPrev: page > 1,
      },
      folder: folderInfo,
    });

  } catch (error) {
    console.error('Files fetch error:', error);
    return NextResponse.json(
      { error: 'Failed to fetch files' },
      { status: 500 }
    );
  }
}
