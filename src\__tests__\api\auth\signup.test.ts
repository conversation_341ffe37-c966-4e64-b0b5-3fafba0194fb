import { describe, it, expect, beforeEach, afterEach, jest } from '@jest/globals';
import { POST } from '@/app/api/auth/signup/route';
import { 
  createMockRequest, 
  mockMongoose, 
  mockEmailService,
  setupTestEnvironment,
  cleanupMocks,
  expectValidationError,
} from '@/__tests__/utils/test-helpers';

// Mock dependencies
jest.mock('@/models/User');
jest.mock('@/lib/mongodb');
jest.mock('@/lib/email');
jest.mock('bcryptjs');

const mockUser = mockMongoose();
const mockEmail = mockEmailService();

describe('/api/auth/signup', () => {
  beforeEach(() => {
    setupTestEnvironment();
    cleanupMocks();
  });

  afterEach(() => {
    cleanupMocks();
  });

  describe('POST', () => {
    const validSignupData = {
      name: 'Test User',
      email: '<EMAIL>',
      password: 'SecurePassword123!',
    };

    it('should create a new user successfully', async () => {
      mockUser.findOne.mockResolvedValue(null);
      
      const newUser = {
        _id: '507f1f77bcf86cd799439011',
        ...validSignupData,
        verified: false,
      };
      mockUser.create.mockResolvedValue(newUser);

      const request = createMockRequest('http://localhost:3000/api/auth/signup', {
        method: 'POST',
        body: validSignupData,
      });

      const response = await POST(request);
      const data = await response.json();

      expect(response.status).toBe(201);
      expect(data.message).toContain('User created successfully');
      expect(data.userId).toBe(newUser._id);
      expect(mockUser.create).toHaveBeenCalledWith(
        expect.objectContaining({
          name: validSignupData.name,
          email: validSignupData.email,
          verified: false,
        })
      );
    });

    it('should return error if user already exists', async () => {
      mockUser.findOne.mockResolvedValue({ email: validSignupData.email });

      const request = createMockRequest('http://localhost:3000/api/auth/signup', {
        method: 'POST',
        body: validSignupData,
      });

      const response = await POST(request);
      const data = await response.json();

      expect(response.status).toBe(400);
      expect(data.error).toContain('already exists');
    });

    it('should validate required fields', async () => {
      const invalidData = {
        name: '',
        email: 'invalid-email',
        password: '123',
      };

      const request = createMockRequest('http://localhost:3000/api/auth/signup', {
        method: 'POST',
        body: invalidData,
      });

      const response = await POST(request);
      
      expectValidationError(response);
    });

    it('should validate email format', async () => {
      const invalidData = {
        ...validSignupData,
        email: 'not-an-email',
      };

      const request = createMockRequest('http://localhost:3000/api/auth/signup', {
        method: 'POST',
        body: invalidData,
      });

      const response = await POST(request);
      
      expectValidationError(response);
    });

    it('should validate password length', async () => {
      const invalidData = {
        ...validSignupData,
        password: '123',
      };

      const request = createMockRequest('http://localhost:3000/api/auth/signup', {
        method: 'POST',
        body: invalidData,
      });

      const response = await POST(request);
      
      expectValidationError(response);
    });

    it('should validate name length', async () => {
      const invalidData = {
        ...validSignupData,
        name: 'A',
      };

      const request = createMockRequest('http://localhost:3000/api/auth/signup', {
        method: 'POST',
        body: invalidData,
      });

      const response = await POST(request);
      
      expectValidationError(response);
    });

    it('should handle database errors gracefully', async () => {
      mockUser.findOne.mockRejectedValue(new Error('Database error'));

      const request = createMockRequest('http://localhost:3000/api/auth/signup', {
        method: 'POST',
        body: validSignupData,
      });

      const response = await POST(request);
      const data = await response.json();

      expect(response.status).toBe(500);
      expect(data.error).toContain('Internal server error');
    });

    it('should continue if email sending fails', async () => {
      mockUser.findOne.mockResolvedValue(null);
      const newUser = {
        _id: '507f1f77bcf86cd799439011',
        ...validSignupData,
        verified: false,
      };
      mockUser.create.mockResolvedValue(newUser);
      
      mockEmail.sendVerificationEmail.mockRejectedValue(new Error('Email error'));

      const request = createMockRequest('http://localhost:3000/api/auth/signup', {
        method: 'POST',
        body: validSignupData,
      });

      const response = await POST(request);
      const data = await response.json();

      expect(response.status).toBe(201);
      expect(data.message).toContain('User created successfully');
    });

    it('should hash password before storing', async () => {
      const bcrypt = require('bcryptjs');
      bcrypt.hash = jest.fn().mockResolvedValue('hashed-password');
      
      mockUser.findOne.mockResolvedValue(null);
      mockUser.create.mockResolvedValue({
        _id: '507f1f77bcf86cd799439011',
        ...validSignupData,
      });

      const request = createMockRequest('http://localhost:3000/api/auth/signup', {
        method: 'POST',
        body: validSignupData,
      });

      await POST(request);

      expect(bcrypt.hash).toHaveBeenCalledWith(validSignupData.password, 12);
      expect(mockUser.create).toHaveBeenCalledWith(
        expect.objectContaining({
          passwordHash: 'hashed-password',
        })
      );
    });

    it('should generate verification token', async () => {
      mockUser.findOne.mockResolvedValue(null);
      mockUser.create.mockResolvedValue({
        _id: '507f1f77bcf86cd799439011',
        ...validSignupData,
      });

      const request = createMockRequest('http://localhost:3000/api/auth/signup', {
        method: 'POST',
        body: validSignupData,
      });

      await POST(request);

      expect(mockUser.create).toHaveBeenCalledWith(
        expect.objectContaining({
          emailVerificationToken: expect.any(String),
          emailVerificationExpires: expect.any(Date),
        })
      );
    });
  });
});
