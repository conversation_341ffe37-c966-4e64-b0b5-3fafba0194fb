/**
 * Standalone Admin Feature Tests
 * 
 * This test suite tests the admin functionality in complete isolation,
 * including user management, system monitoring, and administrative controls.
 * Can be run independently for focused development and debugging.
 */

import { describe, it, expect, beforeEach, afterEach, jest } from '@jest/globals';
import { 
  DevTestEnvironment, 
  MockFactory, 
  FeatureTestUtils, 
  TestAssertions,
  TestDataGenerator,
  mockAdminUser,
  mockTestUser,
  mockAdminSession,
  mockTestSession
} from '../utils/dev-test-helpers';

// Mock all external dependencies
jest.mock('@/lib/mongodb');
jest.mock('@/models/User');
jest.mock('@/models/File');
jest.mock('@/lib/auth');

describe('Admin Feature - Standalone Tests', () => {
  let mockUser: any;
  let mockFile: any;
  let mockAuth: any;

  beforeEach(() => {
    // Setup isolated test environment
    DevTestEnvironment.setup({
      developmentMode: true,
      authBypass: false,
      detailedErrors: true,
    });

    // Create fresh mocks for each test
    mockUser = MockFactory.createDatabaseMocks();
    mockFile = MockFactory.createDatabaseMocks();
    mockAuth = MockFactory.createAuthMocks({
      defaultSession: mockAdminSession,
      developmentMode: true,
    });

    // Apply mocks
    require('@/models/User').User = mockUser;
    require('@/models/File').File = mockFile;
    require('@/lib/auth').auth = mockAuth.auth;
  });

  afterEach(() => {
    DevTestEnvironment.cleanup();
    jest.clearAllMocks();
  });

  describe('User Management', () => {
    it('should list all users for admin', async () => {
      await FeatureTestUtils.testAdminFeature(async () => {
        const testUsers = [
          TestDataGenerator.generateUser({ id: '1', name: 'User 1' }),
          TestDataGenerator.generateUser({ id: '2', name: 'User 2' }),
        ];

        mockAuth.auth.mockResolvedValue(mockAdminSession);
        mockUser.find.mockResolvedValue(testUsers);
        mockUser.countDocuments.mockResolvedValue(2);

        const { GET } = await import('@/app/api/admin/users/route');
        
        const request = MockFactory.createRequest('http://localhost:3000/api/admin/users');

        const response = await GET(request);
        const data = await response.json();

        TestAssertions.expectSuccessResponse(response);
        expect(data.users).toHaveLength(2);
        expect(data.total).toBe(2);
        expect(mockUser.find).toHaveBeenCalled();
      });
    });

    it('should deny access to non-admin users', async () => {
      await FeatureTestUtils.testAdminFeature(async () => {
        mockAuth.auth.mockResolvedValue(mockTestSession); // Regular user

        const { GET } = await import('@/app/api/admin/users/route');
        
        const request = MockFactory.createRequest('http://localhost:3000/api/admin/users');

        const response = await GET(request);
        
        TestAssertions.expectForbiddenError(response);
        expect(mockUser.find).not.toHaveBeenCalled();
      });
    });

    it('should update user properties', async () => {
      await FeatureTestUtils.testAdminFeature(async () => {
        const targetUser = TestDataGenerator.generateUser({
          id: 'target-user-id',
          verified: false,
          allowPlatformS3: false,
        });

        mockAuth.auth.mockResolvedValue(mockAdminSession);
        mockUser.findById.mockResolvedValue(targetUser);
        mockUser.findByIdAndUpdate.mockResolvedValue({
          ...targetUser,
          verified: true,
          allowPlatformS3: true,
        });

        const { PUT } = await import('@/app/api/admin/users/route');
        
        const request = MockFactory.createRequest('http://localhost:3000/api/admin/users', {
          method: 'PUT',
          body: {
            userId: 'target-user-id',
            updates: {
              verified: true,
              allowPlatformS3: true,
            },
          },
        });

        const response = await PUT(request);
        const data = await response.json();

        TestAssertions.expectSuccessResponse(response);
        expect(data.user.verified).toBe(true);
        expect(data.user.allowPlatformS3).toBe(true);
        expect(mockUser.findByIdAndUpdate).toHaveBeenCalledWith(
          'target-user-id',
          expect.objectContaining({
            verified: true,
            allowPlatformS3: true,
          }),
          { new: true }
        );
      });
    });

    it('should delete user and associated files', async () => {
      await FeatureTestUtils.testAdminFeature(async () => {
        const targetUser = TestDataGenerator.generateUser({
          id: 'target-user-id',
        });
        const userFiles = [
          TestDataGenerator.generateFile({ userId: 'target-user-id' }),
          TestDataGenerator.generateFile({ userId: 'target-user-id' }),
        ];

        mockAuth.auth.mockResolvedValue(mockAdminSession);
        mockUser.findById.mockResolvedValue(targetUser);
        mockFile.find.mockResolvedValue(userFiles);
        mockFile.deleteMany.mockResolvedValue({ deletedCount: 2 });
        mockUser.findByIdAndDelete.mockResolvedValue(targetUser);

        const { DELETE } = await import('@/app/api/admin/users/[id]/route');
        
        const request = MockFactory.createRequest(
          'http://localhost:3000/api/admin/users/target-user-id',
          { method: 'DELETE' }
        );

        const response = await DELETE(request, { params: { id: 'target-user-id' } });
        
        TestAssertions.expectSuccessResponse(response);
        expect(mockFile.deleteMany).toHaveBeenCalledWith({ userId: 'target-user-id' });
        expect(mockUser.findByIdAndDelete).toHaveBeenCalledWith('target-user-id');
      });
    });

    it('should prevent admin from deleting themselves', async () => {
      await FeatureTestUtils.testAdminFeature(async () => {
        mockAuth.auth.mockResolvedValue(mockAdminSession);

        const { DELETE } = await import('@/app/api/admin/users/[id]/route');
        
        const request = MockFactory.createRequest(
          `http://localhost:3000/api/admin/users/${mockAdminUser.id}`,
          { method: 'DELETE' }
        );

        const response = await DELETE(request, { params: { id: mockAdminUser.id } });
        
        TestAssertions.expectValidationError(response);
        expect(mockUser.findByIdAndDelete).not.toHaveBeenCalled();
      });
    });
  });

  describe('System Statistics', () => {
    it('should provide system overview statistics', async () => {
      await FeatureTestUtils.testAdminFeature(async () => {
        mockAuth.auth.mockResolvedValue(mockAdminSession);
        
        // Mock statistics data
        mockUser.countDocuments.mockImplementation((query) => {
          if (query?.verified === true) return Promise.resolve(85);
          if (query?.verified === false) return Promise.resolve(15);
          return Promise.resolve(100); // Total users
        });
        
        mockFile.countDocuments.mockResolvedValue(500);
        mockFile.aggregate.mockResolvedValue([{ _id: null, totalSize: 1024 * 1024 * 1024 }]); // 1GB

        const { GET } = await import('@/app/api/admin/stats/route');
        
        const request = MockFactory.createRequest('http://localhost:3000/api/admin/stats');

        const response = await GET(request);
        const data = await response.json();

        TestAssertions.expectSuccessResponse(response);
        expect(data.users.total).toBe(100);
        expect(data.users.verified).toBe(85);
        expect(data.users.unverified).toBe(15);
        expect(data.files.total).toBe(500);
        expect(data.storage.totalSize).toBe(1024 * 1024 * 1024);
      });
    });

    it('should provide user activity statistics', async () => {
      await FeatureTestUtils.testAdminFeature(async () => {
        mockAuth.auth.mockResolvedValue(mockAdminSession);
        
        const recentUsers = [
          TestDataGenerator.generateUser({ 
            createdAt: new Date(Date.now() - 24 * 60 * 60 * 1000) // 1 day ago
          }),
          TestDataGenerator.generateUser({ 
            createdAt: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000) // 1 week ago
          }),
        ];

        mockUser.find.mockResolvedValue(recentUsers);

        const { GET } = await import('@/app/api/admin/stats/users/route');
        
        const request = MockFactory.createRequest('http://localhost:3000/api/admin/stats/users');

        const response = await GET(request);
        const data = await response.json();

        TestAssertions.expectSuccessResponse(response);
        expect(data.recentUsers).toHaveLength(2);
        expect(mockUser.find).toHaveBeenCalledWith(
          expect.objectContaining({
            createdAt: expect.any(Object), // Date range query
          })
        );
      });
    });
  });

  describe('System Configuration', () => {
    it('should allow admin to update system settings', async () => {
      await FeatureTestUtils.testAdminFeature(async () => {
        mockAuth.auth.mockResolvedValue(mockAdminSession);

        const { PUT } = await import('@/app/api/admin/settings/route');
        
        const request = MockFactory.createRequest('http://localhost:3000/api/admin/settings', {
          method: 'PUT',
          body: {
            maxFileSize: 100 * 1024 * 1024, // 100MB
            allowedFileTypes: ['pdf', 'doc', 'docx', 'txt'],
            requireEmailVerification: true,
          },
        });

        const response = await PUT(request);
        const data = await response.json();

        TestAssertions.expectSuccessResponse(response);
        expect(data.settings.maxFileSize).toBe(100 * 1024 * 1024);
        expect(data.settings.allowedFileTypes).toContain('pdf');
      });
    });

    it('should validate system setting values', async () => {
      await FeatureTestUtils.testAdminFeature(async () => {
        mockAuth.auth.mockResolvedValue(mockAdminSession);

        const { PUT } = await import('@/app/api/admin/settings/route');
        
        const request = MockFactory.createRequest('http://localhost:3000/api/admin/settings', {
          method: 'PUT',
          body: {
            maxFileSize: -1, // Invalid negative value
            allowedFileTypes: [], // Empty array not allowed
          },
        });

        const response = await PUT(request);
        
        await TestAssertions.expectErrorWithContext(response, 'VALIDATION_ERROR');
      });
    });
  });

  describe('Development Mode Admin Features', () => {
    it('should provide enhanced debugging information in development', async () => {
      await FeatureTestUtils.testAdminFeature(async () => {
        DevTestEnvironment.setup({
          developmentMode: true,
          authBypass: true, // Use dev bypass
          detailedErrors: true,
          debugLogging: true,
        });

        const { GET } = await import('@/app/api/admin/debug/route');
        
        const request = MockFactory.createRequest('http://localhost:3000/api/admin/debug');

        const response = await GET(request);
        const data = await response.json();

        TestAssertions.expectSuccessResponse(response);
        expect(data.environment).toBe('development');
        expect(data.debugInfo).toBeDefined();
        expect(data.systemHealth).toBeDefined();
      });
    });

    it('should allow admin to test system components', async () => {
      await FeatureTestUtils.testAdminFeature(async () => {
        DevTestEnvironment.setup({
          developmentMode: true,
          authBypass: true,
        });

        const { POST } = await import('@/app/api/admin/test/route');
        
        const request = MockFactory.createRequest('http://localhost:3000/api/admin/test', {
          method: 'POST',
          body: {
            component: 'database',
            action: 'connection-test',
          },
        });

        const response = await POST(request);
        const data = await response.json();

        TestAssertions.expectSuccessResponse(response);
        expect(data.test).toBe('database');
        expect(data.result).toBeDefined();
      });
    });

    it('should hide development features in production', async () => {
      // Test that development admin features are not available in production
      DevTestEnvironment.setup({
        developmentMode: false, // Production mode
      });

      mockAuth.auth.mockResolvedValue(mockAdminSession);

      const { GET } = await import('@/app/api/admin/debug/route');
      
      const request = MockFactory.createRequest('http://localhost:3000/api/admin/debug');

      const response = await GET(request);
      
      TestAssertions.expectNotFoundError(response);
    });
  });

  describe('Error Handling', () => {
    it('should handle database errors gracefully', async () => {
      await FeatureTestUtils.testAdminFeature(async () => {
        mockAuth.auth.mockResolvedValue(mockAdminSession);
        mockUser.find.mockRejectedValue(new Error('Database connection failed'));

        const { GET } = await import('@/app/api/admin/users/route');
        
        const request = MockFactory.createRequest('http://localhost:3000/api/admin/users');

        const response = await GET(request);
        
        await TestAssertions.expectErrorWithContext(response);
        expect(response.status).toBe(500);
      });
    });

    it('should provide detailed error context in development', async () => {
      await FeatureTestUtils.testAdminFeature(async () => {
        mockAuth.auth.mockResolvedValue(mockAdminSession);
        mockUser.findByIdAndUpdate.mockRejectedValue(new Error('Validation failed'));

        const { PUT } = await import('@/app/api/admin/users/route');
        
        const request = MockFactory.createRequest('http://localhost:3000/api/admin/users', {
          method: 'PUT',
          body: {
            userId: 'invalid-id',
            updates: { verified: true },
          },
        });

        const response = await PUT(request);
        const data = await response.json();
        
        await TestAssertions.expectErrorWithContext(response);
        
        // In development mode, should include detailed context
        if (process.env.NODE_ENV === 'development') {
          expect(data.context).toBeDefined();
          expect(data.suggestions).toBeDefined();
        }
      });
    });
  });
});
