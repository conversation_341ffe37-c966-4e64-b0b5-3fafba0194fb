import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@/lib/auth';
import { File } from '@/models/File';
import { S3Service } from '@/lib/s3';
import connectDB from '@/lib/mongodb';

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ fileId: string }> }
) {
  try {
    const session = await auth();
    if (!session) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const { fileId } = await params;
    const url = new URL(request.url);
    const proxy = url.searchParams.get('proxy') === 'true';

    await connectDB();

    // Find file and verify ownership
    const file = await File.findOne({
      _id: fileId,
      userId: session.user.id,
    });

    if (!file) {
      return NextResponse.json(
        { error: 'File not found or access denied' },
        { status: 404 }
      );
    }

    // Get S3 service instance using the appropriate credentials for this file
    let s3Service: S3Service;

    try {
      s3Service = S3Service.getS3ServiceForFile(request, file, session);
    } catch (error) {
      console.error('S3 service configuration error:', error);
      return NextResponse.json(
        { error: 'Unable to access file storage. Please check your S3 configuration.' },
        { status: 500 }
      );
    }

    // If proxy=true, stream the file through our server
    if (proxy) {
      try {
        // Get the file content from S3
        const fileStream = await s3Service.getFileStream(file.s3Key);

        // Increment download count
        await file.incrementDownloadCount();

        // Set proper download headers
        const headers = new Headers();
        headers.set('Content-Type', file.mimeType || 'application/octet-stream');
        headers.set('Content-Disposition', `attachment; filename="${encodeURIComponent(file.originalName || file.name)}"`);
        headers.set('Content-Length', file.size.toString());
        headers.set('Cache-Control', 'no-cache');

        return new NextResponse(fileStream, {
          status: 200,
          headers,
        });
      } catch (error) {
        console.error('File streaming error:', error);
        return NextResponse.json(
          { error: 'Failed to download file' },
          { status: 500 }
        );
      }
    }

    // Default behavior: return presigned URL for client-side download
    const downloadUrl = await s3Service.getPresignedDownloadUrl(file.s3Key);

    // Increment download count
    await file.incrementDownloadCount();

    return NextResponse.json({
      downloadUrl,
      fileName: file.name,
      originalName: file.originalName,
      size: file.size,
      mimeType: file.mimeType,
    });

  } catch (error) {
    console.error('Download URL generation error:', error);
    return NextResponse.json(
      { error: 'Failed to generate download URL' },
      { status: 500 }
    );
  }
}
