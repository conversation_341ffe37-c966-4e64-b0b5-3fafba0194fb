import { NextRequest, NextResponse } from 'next/server';
import connectDB from '@/lib/mongodb';
import { User } from '@/models/User';

interface HealthCheck {
  status: 'healthy' | 'unhealthy' | 'degraded';
  timestamp: string;
  version: string;
  uptime: number;
  checks: {
    database: {
      status: 'up' | 'down';
      responseTime?: number;
      error?: string;
    };
    memory: {
      status: 'ok' | 'warning' | 'critical';
      usage: {
        used: number;
        total: number;
        percentage: number;
      };
    };
    disk: {
      status: 'ok' | 'warning' | 'critical';
      // In a real app, you'd check actual disk usage
    };
    external: {
      s3: {
        status: 'up' | 'down' | 'unknown';
        responseTime?: number;
      };
      email: {
        status: 'up' | 'down' | 'unknown';
      };
    };
  };
}

export async function GET(request: NextRequest) {
  const startTime = Date.now();
  
  try {
    // Initialize health check response
    const healthCheck: HealthCheck = {
      status: 'healthy',
      timestamp: new Date().toISOString(),
      version: process.env.npm_package_version || '1.0.0',
      uptime: process.uptime(),
      checks: {
        database: { status: 'down' },
        memory: { status: 'ok', usage: { used: 0, total: 0, percentage: 0 } },
        disk: { status: 'ok' },
        external: {
          s3: { status: 'unknown' },
          email: { status: 'unknown' },
        },
      },
    };

    // Check database connectivity
    try {
      const dbStartTime = Date.now();
      await connectDB();
      
      // Simple query to test database
      await User.countDocuments().limit(1);
      
      const dbResponseTime = Date.now() - dbStartTime;
      healthCheck.checks.database = {
        status: 'up',
        responseTime: dbResponseTime,
      };
    } catch (error) {
      healthCheck.checks.database = {
        status: 'down',
        error: error instanceof Error ? error.message : 'Database connection failed',
      };
      healthCheck.status = 'unhealthy';
    }

    // Check memory usage
    const memoryUsage = process.memoryUsage();
    const totalMemory = memoryUsage.heapTotal;
    const usedMemory = memoryUsage.heapUsed;
    const memoryPercentage = (usedMemory / totalMemory) * 100;

    healthCheck.checks.memory = {
      status: memoryPercentage > 90 ? 'critical' : memoryPercentage > 70 ? 'warning' : 'ok',
      usage: {
        used: usedMemory,
        total: totalMemory,
        percentage: Math.round(memoryPercentage * 100) / 100,
      },
    };

    if (healthCheck.checks.memory.status === 'critical') {
      healthCheck.status = 'unhealthy';
    } else if (healthCheck.checks.memory.status === 'warning' && healthCheck.status === 'healthy') {
      healthCheck.status = 'degraded';
    }

    // Check S3 connectivity (basic check)
    try {
      if (process.env.PLATFORM_S3_ENDPOINT && process.env.PLATFORM_S3_ACCESS_KEY_ID) {
        // In a real implementation, you'd make a simple S3 API call
        healthCheck.checks.external.s3.status = 'up';
      } else {
        healthCheck.checks.external.s3.status = 'unknown';
      }
    } catch (error) {
      healthCheck.checks.external.s3.status = 'down';
      if (healthCheck.status === 'healthy') {
        healthCheck.status = 'degraded';
      }
    }

    // Check email service
    try {
      if (process.env.EMAIL_SERVER_HOST && process.env.EMAIL_SERVER_USER) {
        // In a real implementation, you'd test SMTP connectivity
        healthCheck.checks.external.email.status = 'up';
      } else {
        healthCheck.checks.external.email.status = 'unknown';
      }
    } catch (error) {
      healthCheck.checks.external.email.status = 'down';
      if (healthCheck.status === 'healthy') {
        healthCheck.status = 'degraded';
      }
    }

    // Determine HTTP status code based on health
    const statusCode = healthCheck.status === 'healthy' ? 200 : 
                      healthCheck.status === 'degraded' ? 200 : 503;

    return NextResponse.json(healthCheck, { 
      status: statusCode,
      headers: {
        'Cache-Control': 'no-cache, no-store, must-revalidate',
        'Pragma': 'no-cache',
        'Expires': '0',
      },
    });

  } catch (error) {
    console.error('Health check error:', error);
    
    return NextResponse.json({
      status: 'unhealthy',
      timestamp: new Date().toISOString(),
      error: error instanceof Error ? error.message : 'Health check failed',
      uptime: process.uptime(),
    }, { 
      status: 503,
      headers: {
        'Cache-Control': 'no-cache, no-store, must-revalidate',
        'Pragma': 'no-cache',
        'Expires': '0',
      },
    });
  }
}

// Simple ping endpoint for basic availability checks
export async function HEAD(request: NextRequest) {
  return new NextResponse(null, { 
    status: 200,
    headers: {
      'Cache-Control': 'no-cache',
    },
  });
}
