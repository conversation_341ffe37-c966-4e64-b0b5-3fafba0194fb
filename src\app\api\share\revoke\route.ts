import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';
import { auth } from '@/lib/auth';
import { Share } from '@/models/Share';
import connectDB from '@/lib/mongodb';

const revokeShareSchema = z.object({
  shareId: z.string().min(1, 'Share ID is required'),
});

export async function POST(request: NextRequest) {
  try {
    const session = await auth();
    if (!session) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const body = await request.json();
    const { shareId } = revokeShareSchema.parse(body);

    await connectDB();

    // Find share and verify ownership
    const share = await Share.findOne({
      shareId,
      userId: session.user.id,
    });

    if (!share) {
      return NextResponse.json(
        { error: 'Share not found or access denied' },
        { status: 404 }
      );
    }

    // Deactivate share
    share.isActive = false;
    await share.save();

    return NextResponse.json({
      message: 'Share revoked successfully',
    });

  } catch (error) {
    console.error('Share revoke error:', error);
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Validation error', details: error.issues },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: 'Failed to revoke share' },
      { status: 500 }
    );
  }
}

export async function DELETE(request: NextRequest) {
  try {
    const session = await auth();
    if (!session) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const { searchParams } = new URL(request.url);
    const shareId = searchParams.get('shareId');

    if (!shareId) {
      return NextResponse.json(
        { error: 'Share ID is required' },
        { status: 400 }
      );
    }

    await connectDB();

    // Find share and verify ownership
    const share = await Share.findOne({
      shareId,
      userId: session.user.id,
    });

    if (!share) {
      return NextResponse.json(
        { error: 'Share not found or access denied' },
        { status: 404 }
      );
    }

    // Delete share permanently
    await Share.deleteOne({ _id: share._id });

    return NextResponse.json({
      message: 'Share deleted successfully',
    });

  } catch (error) {
    console.error('Share deletion error:', error);
    return NextResponse.json(
      { error: 'Failed to delete share' },
      { status: 500 }
    );
  }
}
